#!/usr/bin/env node

/**
 * Script para identificar e insertar datos de prueba en tablas vacías
 *
 * Este script:
 * 1. Identifica todas las tablas que no contienen registros (count = 0)
 * 2. Para cada tabla vacía encontrada:
 *    - Analiza su estructura (columnas, tipos de datos y restricciones)
 *    - Genera inserciones con datos de prueba realistas y coherentes
 *    - Respeta las relaciones entre tablas (foreign keys)
 *    - Incluye al menos 5 registros por tabla
 *
 * Autor: <PERSON>
 * Fecha: 2023
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import { execSync } from 'child_process';

// Obtener __dirname en ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Cargar variables de entorno
const rootEnvFile = path.join(__dirname, '../../.env');

if (fs.existsSync(rootEnvFile)) {
  console.log('Usando archivo .env en la raíz del proyecto');
  dotenv.config({ path: rootEnvFile });
} else {
  console.error('\x1b[31m%s\x1b[0m', '❌ Error: No se encontró el archivo .env en la raíz del proyecto');
  process.exit(1);
}

// Verificar que las variables de entorno necesarias están definidas
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('\x1b[31m%s\x1b[0m', '❌ Error: SUPABASE_URL o SUPABASE_SERVICE_KEY no están definidas en el archivo .env');
  process.exit(1);
}

// Crear cliente de Supabase
const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Función principal que ejecuta el script
 */
async function main() {
  console.log('🔍 Identificando tablas vacías...');

  // Generar consulta SQL para identificar e insertar datos en tablas vacías
  const sqlQuery = `
-- Script para identificar e insertar datos de prueba en tablas vacías
-- Autor: Javier Reinaldo Almarales
-- Fecha: 2023

-- Función para verificar si una tabla está vacía
CREATE OR REPLACE FUNCTION is_table_empty(table_name text) RETURNS boolean AS $$
DECLARE
  count_rows integer;
BEGIN
  EXECUTE 'SELECT COUNT(*) FROM ' || quote_ident(table_name) INTO count_rows;
  RETURN count_rows = 0;
END;
$$ LANGUAGE plpgsql;

-- Función para verificar si un valor existe en una columna
CREATE OR REPLACE FUNCTION value_exists(table_name text, column_name text, value text) RETURNS boolean AS $$
DECLARE
  count_rows integer;
BEGIN
  EXECUTE 'SELECT COUNT(*) FROM ' || quote_ident(table_name) ||
          ' WHERE ' || quote_ident(column_name) || ' = ' || quote_literal(value) INTO count_rows;
  RETURN count_rows > 0;
END;
$$ LANGUAGE plpgsql;

-- 1. INSERTAR DATOS EN TABLAS BÁSICAS (SIN DEPENDENCIAS EXTERNAS)

-- Insertar datos en sources si está vacía
DO $$
BEGIN
  IF is_table_empty('sources') THEN
    INSERT INTO sources (name, url, api_endpoint, is_active, update_frequency, selectors)
    VALUES
      ('Amazon', 'https://www.amazon.com', 'https://api.amazon.com/products', TRUE, 'daily', '{"product": ".product-container", "price": ".price-class", "title": ".product-title"}'),
      ('Best Buy', 'https://www.bestbuy.com', 'https://api.bestbuy.com/v1/products', TRUE, 'daily', '{"product": ".product-item", "price": ".price-block", "title": ".product-name"}'),
      ('Newegg', 'https://www.newegg.com', 'https://api.newegg.com/products', TRUE, 'weekly', '{"product": ".item-container", "price": ".price-current", "title": ".item-title"}'),
      ('Walmart', 'https://www.walmart.com', 'https://api.walmart.com/v3/items', TRUE, 'daily', '{"product": ".product-card", "price": ".price-main", "title": ".product-title-link"}'),
      ('B&H Photo', 'https://www.bhphotovideo.com', 'https://api.bhphotovideo.com/products', TRUE, 'weekly', '{"product": ".product-item", "price": ".price", "title": ".product-title"}');

    RAISE NOTICE 'Datos insertados en la tabla sources';
  END IF;
END $$;

-- Insertar datos en operating_systems si está vacía o faltan registros
DO $$
DECLARE
  os_name text;
  os_version text;
BEGIN
  -- Verificar si la tabla tiene pocos registros (menos de 3)
  IF (SELECT COUNT(*) FROM operating_systems) < 3 THEN
    -- Windows 11
    IF NOT value_exists('operating_systems', 'name', 'Windows 11') THEN
      INSERT INTO operating_systems (name, version, is_linux, is_windows, is_macos, min_ram_gb, min_storage_gb)
      VALUES ('Windows 11', 'Home/Pro', FALSE, TRUE, FALSE, 8, 64);
      RAISE NOTICE 'Insertado Windows 11';
    END IF;

    -- Windows 10
    IF NOT value_exists('operating_systems', 'name', 'Windows 10') THEN
      INSERT INTO operating_systems (name, version, is_linux, is_windows, is_macos, min_ram_gb, min_storage_gb)
      VALUES ('Windows 10', 'Home/Pro', FALSE, TRUE, FALSE, 4, 32);
      RAISE NOTICE 'Insertado Windows 10';
    END IF;

    -- macOS Sonoma
    IF NOT value_exists('operating_systems', 'name', 'macOS Sonoma') THEN
      INSERT INTO operating_systems (name, version, is_linux, is_windows, is_macos, min_ram_gb, min_storage_gb)
      VALUES ('macOS Sonoma', '14', FALSE, FALSE, TRUE, 8, 64);
      RAISE NOTICE 'Insertado macOS Sonoma';
    END IF;

    -- macOS Ventura
    IF NOT value_exists('operating_systems', 'name', 'macOS Ventura') THEN
      INSERT INTO operating_systems (name, version, is_linux, is_windows, is_macos, min_ram_gb, min_storage_gb)
      VALUES ('macOS Ventura', '13', FALSE, FALSE, TRUE, 8, 64);
      RAISE NOTICE 'Insertado macOS Ventura';
    END IF;

    -- Ubuntu
    IF NOT value_exists('operating_systems', 'name', 'Ubuntu') THEN
      INSERT INTO operating_systems (name, version, is_linux, is_windows, is_macos, min_ram_gb, min_storage_gb)
      VALUES ('Ubuntu', '22.04 LTS', TRUE, FALSE, FALSE, 4, 25);
      RAISE NOTICE 'Insertado Ubuntu';
    END IF;

    -- Fedora
    IF NOT value_exists('operating_systems', 'name', 'Fedora') THEN
      INSERT INTO operating_systems (name, version, is_linux, is_windows, is_macos, min_ram_gb, min_storage_gb)
      VALUES ('Fedora', '38', TRUE, FALSE, FALSE, 4, 20);
      RAISE NOTICE 'Insertado Fedora';
    END IF;

    -- Debian
    IF NOT value_exists('operating_systems', 'name', 'Debian') THEN
      INSERT INTO operating_systems (name, version, is_linux, is_windows, is_macos, min_ram_gb, min_storage_gb)
      VALUES ('Debian', '12', TRUE, FALSE, FALSE, 2, 15);
      RAISE NOTICE 'Insertado Debian';
    END IF;

    RAISE NOTICE 'Datos insertados en la tabla operating_systems';
  END IF;
END $$;

-- Insertar datos en tag_categories
-- Nota: Esta inserción ahora se maneja en el bloque de tags para garantizar la consistencia de IDs

-- Insertar datos en tags si está vacía
DO $$
DECLARE
  uso_id INT;
  rendimiento_id INT;
  portabilidad_id INT;
  pantalla_id INT;
  bateria_id INT;
  llm_id INT;
BEGIN
  -- Primero verificamos que tag_categories tenga datos
  IF is_table_empty('tag_categories') THEN
    -- Insertar categorías y obtener sus IDs
    INSERT INTO tag_categories (name, description) VALUES ('Uso', 'Categorías según el uso principal del equipo') RETURNING id INTO uso_id;
    INSERT INTO tag_categories (name, description) VALUES ('Rendimiento', 'Categorías según el nivel de rendimiento') RETURNING id INTO rendimiento_id;
    INSERT INTO tag_categories (name, description) VALUES ('Portabilidad', 'Categorías según la portabilidad') RETURNING id INTO portabilidad_id;
    INSERT INTO tag_categories (name, description) VALUES ('Pantalla', 'Categorías según el tipo y calidad de pantalla') RETURNING id INTO pantalla_id;
    INSERT INTO tag_categories (name, description) VALUES ('Batería', 'Categorías según la duración de la batería') RETURNING id INTO bateria_id;
    INSERT INTO tag_categories (name, description) VALUES ('Compatibilidad LLM', 'Categorías según la compatibilidad con modelos LLM') RETURNING id INTO llm_id;

    RAISE NOTICE 'Datos insertados en la tabla tag_categories con IDs: %, %, %, %, %, %', uso_id, rendimiento_id, portabilidad_id, pantalla_id, bateria_id, llm_id;
  ELSE
    -- Obtener los IDs existentes
    SELECT id INTO uso_id FROM tag_categories WHERE name = 'Uso' LIMIT 1;
    SELECT id INTO rendimiento_id FROM tag_categories WHERE name = 'Rendimiento' LIMIT 1;
    SELECT id INTO portabilidad_id FROM tag_categories WHERE name = 'Portabilidad' LIMIT 1;
    SELECT id INTO pantalla_id FROM tag_categories WHERE name = 'Pantalla' LIMIT 1;
    SELECT id INTO bateria_id FROM tag_categories WHERE name = 'Batería' LIMIT 1;
    SELECT id INTO llm_id FROM tag_categories WHERE name = 'Compatibilidad LLM' LIMIT 1;

    -- Si no existen, insertarlos
    IF uso_id IS NULL THEN
      INSERT INTO tag_categories (name, description) VALUES ('Uso', 'Categorías según el uso principal del equipo') RETURNING id INTO uso_id;
    END IF;

    IF rendimiento_id IS NULL THEN
      INSERT INTO tag_categories (name, description) VALUES ('Rendimiento', 'Categorías según el nivel de rendimiento') RETURNING id INTO rendimiento_id;
    END IF;

    IF portabilidad_id IS NULL THEN
      INSERT INTO tag_categories (name, description) VALUES ('Portabilidad', 'Categorías según la portabilidad') RETURNING id INTO portabilidad_id;
    END IF;

    IF pantalla_id IS NULL THEN
      INSERT INTO tag_categories (name, description) VALUES ('Pantalla', 'Categorías según el tipo y calidad de pantalla') RETURNING id INTO pantalla_id;
    END IF;

    IF bateria_id IS NULL THEN
      INSERT INTO tag_categories (name, description) VALUES ('Batería', 'Categorías según la duración de la batería') RETURNING id INTO bateria_id;
    END IF;

    IF llm_id IS NULL THEN
      INSERT INTO tag_categories (name, description) VALUES ('Compatibilidad LLM', 'Categorías según la compatibilidad con modelos LLM') RETURNING id INTO llm_id;
    END IF;
  END IF;

  -- Ahora insertamos los tags usando los IDs obtenidos
  IF is_table_empty('tags') THEN
    INSERT INTO tags (name, category_id, description)
    VALUES
      ('Gaming', uso_id, 'Laptops optimizadas para juegos'),
      ('Productividad', uso_id, 'Laptops para trabajo y productividad'),
      ('Creación de contenido', uso_id, 'Laptops para diseño, edición de video, etc.'),
      ('Desarrollo', uso_id, 'Laptops para programación y desarrollo'),
      ('Estudiante', uso_id, 'Laptops económicas para estudiantes'),
      ('Alto rendimiento', rendimiento_id, 'Laptops con componentes de gama alta'),
      ('Gama media', rendimiento_id, 'Laptops con componentes de gama media'),
      ('Gama baja', rendimiento_id, 'Laptops con componentes básicos'),
      ('Ultraportátil', portabilidad_id, 'Laptops extremadamente ligeras y delgadas'),
      ('Larga duración', bateria_id, 'Laptops con batería de larga duración'),
      ('Compatible con LLMs grandes', llm_id, 'Puede ejecutar modelos LLM de gran tamaño'),
      ('Compatible con LLMs pequeños', llm_id, 'Puede ejecutar modelos LLM pequeños');

    RAISE NOTICE 'Datos insertados en la tabla tags';
  END IF;
END $$;

-- 2. INSERTAR DATOS EN TABLAS DE COMPONENTES

-- Insertar datos en cpus si está vacía
DO $$
BEGIN
  IF is_table_empty('cpus') THEN
    INSERT INTO cpus (manufacturer_id, model, generation, cores, threads, base_clock_ghz, boost_clock_ghz, tdp_watts, cache_mb, architecture_id, supports_avx512)
    VALUES
      (1, 'Core i7', '13700H', 14, 20, 3.70, 5.00, 45, 24, 1, TRUE),
      (1, 'Core i5', '13500H', 12, 16, 3.50, 4.70, 45, 18, 1, TRUE),
      (1, 'Core i9', '13900HX', 24, 32, 3.90, 5.40, 55, 36, 1, TRUE),
      (2, 'Ryzen 7', '7840HS', 8, 16, 3.80, 5.10, 45, 16, 2, FALSE),
      (2, 'Ryzen 9', '7940HS', 8, 16, 4.00, 5.20, 45, 16, 2, FALSE),
      (3, 'M2 Pro', '', 10, 10, 3.50, 4.00, 30, 24, 3, FALSE),
      (3, 'M2 Max', '', 12, 12, 3.70, 4.20, 40, 32, 3, FALSE);

    RAISE NOTICE 'Datos insertados en la tabla cpus';
  END IF;
END $$;

-- Insertar datos en gpus si está vacía
DO $$
BEGIN
  IF is_table_empty('gpus') THEN
    INSERT INTO gpus (manufacturer_id, model, vram_gb, memory_type_id, base_clock_mhz, boost_clock_mhz, tdp_watts, ray_tracing_support, tensor_cores, cuda_cores, compute_units, supports_dlss, supports_fsr)
    VALUES
      (4, 'GeForce RTX 4070', 8, 3, 1920, 2175, 115, TRUE, 56, 5888, NULL, TRUE, FALSE),
      (4, 'GeForce RTX 4060', 8, 3, 1830, 2070, 85, TRUE, 36, 3072, NULL, TRUE, FALSE),
      (4, 'GeForce RTX 4080', 12, 3, 2205, 2505, 150, TRUE, 76, 9728, NULL, TRUE, FALSE),
      (2, 'Radeon RX 7700S', 10, 3, 1900, 2200, 100, TRUE, NULL, NULL, 36, FALSE, TRUE),
      (2, 'Radeon RX 7600S', 8, 3, 1865, 2050, 75, TRUE, NULL, NULL, 28, FALSE, TRUE),
      (3, 'M2 Pro', 16, 4, 1398, NULL, NULL, FALSE, NULL, NULL, 16, FALSE, FALSE),
      (3, 'M2 Max', 32, 4, 1398, NULL, NULL, FALSE, NULL, NULL, 30, FALSE, FALSE);

    RAISE NOTICE 'Datos insertados en la tabla gpus';
  END IF;
END $$;

-- Insertar datos en ram_configurations si está vacía
DO $$
BEGIN
  IF is_table_empty('ram_configurations') THEN
    INSERT INTO ram_configurations (memory_type_id, size_gb, speed_mhz, manufacturer_id, is_dual_channel, cas_latency)
    VALUES
      (1, 16, 4800, 5, TRUE, 40),
      (1, 32, 5200, 5, TRUE, 42),
      (1, 64, 5600, 6, TRUE, 40),
      (2, 16, 3200, 7, TRUE, 22),
      (2, 32, 3600, 7, TRUE, 18),
      (3, 16, 6400, 8, TRUE, 32),
      (3, 32, 6400, 8, TRUE, 32);

    RAISE NOTICE 'Datos insertados en la tabla ram_configurations';
  END IF;
END $$;

-- Insertar datos en storage_devices si está vacía
DO $$
BEGIN
  IF is_table_empty('storage_devices') THEN
    INSERT INTO storage_devices (interface_id, capacity_gb, manufacturer_id, read_speed_mbps, write_speed_mbps, is_nvme, has_dram_cache)
    VALUES
      (1, 512, 9, 3500, 2300, TRUE, TRUE),
      (1, 1024, 9, 3500, 2800, TRUE, TRUE),
      (1, 2048, 10, 7000, 5000, TRUE, TRUE),
      (1, 512, 11, 5000, 2500, TRUE, TRUE),
      (1, 1024, 11, 5000, 3000, TRUE, TRUE),
      (2, 512, 12, 560, 530, FALSE, FALSE),
      (2, 1024, 12, 560, 530, FALSE, FALSE);

    RAISE NOTICE 'Datos insertados en la tabla storage_devices';
  END IF;
END $$;

-- 3. INSERTAR DATOS EN TABLAS DE RELACIÓN

-- Insertar datos en laptop_cpus si está vacía
DO $$
DECLARE
  core_i7_id INT;
  m2_pro_id INT;
  ryzen7_id INT;
  laptop1_exists BOOLEAN;
  laptop2_exists BOOLEAN;
  laptop3_exists BOOLEAN;
BEGIN
  -- Verificar si las laptops existen
  SELECT EXISTS(SELECT 1 FROM laptops WHERE id = 1) INTO laptop1_exists;
  SELECT EXISTS(SELECT 1 FROM laptops WHERE id = 2) INTO laptop2_exists;
  SELECT EXISTS(SELECT 1 FROM laptops WHERE id = 3) INTO laptop3_exists;

  -- Obtener IDs de los CPUs insertados
  SELECT id INTO core_i7_id FROM cpus WHERE model = 'Core i7' LIMIT 1;
  SELECT id INTO m2_pro_id FROM cpus WHERE model = 'M2 Pro' LIMIT 1;
  SELECT id INTO ryzen7_id FROM cpus WHERE model = 'Ryzen 7' LIMIT 1;

  -- Verificar si tenemos los IDs necesarios
  IF core_i7_id IS NULL OR m2_pro_id IS NULL OR ryzen7_id IS NULL THEN
    RAISE NOTICE 'No se encontraron todos los CPUs necesarios. Insertando CPUs...';

    -- Insertar CPUs si no existen
    IF core_i7_id IS NULL THEN
      INSERT INTO cpus (manufacturer_id, model, generation, cores, threads, base_clock_ghz, boost_clock_ghz, tdp_watts, cache_mb, architecture_id, supports_avx512)
      VALUES (1, 'Core i7', '13700H', 14, 20, 3.70, 5.00, 45, 24, 1, TRUE)
      RETURNING id INTO core_i7_id;
      RAISE NOTICE 'Insertado CPU Core i7 con ID %', core_i7_id;
    END IF;

    IF m2_pro_id IS NULL THEN
      INSERT INTO cpus (manufacturer_id, model, generation, cores, threads, base_clock_ghz, boost_clock_ghz, tdp_watts, cache_mb, architecture_id, supports_avx512)
      VALUES (3, 'M2 Pro', '', 10, 10, 3.50, 4.00, 30, 24, 3, FALSE)
      RETURNING id INTO m2_pro_id;
      RAISE NOTICE 'Insertado CPU M2 Pro con ID %', m2_pro_id;
    END IF;

    IF ryzen7_id IS NULL THEN
      INSERT INTO cpus (manufacturer_id, model, generation, cores, threads, base_clock_ghz, boost_clock_ghz, tdp_watts, cache_mb, architecture_id, supports_avx512)
      VALUES (2, 'Ryzen 7', '7840HS', 8, 16, 3.80, 5.10, 45, 16, 2, FALSE)
      RETURNING id INTO ryzen7_id;
      RAISE NOTICE 'Insertado CPU Ryzen 7 con ID %', ryzen7_id;
    END IF;
  END IF;

  -- Insertar en laptop_cpus si está vacía y tenemos las laptops y CPUs necesarios
  IF is_table_empty('laptop_cpus') AND laptop1_exists AND laptop2_exists AND laptop3_exists AND
     core_i7_id IS NOT NULL AND m2_pro_id IS NOT NULL AND ryzen7_id IS NOT NULL THEN

    INSERT INTO laptop_cpus (laptop_id, cpu_id, performance_score)
    VALUES
      (1, core_i7_id, 85),
      (2, m2_pro_id, 90),
      (3, ryzen7_id, 80);

    RAISE NOTICE 'Datos insertados en la tabla laptop_cpus';
  ELSE
    RAISE NOTICE 'No se insertaron datos en laptop_cpus: vacía=%, laptop1=%, laptop2=%, laptop3=%, core_i7=%, m2_pro=%, ryzen7=%',
      is_table_empty('laptop_cpus'), laptop1_exists, laptop2_exists, laptop3_exists, core_i7_id, m2_pro_id, ryzen7_id;
  END IF;
END $$;

-- Insertar datos en laptop_gpus si está vacía
DO $$
DECLARE
  rtx4060_id INT;
  m2pro_gpu_id INT;
  radeon_id INT;
  laptop1_exists BOOLEAN;
  laptop2_exists BOOLEAN;
  laptop3_exists BOOLEAN;
BEGIN
  -- Verificar si las laptops existen
  SELECT EXISTS(SELECT 1 FROM laptops WHERE id = 1) INTO laptop1_exists;
  SELECT EXISTS(SELECT 1 FROM laptops WHERE id = 2) INTO laptop2_exists;
  SELECT EXISTS(SELECT 1 FROM laptops WHERE id = 3) INTO laptop3_exists;

  -- Obtener IDs de las GPUs insertadas
  SELECT id INTO rtx4060_id FROM gpus WHERE model = 'GeForce RTX 4060' LIMIT 1;
  SELECT id INTO m2pro_gpu_id FROM gpus WHERE model = 'M2 Pro' LIMIT 1;
  SELECT id INTO radeon_id FROM gpus WHERE model = 'Radeon RX 7700S' LIMIT 1;

  -- Verificar si tenemos los IDs necesarios
  IF rtx4060_id IS NULL OR m2pro_gpu_id IS NULL OR radeon_id IS NULL THEN
    RAISE NOTICE 'No se encontraron todas las GPUs necesarias. Insertando GPUs...';

    -- Insertar GPUs si no existen
    IF rtx4060_id IS NULL THEN
      INSERT INTO gpus (manufacturer_id, model, vram_gb, memory_type_id, base_clock_mhz, boost_clock_mhz, tdp_watts, ray_tracing_support, tensor_cores, cuda_cores, compute_units, supports_dlss, supports_fsr)
      VALUES (4, 'GeForce RTX 4060', 8, 3, 1830, 2070, 85, TRUE, 36, 3072, NULL, TRUE, FALSE)
      RETURNING id INTO rtx4060_id;
      RAISE NOTICE 'Insertada GPU GeForce RTX 4060 con ID %', rtx4060_id;
    END IF;

    IF m2pro_gpu_id IS NULL THEN
      INSERT INTO gpus (manufacturer_id, model, vram_gb, memory_type_id, base_clock_mhz, boost_clock_mhz, tdp_watts, ray_tracing_support, tensor_cores, cuda_cores, compute_units, supports_dlss, supports_fsr)
      VALUES (3, 'M2 Pro', 16, 4, 1398, NULL, NULL, FALSE, NULL, NULL, 16, FALSE, FALSE)
      RETURNING id INTO m2pro_gpu_id;
      RAISE NOTICE 'Insertada GPU M2 Pro con ID %', m2pro_gpu_id;
    END IF;

    IF radeon_id IS NULL THEN
      INSERT INTO gpus (manufacturer_id, model, vram_gb, memory_type_id, base_clock_mhz, boost_clock_mhz, tdp_watts, ray_tracing_support, tensor_cores, cuda_cores, compute_units, supports_dlss, supports_fsr)
      VALUES (2, 'Radeon RX 7700S', 10, 3, 1900, 2200, 100, TRUE, NULL, NULL, 36, FALSE, TRUE)
      RETURNING id INTO radeon_id;
      RAISE NOTICE 'Insertada GPU Radeon RX 7700S con ID %', radeon_id;
    END IF;
  END IF;

  -- Insertar en laptop_gpus si está vacía y tenemos las laptops y GPUs necesarias
  IF is_table_empty('laptop_gpus') AND laptop1_exists AND laptop2_exists AND laptop3_exists AND
     rtx4060_id IS NOT NULL AND m2pro_gpu_id IS NOT NULL AND radeon_id IS NOT NULL THEN

    INSERT INTO laptop_gpus (laptop_id, gpu_id, is_discrete, performance_score)
    VALUES
      (1, rtx4060_id, TRUE, 80),
      (2, m2pro_gpu_id, TRUE, 85),
      (3, radeon_id, TRUE, 75);

    RAISE NOTICE 'Datos insertados en la tabla laptop_gpus';
  ELSE
    RAISE NOTICE 'No se insertaron datos en laptop_gpus: vacía=%, laptop1=%, laptop2=%, laptop3=%, rtx4060=%, m2pro_gpu=%, radeon=%',
      is_table_empty('laptop_gpus'), laptop1_exists, laptop2_exists, laptop3_exists, rtx4060_id, m2pro_gpu_id, radeon_id;
  END IF;
END $$;

-- Insertar datos en laptop_ram si está vacía
DO $$
DECLARE
  ram_16gb_id INT;
  ram_32gb_id INT;
  laptop1_exists BOOLEAN;
  laptop2_exists BOOLEAN;
  laptop3_exists BOOLEAN;
BEGIN
  -- Verificar si las laptops existen
  SELECT EXISTS(SELECT 1 FROM laptops WHERE id = 1) INTO laptop1_exists;
  SELECT EXISTS(SELECT 1 FROM laptops WHERE id = 2) INTO laptop2_exists;
  SELECT EXISTS(SELECT 1 FROM laptops WHERE id = 3) INTO laptop3_exists;

  -- Obtener IDs de las configuraciones de RAM insertadas
  SELECT id INTO ram_16gb_id FROM ram_configurations WHERE size_gb = 16 LIMIT 1;
  SELECT id INTO ram_32gb_id FROM ram_configurations WHERE size_gb = 32 LIMIT 1;

  -- Verificar si tenemos los IDs necesarios
  IF ram_16gb_id IS NULL OR ram_32gb_id IS NULL THEN
    RAISE NOTICE 'No se encontraron todas las configuraciones de RAM necesarias. Insertando configuraciones...';

    -- Insertar configuraciones de RAM si no existen
    IF ram_16gb_id IS NULL THEN
      INSERT INTO ram_configurations (memory_type_id, size_gb, speed_mhz, manufacturer_id, is_dual_channel, cas_latency)
      VALUES (1, 16, 4800, 5, TRUE, 40)
      RETURNING id INTO ram_16gb_id;
      RAISE NOTICE 'Insertada configuración de RAM 16GB con ID %', ram_16gb_id;
    END IF;

    IF ram_32gb_id IS NULL THEN
      INSERT INTO ram_configurations (memory_type_id, size_gb, speed_mhz, manufacturer_id, is_dual_channel, cas_latency)
      VALUES (1, 32, 5200, 5, TRUE, 42)
      RETURNING id INTO ram_32gb_id;
      RAISE NOTICE 'Insertada configuración de RAM 32GB con ID %', ram_32gb_id;
    END IF;
  END IF;

  -- Insertar en laptop_ram si está vacía y tenemos las laptops y configuraciones de RAM necesarias
  IF is_table_empty('laptop_ram') AND laptop1_exists AND laptop2_exists AND laptop3_exists AND
     ram_16gb_id IS NOT NULL AND ram_32gb_id IS NOT NULL THEN

    INSERT INTO laptop_ram (laptop_id, ram_configuration_id, slots_used, expandable, max_supported_gb)
    VALUES
      (1, ram_16gb_id, 2, TRUE, 64),
      (2, ram_32gb_id, 1, FALSE, 32),
      (3, ram_16gb_id, 2, TRUE, 64);

    RAISE NOTICE 'Datos insertados en la tabla laptop_ram';
  ELSE
    RAISE NOTICE 'No se insertaron datos en laptop_ram: vacía=%, laptop1=%, laptop2=%, laptop3=%, ram_16gb=%, ram_32gb=%',
      is_table_empty('laptop_ram'), laptop1_exists, laptop2_exists, laptop3_exists, ram_16gb_id, ram_32gb_id;
  END IF;
END $$;

-- Insertar datos en laptop_storage si está vacía
DO $$
DECLARE
  storage_512gb_id INT;
  storage_1tb_id INT;
  storage_2tb_id INT;
  laptop1_exists BOOLEAN;
  laptop2_exists BOOLEAN;
  laptop3_exists BOOLEAN;
BEGIN
  -- Verificar si las laptops existen
  SELECT EXISTS(SELECT 1 FROM laptops WHERE id = 1) INTO laptop1_exists;
  SELECT EXISTS(SELECT 1 FROM laptops WHERE id = 2) INTO laptop2_exists;
  SELECT EXISTS(SELECT 1 FROM laptops WHERE id = 3) INTO laptop3_exists;

  -- Obtener IDs de los dispositivos de almacenamiento insertados
  SELECT id INTO storage_512gb_id FROM storage_devices WHERE capacity_gb = 512 LIMIT 1;
  SELECT id INTO storage_1tb_id FROM storage_devices WHERE capacity_gb = 1024 LIMIT 1;
  SELECT id INTO storage_2tb_id FROM storage_devices WHERE capacity_gb = 2048 LIMIT 1;

  -- Verificar si tenemos los IDs necesarios
  IF storage_1tb_id IS NULL OR storage_2tb_id IS NULL THEN
    RAISE NOTICE 'No se encontraron todos los dispositivos de almacenamiento necesarios. Insertando dispositivos...';

    -- Insertar dispositivos de almacenamiento si no existen
    IF storage_1tb_id IS NULL THEN
      INSERT INTO storage_devices (interface_id, capacity_gb, manufacturer_id, read_speed_mbps, write_speed_mbps, is_nvme, has_dram_cache)
      VALUES (1, 1024, 9, 3500, 2800, TRUE, TRUE)
      RETURNING id INTO storage_1tb_id;
      RAISE NOTICE 'Insertado dispositivo de almacenamiento 1TB con ID %', storage_1tb_id;
    END IF;

    IF storage_2tb_id IS NULL THEN
      INSERT INTO storage_devices (interface_id, capacity_gb, manufacturer_id, read_speed_mbps, write_speed_mbps, is_nvme, has_dram_cache)
      VALUES (1, 2048, 10, 7000, 5000, TRUE, TRUE)
      RETURNING id INTO storage_2tb_id;
      RAISE NOTICE 'Insertado dispositivo de almacenamiento 2TB con ID %', storage_2tb_id;
    END IF;
  END IF;

  -- Insertar en laptop_storage si está vacía y tenemos las laptops y dispositivos de almacenamiento necesarios
  IF is_table_empty('laptop_storage') AND laptop1_exists AND laptop2_exists AND laptop3_exists AND
     storage_1tb_id IS NOT NULL AND storage_2tb_id IS NOT NULL THEN

    INSERT INTO laptop_storage (laptop_id, storage_id, is_primary, slot_type)
    VALUES
      (1, storage_1tb_id, TRUE, 'M.2 NVMe'),
      (2, storage_2tb_id, TRUE, 'M.2 NVMe'),
      (3, storage_1tb_id, TRUE, 'M.2 NVMe');

    RAISE NOTICE 'Datos insertados en la tabla laptop_storage';
  ELSE
    RAISE NOTICE 'No se insertaron datos en laptop_storage: vacía=%, laptop1=%, laptop2=%, laptop3=%, storage_1tb=%, storage_2tb=%',
      is_table_empty('laptop_storage'), laptop1_exists, laptop2_exists, laptop3_exists, storage_1tb_id, storage_2tb_id;
  END IF;
END $$;

-- Insertar datos en laptop_os_compatibility si está vacía
DO $$
DECLARE
  windows11_id INT;
  windows10_id INT;
  macos_sonoma_id INT;
  macos_ventura_id INT;
  ubuntu_id INT;
  laptop1_exists BOOLEAN;
  laptop2_exists BOOLEAN;
  laptop3_exists BOOLEAN;
BEGIN
  -- Verificar si las laptops existen
  SELECT EXISTS(SELECT 1 FROM laptops WHERE id = 1) INTO laptop1_exists;
  SELECT EXISTS(SELECT 1 FROM laptops WHERE id = 2) INTO laptop2_exists;
  SELECT EXISTS(SELECT 1 FROM laptops WHERE id = 3) INTO laptop3_exists;

  -- Obtener IDs de los sistemas operativos
  SELECT id INTO windows11_id FROM operating_systems WHERE name = 'Windows 11' LIMIT 1;
  SELECT id INTO windows10_id FROM operating_systems WHERE name = 'Windows 10' LIMIT 1;
  SELECT id INTO macos_sonoma_id FROM operating_systems WHERE name = 'macOS Sonoma' LIMIT 1;
  SELECT id INTO macos_ventura_id FROM operating_systems WHERE name = 'macOS Ventura' LIMIT 1;
  SELECT id INTO ubuntu_id FROM operating_systems WHERE name = 'Ubuntu' LIMIT 1;

  RAISE NOTICE 'IDs de sistemas operativos: Windows 11=%, Windows 10=%, macOS Sonoma=%, macOS Ventura=%, Ubuntu=%',
    windows11_id, windows10_id, macos_sonoma_id, macos_ventura_id, ubuntu_id;

  -- Insertar en laptop_os_compatibility si está vacía y tenemos las laptops y sistemas operativos necesarios
  IF is_table_empty('laptop_os_compatibility') AND laptop1_exists AND laptop2_exists AND laptop3_exists AND
     windows11_id IS NOT NULL AND windows10_id IS NOT NULL AND macos_sonoma_id IS NOT NULL AND
     macos_ventura_id IS NOT NULL AND ubuntu_id IS NOT NULL THEN

    INSERT INTO laptop_os_compatibility (laptop_id, os_id, is_officially_supported, has_driver_issues, notes)
    VALUES
      (1, windows11_id, TRUE, FALSE, 'Funciona perfectamente con Windows 11'),
      (1, windows10_id, TRUE, FALSE, 'Compatible con Windows 10'),
      (1, ubuntu_id, TRUE, TRUE, 'Algunos problemas con el trackpad en Ubuntu'),
      (2, macos_sonoma_id, TRUE, FALSE, 'Sistema operativo nativo'),
      (2, macos_ventura_id, TRUE, FALSE, 'Compatible con versiones anteriores'),
      (3, windows11_id, TRUE, FALSE, 'Funciona bien con Windows 11'),
      (3, ubuntu_id, TRUE, FALSE, 'Buen soporte para Ubuntu');

    RAISE NOTICE 'Datos insertados en la tabla laptop_os_compatibility';
  ELSE
    RAISE NOTICE 'No se insertaron datos en laptop_os_compatibility: vacía=%, laptop1=%, laptop2=%, laptop3=%, win11=%, win10=%, macOS_sonoma=%, macOS_ventura=%, ubuntu=%',
      is_table_empty('laptop_os_compatibility'), laptop1_exists, laptop2_exists, laptop3_exists,
      windows11_id, windows10_id, macos_sonoma_id, macos_ventura_id, ubuntu_id;
  END IF;
END $$;

-- Insertar datos en laptop_tags si está vacía
DO $$
DECLARE
  gaming_id INT;
  productividad_id INT;
  creacion_id INT;
  alto_rendimiento_id INT;
  gama_media_id INT;
  ultraportatil_id INT;
  larga_duracion_id INT;
  llm_grandes_id INT;
  laptop1_exists BOOLEAN;
  laptop2_exists BOOLEAN;
  laptop3_exists BOOLEAN;
BEGIN
  -- Verificar si las laptops existen
  SELECT EXISTS(SELECT 1 FROM laptops WHERE id = 1) INTO laptop1_exists;
  SELECT EXISTS(SELECT 1 FROM laptops WHERE id = 2) INTO laptop2_exists;
  SELECT EXISTS(SELECT 1 FROM laptops WHERE id = 3) INTO laptop3_exists;

  -- Obtener IDs de los tags
  SELECT id INTO gaming_id FROM tags WHERE name = 'Gaming' LIMIT 1;
  SELECT id INTO productividad_id FROM tags WHERE name = 'Productividad' LIMIT 1;
  SELECT id INTO creacion_id FROM tags WHERE name = 'Creación de contenido' LIMIT 1;
  SELECT id INTO alto_rendimiento_id FROM tags WHERE name = 'Alto rendimiento' LIMIT 1;
  SELECT id INTO gama_media_id FROM tags WHERE name = 'Gama media' LIMIT 1;
  SELECT id INTO ultraportatil_id FROM tags WHERE name = 'Ultraportátil' LIMIT 1;
  SELECT id INTO larga_duracion_id FROM tags WHERE name = 'Larga duración' LIMIT 1;
  SELECT id INTO llm_grandes_id FROM tags WHERE name = 'Compatible con LLMs grandes' LIMIT 1;

  RAISE NOTICE 'IDs de tags: Gaming=%, Productividad=%, Creación=%, Alto rendimiento=%, Gama media=%, Ultraportátil=%, Larga duración=%, LLMs grandes=%',
    gaming_id, productividad_id, creacion_id, alto_rendimiento_id, gama_media_id, ultraportatil_id, larga_duracion_id, llm_grandes_id;

  -- Insertar en laptop_tags si está vacía y tenemos las laptops y tags necesarios
  IF is_table_empty('laptop_tags') AND laptop1_exists AND laptop2_exists AND laptop3_exists AND
     gaming_id IS NOT NULL AND productividad_id IS NOT NULL AND creacion_id IS NOT NULL AND
     alto_rendimiento_id IS NOT NULL AND gama_media_id IS NOT NULL AND ultraportatil_id IS NOT NULL AND
     larga_duracion_id IS NOT NULL AND llm_grandes_id IS NOT NULL THEN

    INSERT INTO laptop_tags (laptop_id, tag_id, relevance_score)
    VALUES
      (1, gaming_id, 90),
      (1, alto_rendimiento_id, 85),
      (1, llm_grandes_id, 80),
      (2, creacion_id, 95),
      (2, alto_rendimiento_id, 90),
      (2, ultraportatil_id, 85),
      (3, productividad_id, 90),
      (3, gama_media_id, 85),
      (3, larga_duracion_id, 95);

    RAISE NOTICE 'Datos insertados en la tabla laptop_tags';
  ELSE
    RAISE NOTICE 'No se insertaron datos en laptop_tags: vacía=%, laptop1=%, laptop2=%, laptop3=%, gaming=%, productividad=%, creacion=%, alto_rendimiento=%, gama_media=%, ultraportatil=%, larga_duracion=%, llm_grandes=%',
      is_table_empty('laptop_tags'), laptop1_exists, laptop2_exists, laptop3_exists,
      gaming_id, productividad_id, creacion_id, alto_rendimiento_id, gama_media_id, ultraportatil_id, larga_duracion_id, llm_grandes_id;
  END IF;
END $$;

-- Insertar datos en laptop_listings si está vacía
DO $$
DECLARE
  amazon_id INT;
  bestbuy_id INT;
  newegg_id INT;
  walmart_id INT;
  laptop1_exists BOOLEAN;
  laptop2_exists BOOLEAN;
  laptop3_exists BOOLEAN;
BEGIN
  -- Verificar si las laptops existen
  SELECT EXISTS(SELECT 1 FROM laptops WHERE id = 1) INTO laptop1_exists;
  SELECT EXISTS(SELECT 1 FROM laptops WHERE id = 2) INTO laptop2_exists;
  SELECT EXISTS(SELECT 1 FROM laptops WHERE id = 3) INTO laptop3_exists;

  -- Obtener IDs de las fuentes
  SELECT id INTO amazon_id FROM sources WHERE name = 'Amazon' LIMIT 1;
  SELECT id INTO bestbuy_id FROM sources WHERE name = 'Best Buy' LIMIT 1;
  SELECT id INTO newegg_id FROM sources WHERE name = 'Newegg' LIMIT 1;
  SELECT id INTO walmart_id FROM sources WHERE name = 'Walmart' LIMIT 1;

  RAISE NOTICE 'IDs de fuentes: Amazon=%, Best Buy=%, Newegg=%, Walmart=%',
    amazon_id, bestbuy_id, newegg_id, walmart_id;

  -- Insertar en laptop_listings si está vacía y tenemos las laptops y fuentes necesarias
  IF is_table_empty('laptop_listings') AND laptop1_exists AND laptop2_exists AND laptop3_exists AND
     amazon_id IS NOT NULL AND bestbuy_id IS NOT NULL AND newegg_id IS NOT NULL AND walmart_id IS NOT NULL THEN

    INSERT INTO laptop_listings (laptop_id, source_id, price, url, in_stock, shipping_cost, rating, reviews_count, free_shipping, estimated_delivery_days, has_warranty, warranty_months, name, brand, source_url, specs)
    VALUES
      (1, amazon_id, 1299.99, 'https://www.amazon.com/laptop1', TRUE, 0.00, 4.5, 120, TRUE, 2, TRUE, 12, 'ThinkPad X1 Carbon Gen 10', 'Lenovo', 'https://www.amazon.com', '{"cpu":{"name":"Intel Core i7-13700H","cores":14,"threads":20,"speed":"3.7 GHz"},"gpu":{"name":"Intel Iris Xe","memory":"Shared"},"ram":{"size":"16GB","type":"LPDDR5","speed":"5200MHz"},"storage":{"size":"1TB","type":"NVMe SSD"},"display":{"size":"14 inch","resolution":"2880x1800","type":"OLED"}}'),
      (1, bestbuy_id, 1349.99, 'https://www.bestbuy.com/laptop1', TRUE, 0.00, 4.3, 85, TRUE, 1, TRUE, 12, 'ThinkPad X1 Carbon Gen 10', 'Lenovo', 'https://www.bestbuy.com', '{"cpu":{"name":"Intel Core i7-13700H","cores":14,"threads":20,"speed":"3.7 GHz"},"gpu":{"name":"Intel Iris Xe","memory":"Shared"},"ram":{"size":"16GB","type":"LPDDR5","speed":"5200MHz"},"storage":{"size":"1TB","type":"NVMe SSD"},"display":{"size":"14 inch","resolution":"2880x1800","type":"OLED"}}'),
      (2, amazon_id, 1999.99, 'https://www.amazon.com/laptop2', TRUE, 0.00, 4.8, 210, TRUE, 2, TRUE, 12, 'MacBook Pro 16" M2 Pro', 'Apple', 'https://www.amazon.com', '{"cpu":{"name":"Apple M2 Pro","cores":10,"threads":10,"speed":"3.5 GHz"},"gpu":{"name":"Apple M2 Pro","memory":"16GB"},"ram":{"size":"32GB","type":"Unified","speed":"6400MHz"},"storage":{"size":"1TB","type":"NVMe SSD"},"display":{"size":"16 inch","resolution":"3456x2234","type":"Liquid Retina XDR"}}'),
      (2, newegg_id, 1949.99, 'https://www.newegg.com/laptop2', TRUE, 9.99, 4.7, 95, FALSE, 3, TRUE, 12, 'MacBook Pro 16" M2 Pro', 'Apple', 'https://www.newegg.com', '{"cpu":{"name":"Apple M2 Pro","cores":10,"threads":10,"speed":"3.5 GHz"},"gpu":{"name":"Apple M2 Pro","memory":"16GB"},"ram":{"size":"32GB","type":"Unified","speed":"6400MHz"},"storage":{"size":"1TB","type":"NVMe SSD"},"display":{"size":"16 inch","resolution":"3456x2234","type":"Liquid Retina XDR"}}'),
      (3, amazon_id, 1099.99, 'https://www.amazon.com/laptop3', TRUE, 0.00, 4.4, 150, TRUE, 2, TRUE, 12, 'ASUS Zenbook 14 OLED', 'ASUS', 'https://www.amazon.com', '{"cpu":{"name":"AMD Ryzen 7 7840HS","cores":8,"threads":16,"speed":"3.8 GHz"},"gpu":{"name":"AMD Radeon 780M","memory":"Shared"},"ram":{"size":"16GB","type":"LPDDR5X","speed":"6400MHz"},"storage":{"size":"1TB","type":"NVMe SSD"},"display":{"size":"14 inch","resolution":"2880x1800","type":"OLED"}}'),
      (3, walmart_id, 1149.99, 'https://www.walmart.com/laptop3', TRUE, 0.00, 4.2, 65, TRUE, 3, TRUE, 12, 'ASUS Zenbook 14 OLED', 'ASUS', 'https://www.walmart.com', '{"cpu":{"name":"AMD Ryzen 7 7840HS","cores":8,"threads":16,"speed":"3.8 GHz"},"gpu":{"name":"AMD Radeon 780M","memory":"Shared"},"ram":{"size":"16GB","type":"LPDDR5X","speed":"6400MHz"},"storage":{"size":"1TB","type":"NVMe SSD"},"display":{"size":"14 inch","resolution":"2880x1800","type":"OLED"}}');

    RAISE NOTICE 'Datos insertados en la tabla laptop_listings';
  ELSE
    RAISE NOTICE 'No se insertaron datos en laptop_listings: vacía=%, laptop1=%, laptop2=%, laptop3=%, amazon=%, bestbuy=%, newegg=%, walmart=%',
      is_table_empty('laptop_listings'), laptop1_exists, laptop2_exists, laptop3_exists,
      amazon_id, bestbuy_id, newegg_id, walmart_id;
  END IF;
END $$;

-- Insertar datos en price_history si está vacía
DO $$
DECLARE
  listing1_id INT;
  listing2_id INT;
  listing3_id INT;
  listing4_id INT;
  listing5_id INT;
  listing6_id INT;
BEGIN
  -- Verificar si tenemos listings
  IF NOT is_table_empty('laptop_listings') THEN
    -- Obtener IDs de los listings (uno por uno para evitar problemas con MAX)
    SELECT id INTO listing1_id FROM laptop_listings ORDER BY id LIMIT 1;
    SELECT id INTO listing2_id FROM laptop_listings WHERE id > listing1_id ORDER BY id LIMIT 1;
    SELECT id INTO listing3_id FROM laptop_listings WHERE id > listing2_id ORDER BY id LIMIT 1;
    SELECT id INTO listing4_id FROM laptop_listings WHERE id > listing3_id ORDER BY id LIMIT 1;
    SELECT id INTO listing5_id FROM laptop_listings WHERE id > listing4_id ORDER BY id LIMIT 1;
    SELECT id INTO listing6_id FROM laptop_listings WHERE id > listing5_id ORDER BY id LIMIT 1;

    RAISE NOTICE 'IDs de listings: 1=%, 2=%, 3=%, 4=%, 5=%, 6=%',
      listing1_id, listing2_id, listing3_id, listing4_id, listing5_id, listing6_id;

    -- Insertar en price_history si está vacía y tenemos los listings necesarios
    IF is_table_empty('price_history') AND
       listing1_id IS NOT NULL AND listing2_id IS NOT NULL AND listing3_id IS NOT NULL AND
       listing4_id IS NOT NULL AND listing5_id IS NOT NULL AND listing6_id IS NOT NULL THEN

      -- Primero insertamos los precios iniciales
      INSERT INTO price_history (listing_id, price, recorded_at)
      SELECT
        id,
        price,
        CURRENT_TIMESTAMP - INTERVAL '30 days'
      FROM laptop_listings
      WHERE id IN (listing1_id, listing2_id, listing3_id, listing4_id, listing5_id, listing6_id);

      -- Insertar historial de precios para cada listing (precios anteriores)
      INSERT INTO price_history (listing_id, price, recorded_at)
      SELECT
        listing1_id,
        1349.99,
        CURRENT_TIMESTAMP - INTERVAL '25 days'
      UNION ALL
      SELECT
        listing1_id,
        1329.99,
        CURRENT_TIMESTAMP - INTERVAL '15 days'
      UNION ALL
      SELECT
        listing1_id,
        1299.99,
        CURRENT_TIMESTAMP - INTERVAL '5 days'
      UNION ALL
      SELECT
        listing2_id,
        1399.99,
        CURRENT_TIMESTAMP - INTERVAL '20 days'
      UNION ALL
      SELECT
        listing2_id,
        1349.99,
        CURRENT_TIMESTAMP - INTERVAL '10 days'
      UNION ALL
      SELECT
        listing3_id,
        2099.99,
        CURRENT_TIMESTAMP - INTERVAL '15 days'
      UNION ALL
      SELECT
        listing3_id,
        1999.99,
        CURRENT_TIMESTAMP - INTERVAL '5 days'
      UNION ALL
      SELECT
        listing4_id,
        1999.99,
        CURRENT_TIMESTAMP - INTERVAL '15 days'
      UNION ALL
      SELECT
        listing4_id,
        1949.99,
        CURRENT_TIMESTAMP - INTERVAL '5 days'
      UNION ALL
      SELECT
        listing5_id,
        1199.99,
        CURRENT_TIMESTAMP - INTERVAL '20 days'
      UNION ALL
      SELECT
        listing5_id,
        1149.99,
        CURRENT_TIMESTAMP - INTERVAL '10 days'
      UNION ALL
      SELECT
        listing5_id,
        1099.99,
        CURRENT_TIMESTAMP - INTERVAL '3 days';

      RAISE NOTICE 'Datos insertados en la tabla price_history';
    ELSE
      RAISE NOTICE 'No se insertaron datos en price_history: vacía=%, listing1=%, listing2=%, listing3=%, listing4=%, listing5=%, listing6=%',
        is_table_empty('price_history'), listing1_id, listing2_id, listing3_id, listing4_id, listing5_id, listing6_id;
    END IF;
  ELSE
    RAISE NOTICE 'No se insertaron datos en price_history porque la tabla laptop_listings está vacía';
  END IF;
END $$;

-- Eliminar las funciones temporales
DROP FUNCTION IF EXISTS is_table_empty;
DROP FUNCTION IF EXISTS value_exists;

-- Mostrar un resumen de las tablas actualizadas
SELECT
  table_name,
  (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_name = t.table_name) > 0 AS exists,
  (SELECT COUNT(*) FROM pg_tables WHERE schemaname = 'public' AND tablename = t.table_name) > 0 AS is_table,
  (SELECT COUNT(*) FROM pg_views WHERE schemaname = 'public' AND viewname = t.table_name) > 0 AS is_view,
  (SELECT COUNT(*) FROM pg_matviews WHERE schemaname = 'public' AND matviewname = t.table_name) > 0 AS is_matview,
  (SELECT COUNT(*) FROM (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = t.table_name LIMIT 1) AS x) > 0 AS table_exists,
  CASE
    WHEN (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_name = t.table_name) > 0 THEN
      (SELECT COUNT(*) FROM (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = t.table_name LIMIT 1) AS x) > 0
    ELSE FALSE
  END AS is_base_table
FROM (
  VALUES
    ('sources'),
    ('operating_systems'),
    ('tag_categories'),
    ('tags'),
    ('cpus'),
    ('gpus'),
    ('ram_configurations'),
    ('storage_devices'),
    ('laptop_cpus'),
    ('laptop_gpus'),
    ('laptop_ram'),
    ('laptop_storage'),
    ('laptop_os_compatibility'),
    ('laptop_tags'),
    ('laptop_listings'),
    ('price_history')
) AS t(table_name);
  `;

  try {
    // Guardar la consulta SQL en un archivo temporal
    const tempFile = path.join(__dirname, 'temp_seed_empty.sql');
    fs.writeFileSync(tempFile, sqlQuery);

    // Ejecutar la consulta usando psql
    console.log('Ejecutando consulta SQL para insertar datos en tablas vacías...');

    try {
      execSync(`docker cp ${tempFile} supabase-db:/tmp/temp_seed_empty.sql && docker exec supabase-db psql -U postgres -d postgres -f /tmp/temp_seed_empty.sql`, {
        stdio: 'inherit'
      });
      console.log('\x1b[32m%s\x1b[0m', '✅ Datos insertados correctamente en las tablas vacías');
    } catch (execError) {
      console.error('\x1b[31m%s\x1b[0m', '❌ Error al insertar datos:', execError.message);
    } finally {
      // Eliminar el archivo temporal
      if (fs.existsSync(tempFile)) {
        fs.unlinkSync(tempFile);
      }
    }
  } catch (err) {
    console.error('\x1b[31m%s\x1b[0m', '❌ Error:', err.message);
  }
}

// Ejecutar la función principal
main().catch(err => {
  console.error('\x1b[31m%s\x1b[0m', '❌ Error en la ejecución del script:', err.message);
  process.exit(1);
});