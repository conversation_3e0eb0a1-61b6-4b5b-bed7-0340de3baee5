-- Fase 2: Tablas para compatibilidad con LLMs y características avanzadas
-- Este script añade las tablas específicas para LLMs y funcionalidades avanzadas

-- Compatibilidad con LLMs y software específico
CREATE TABLE llm_models (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    parameters_billions NUMERIC(10, 2),
    quantization_bits INT, -- 8-bit, 4-bit, etc.
    min_ram_gb INT,
    min_vram_gb INT,
    requires_gpu BOOLEAN DEFAULT TRUE,
    description TEXT,
    model_card_url VARCHAR(512),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE laptop_llm_compatibility (
    laptop_id INT NOT NULL REFERENCES laptops(id),
    llm_id INT NOT NULL REFERENCES llm_models(id),
    estimated_tokens_per_second INT,
    max_context_length INT,
    score INT, -- General compatibility score
    qualitative_assessment TEXT,
    can_run_offline BOOLEAN DEFAULT FALSE,
    recommended_batch_size INT,
    estimated_memory_usage_gb NUMERIC(5, 2),
    PRIMARY KEY (laptop_id, llm_id)
);

-- Sistemas operativos y software
CREATE TABLE operating_systems (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    version VARCHAR(50),
    is_linux BOOLEAN DEFAULT FALSE,
    is_windows BOOLEAN DEFAULT FALSE,
    is_macos BOOLEAN DEFAULT FALSE,
    min_ram_gb INT,
    min_storage_gb INT
);

CREATE TABLE laptop_os_compatibility (
    laptop_id INT NOT NULL REFERENCES laptops(id),
    os_id INT NOT NULL REFERENCES operating_systems(id),
    is_officially_supported BOOLEAN DEFAULT TRUE,
    has_driver_issues BOOLEAN DEFAULT FALSE,
    notes TEXT,
    PRIMARY KEY (laptop_id, os_id)
);

-- Sistema de etiquetas para filtrado dinámico
CREATE TABLE tag_categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT
);

CREATE TABLE tags (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    category_id INT REFERENCES tag_categories(id),
    description TEXT
);

CREATE TABLE laptop_tags (
    laptop_id INT NOT NULL REFERENCES laptops(id),
    tag_id INT NOT NULL REFERENCES tags(id),
    relevance_score INT DEFAULT 100, -- Para ordenar por relevancia
    PRIMARY KEY (laptop_id, tag_id)
);

-- Sistema de puntuación global
CREATE TABLE laptop_scores (
    id SERIAL PRIMARY KEY,
    laptop_id INT NOT NULL REFERENCES laptops(id) UNIQUE,
    overall_score INT NOT NULL,
    llm_performance_score INT NOT NULL,
    battery_life_score INT,
    build_quality_score INT,
    display_score INT,
    keyboard_score INT,
    performance_score INT,
    value_score INT,
    thermal_score INT,
    noise_score INT,
    portability_score INT,
    last_evaluated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Actualizar la vista laptop_basic_specs para incluir puntuaciones
DROP VIEW IF EXISTS laptop_basic_specs;
CREATE VIEW laptop_basic_specs AS
SELECT
    l.id, l.model_name, b.name AS brand_name, b.manufacturer_id,
    m.name AS manufacturer_name,
    d.size_inches AS screen_size, rt.name AS resolution,
    pt.name AS panel_type, d.refresh_rate,
    cpu.model AS cpu_model, cpu.cores, cpu.threads,
    gpu.model AS gpu_model, gpu.vram_gb,
    rc.size_gb AS ram_gb, rc.speed_mhz AS ram_speed,
    mt.name AS memory_type,
    sd.capacity_gb AS storage_capacity,
    si.name AS storage_interface,
    ps.weight_kg,
    lscores.overall_score,
    lscores.llm_performance_score
FROM laptops l
JOIN brands b ON l.brand_id = b.id
JOIN manufacturers m ON b.manufacturer_id = m.id
LEFT JOIN displays d ON d.laptop_id = l.id
LEFT JOIN resolution_types rt ON d.resolution_id = rt.id
LEFT JOIN panel_types pt ON d.panel_type_id = pt.id
LEFT JOIN laptop_cpus lc ON lc.laptop_id = l.id
LEFT JOIN cpus cpu ON lc.cpu_id = cpu.id
LEFT JOIN laptop_gpus lg ON lg.laptop_id = l.id
LEFT JOIN gpus gpu ON lg.gpu_id = gpu.id
LEFT JOIN laptop_ram lr ON lr.laptop_id = l.id
LEFT JOIN ram_configurations rc ON lr.ram_configuration_id = rc.id
LEFT JOIN memory_types mt ON rc.memory_type_id = mt.id
LEFT JOIN laptop_storage lst ON lst.laptop_id = l.id AND lst.is_primary = TRUE
LEFT JOIN storage_devices sd ON lst.storage_id = sd.id
LEFT JOIN storage_interfaces si ON sd.interface_id = si.id
LEFT JOIN physical_specs ps ON ps.laptop_id = l.id
LEFT JOIN laptop_scores lscores ON lscores.laptop_id = l.id;

-- Vista para compatibilidad con LLMs
CREATE VIEW llm_compatible_laptops AS
SELECT
    l.id AS laptop_id,
    l.model_name,
    b.name AS brand,
    llm.name AS llm_model,
    llm.parameters_billions,
    llc.estimated_tokens_per_second,
    llc.score AS compatibility_score,
    llc.can_run_offline,
    llc.recommended_batch_size,
    llc.estimated_memory_usage_gb
FROM laptops l
JOIN brands b ON l.brand_id = b.id
JOIN laptop_llm_compatibility llc ON llc.laptop_id = l.id
JOIN llm_models llm ON llc.llm_id = llm.id
WHERE llc.score > 50
ORDER BY l.id, llc.score DESC;

-- Aplicar el trigger de actualización a la tabla llm_models
CREATE TRIGGER update_llm_models_modtime
BEFORE UPDATE ON llm_models
FOR EACH ROW
EXECUTE PROCEDURE update_modified_column();
