-- Actualización del esquema para asegurar compatibilidad con las APIs de Revolico, laptop-ventas.ola.click y Smart-things.ola.click
-- Fecha: Mayo 2025

-- 1. Actualizar la restricción del esquema JSON para specs en laptop_listings
-- Modificar para asegurar que puede almacenar todos los datos de las diferentes fuentes
ALTER TABLE laptop_listings DROP CONSTRAINT IF EXISTS specs_schema;

-- Crear una nueva restricción más flexible que permita almacenar datos de diferentes fuentes
ALTER TABLE laptop_listings
ADD CONSTRAINT specs_schema CHECK (
  specs IS NULL OR jsonb_typeof(specs) = 'object'
);

-- 2. <PERSON><PERSON>dir campos adicionales a laptop_listings para mejorar la compatibilidad con las diferentes fuentes
ALTER TABLE laptop_listings
ADD COLUMN IF NOT EXISTS currency VARCHAR(10) DEFAULT 'USD',
ADD COLUMN IF NOT EXISTS original_price NUMERIC(10, 2),
ADD COLUMN IF NOT EXISTS posted_date TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS seller_info JSONB,
ADD COLUMN IF NOT EXISTS raw_data JSONB;

-- 3. Crear una función para extraer especificaciones de la descripción
CREATE OR REPLACE FUNCTION extract_specs_from_description(description TEXT)
RETURNS JSONB AS $$
DECLARE
  specs JSONB;
  cpu_match TEXT;
  ram_match TEXT;
  storage_match TEXT;
  gpu_match TEXT;
  display_match TEXT;
BEGIN
  specs = '{
    "cpu": {"name": null, "cores": null, "threads": null},
    "ram": {"size": null, "type": null},
    "storage": {"size": null, "type": null},
    "gpu": {"name": null, "type": null, "vram": null},
    "display": {"size": null, "resolution": null, "type": null}
  }'::jsonb;
  
  -- Extraer CPU (Intel Core i5, i7, AMD Ryzen, etc.)
  IF description ~* '(intel|core|i[3579]|ryzen|amd|m1|m2|m3)' THEN
    -- Extraer nombre de CPU
    IF description ~* 'i[3579][ -]?\d{4}[a-z]*' THEN
      cpu_match := substring(description from 'i[3579][ -]?\d{4}[a-z]*');
      specs = jsonb_set(specs, '{cpu,name}', to_jsonb('Intel Core ' || cpu_match));
    ELSIF description ~* 'ryzen [3579][ -]\d{4}[a-z]*' THEN
      cpu_match := substring(description from 'ryzen [3579][ -]\d{4}[a-z]*');
      specs = jsonb_set(specs, '{cpu,name}', to_jsonb('AMD ' || cpu_match));
    ELSIF description ~* 'm[123] (pro|max|ultra)?' THEN
      cpu_match := substring(description from 'm[123] (pro|max|ultra)?');
      specs = jsonb_set(specs, '{cpu,name}', to_jsonb('Apple ' || cpu_match));
    END IF;
    
    -- Extraer núcleos
    IF description ~* '(\d+)[ -]?cores?' THEN
      specs = jsonb_set(specs, '{cpu,cores}', to_jsonb(substring(description from '(\d+)[ -]?cores?')::int));
    END IF;
    
    -- Extraer hilos
    IF description ~* '(\d+)[ -]?threads?' THEN
      specs = jsonb_set(specs, '{cpu,threads}', to_jsonb(substring(description from '(\d+)[ -]?threads?')::int));
    END IF;
  END IF;
  
  -- Extraer RAM (8GB, 16GB, etc.)
  IF description ~* '(\d+)[ -]?gb' THEN
    ram_match := substring(description from '(\d+)[ -]?gb');
    specs = jsonb_set(specs, '{ram,size}', to_jsonb(ram_match::int));
    
    -- Extraer tipo de RAM
    IF description ~* 'ddr[45]' THEN
      specs = jsonb_set(specs, '{ram,type}', to_jsonb(upper(substring(description from 'ddr[45]'))));
    END IF;
  END IF;
  
  -- Extraer almacenamiento (SSD, HDD, etc.)
  IF description ~* '(\d+)[ -]?(gb|tb).*?(ssd|hdd|nvme)' THEN
    storage_match := substring(description from '(\d+)[ -]?(gb|tb).*?(ssd|hdd|nvme)');
    
    -- Extraer tamaño
    IF storage_match ~* '(\d+)[ -]?(gb|tb)' THEN
      DECLARE
        size_value INT;
        size_unit TEXT;
      BEGIN
        size_value := substring(storage_match from '(\d+)')::int;
        size_unit := lower(substring(storage_match from '(gb|tb)'));
        
        -- Convertir TB a GB si es necesario
        IF size_unit = 'tb' THEN
          size_value := size_value * 1024;
        END IF;
        
        specs = jsonb_set(specs, '{storage,size}', to_jsonb(size_value));
      END;
    END IF;
    
    -- Extraer tipo
    IF storage_match ~* '(ssd|hdd|nvme)' THEN
      specs = jsonb_set(specs, '{storage,type}', to_jsonb(upper(substring(storage_match from '(ssd|hdd|nvme)'))));
    END IF;
  END IF;
  
  -- Extraer GPU
  IF description ~* '(nvidia|geforce|gtx|rtx|radeon|intel iris|intel uhd)' THEN
    gpu_match := substring(description from '(nvidia|geforce|gtx|rtx|radeon|intel iris|intel uhd)[^,\.]*');
    specs = jsonb_set(specs, '{gpu,name}', to_jsonb(gpu_match));
    
    -- Determinar si es dedicada o integrada
    IF gpu_match ~* '(nvidia|geforce|gtx|rtx|radeon)' THEN
      specs = jsonb_set(specs, '{gpu,type}', to_jsonb('Dedicada'));
    ELSE
      specs = jsonb_set(specs, '{gpu,type}', to_jsonb('Integrada'));
    END IF;
    
    -- Extraer VRAM
    IF description ~* '(\d+)[ -]?gb.*?vram' THEN
      specs = jsonb_set(specs, '{gpu,vram}', to_jsonb(substring(description from '(\d+)[ -]?gb.*?vram')::int));
    END IF;
  END IF;
  
  -- Extraer pantalla
  IF description ~* '(\d+(\.\d+)?)[ -]?pulgadas?' OR description ~* '(\d+(\.\d+)?)[ -]?inch' THEN
    IF description ~* '(\d+(\.\d+)?)[ -]?pulgadas?' THEN
      display_match := substring(description from '(\d+(\.\d+)?)[ -]?pulgadas?');
    ELSE
      display_match := substring(description from '(\d+(\.\d+)?)[ -]?inch');
    END IF;
    specs = jsonb_set(specs, '{display,size}', to_jsonb(display_match::float));
    
    -- Extraer resolución
    IF description ~* '(\d+x\d+)' THEN
      specs = jsonb_set(specs, '{display,resolution}', to_jsonb(substring(description from '(\d+x\d+)')));
    ELSIF description ~* '(full hd|fhd|uhd|4k|2k|qhd)' THEN
      specs = jsonb_set(specs, '{display,resolution}', to_jsonb(substring(description from '(full hd|fhd|uhd|4k|2k|qhd)')));
    END IF;
    
    -- Extraer tipo de pantalla
    IF description ~* '(ips|oled|lcd|led|retina)' THEN
      specs = jsonb_set(specs, '{display,type}', to_jsonb(upper(substring(description from '(ips|oled|lcd|led|retina)'))));
    END IF;
  END IF;
  
  RETURN specs;
END;
$$ LANGUAGE plpgsql;

-- 4. Crear un trigger para extraer automáticamente las especificaciones de la descripción
CREATE OR REPLACE FUNCTION update_specs_from_description()
RETURNS TRIGGER AS $$
BEGIN
  -- Solo actualizar si specs es NULL y hay una descripción
  IF (NEW.specs IS NULL OR NEW.specs = '{}'::jsonb) AND NEW.description IS NOT NULL THEN
    NEW.specs = extract_specs_from_description(NEW.description);
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Crear el trigger
DROP TRIGGER IF EXISTS update_specs_trigger ON laptop_listings;
CREATE TRIGGER update_specs_trigger
BEFORE INSERT OR UPDATE ON laptop_listings
FOR EACH ROW
EXECUTE FUNCTION update_specs_from_description();

-- 5. Actualizar las fuentes existentes para asegurar que tienen la configuración correcta
-- Revolico ya está configurado en update_revolico_source.sql
-- laptop-ventas.ola.click está configurado en update_laptop_ventas_source.sql
-- Smart-things.ola.click está configurado en update_smart_things_source.sql

-- 6. Crear índices adicionales para mejorar el rendimiento
CREATE INDEX IF NOT EXISTS idx_laptop_listings_currency ON laptop_listings(currency);
CREATE INDEX IF NOT EXISTS idx_laptop_listings_posted_date ON laptop_listings(posted_date);
CREATE INDEX IF NOT EXISTS idx_laptop_listings_name ON laptop_listings(name);
CREATE INDEX IF NOT EXISTS idx_laptop_listings_brand ON laptop_listings(brand);

-- 7. Actualizar la vista laptop_basic_specs para incluir los nuevos campos
DROP VIEW IF EXISTS laptop_basic_specs CASCADE;
CREATE VIEW laptop_basic_specs AS
SELECT
    l.id, l.model_name, b.name AS brand_name, b.manufacturer_id,
    m.name AS manufacturer_name,
    d.size_inches AS screen_size, rt.name AS resolution,
    pt.name AS panel_type, d.refresh_rate,
    cpu.model AS cpu_model, cpu.cores, cpu.threads,
    gpu.model AS gpu_model, gpu.vram_gb,
    rc.size_gb AS ram_gb, rc.speed_mhz AS ram_speed,
    mt.name AS memory_type,
    sd.capacity_gb AS storage_capacity,
    si.name AS storage_interface,
    ps.weight_kg,
    lscores.overall_score,
    lscores.llm_performance_score,
    ll.specs,
    ll.currency,
    ll.original_price,
    ll.posted_date,
    ll.seller_info
FROM laptops l
JOIN brands b ON l.brand_id = b.id
JOIN manufacturers m ON b.manufacturer_id = m.id
LEFT JOIN displays d ON d.laptop_id = l.id
LEFT JOIN resolution_types rt ON d.resolution_id = rt.id
LEFT JOIN panel_types pt ON d.panel_type_id = pt.id
LEFT JOIN laptop_cpus lc ON lc.laptop_id = l.id
LEFT JOIN cpus cpu ON lc.cpu_id = cpu.id
LEFT JOIN laptop_gpus lg ON lg.laptop_id = l.id
LEFT JOIN gpus gpu ON lg.gpu_id = gpu.id
LEFT JOIN laptop_ram lr ON lr.laptop_id = l.id
LEFT JOIN ram_configurations rc ON lr.ram_configuration_id = rc.id
LEFT JOIN memory_types mt ON rc.memory_type_id = mt.id
LEFT JOIN laptop_storage lst ON lst.laptop_id = l.id AND lst.is_primary = TRUE
LEFT JOIN storage_devices sd ON lst.storage_id = sd.id
LEFT JOIN storage_interfaces si ON sd.interface_id = si.id
LEFT JOIN physical_specs ps ON ps.laptop_id = l.id
LEFT JOIN laptop_scores lscores ON lscores.laptop_id = l.id
LEFT JOIN (
    SELECT DISTINCT ON (laptop_id) *
    FROM laptop_listings
    WHERE specs IS NOT NULL
    ORDER BY laptop_id, id DESC
) ll ON ll.laptop_id = l.id;
