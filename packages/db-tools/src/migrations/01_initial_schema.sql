-- Fase 1: Configuración Inicial para LLM Laptop Lens
-- Este script crea las tablas básicas y sus relaciones

-- Catálogos básicos
CREATE TABLE manufacturers (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    website VARCHAR(512),
    country VARCHAR(100),
    founded_year INT
);

CREATE TABLE brands (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    website VARCHAR(512),
    logo_url VARCHAR(512),
    manufacturer_id INT REFERENCES manufacturers(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE sources (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    url VARCHAR(512),
    api_endpoint VARCHAR(512),
    is_active BOOLEAN DEFAULT TRUE,
    last_updated TIMESTAMP,
    update_frequency VARCHAR(50), -- 'daily', 'weekly', etc.
    selectors JSONB DEFAULT '{}'::jsonb, -- Para configuración de scraping
    last_scrape_attempt TIMESTAMP,
    scrape_success_count INT DEFAULT 0,
    scrape_failure_count INT DEFAULT 0
);

-- Tabla para historial de scraping
CREATE TABLE scraping_history (
    id SERIAL PRIMARY KEY,
    source_id INT NOT NULL REFERENCES sources(id),
    start_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP,
    status VARCHAR(50) NOT NULL DEFAULT 'running', -- 'running', 'completed', 'failed'
    items_found INT DEFAULT 0,
    error_message TEXT
);

-- Especificaciones técnicas normalizadas
CREATE TABLE panel_types (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    advantages TEXT,
    disadvantages TEXT
);

CREATE TABLE resolution_types (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE, -- 'FHD', '4K', etc.
    width INT NOT NULL,
    height INT NOT NULL,
    aspect_ratio VARCHAR(10)
);

CREATE TABLE cpu_architectures (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    technology_nm INT, -- Fabrication process in nanometers
    release_year INT
);

CREATE TABLE memory_types (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,    -- DDR4, DDR5, etc.
    description TEXT,
    max_bandwidth_gbps NUMERIC(8, 2)
);

CREATE TABLE storage_interfaces (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,    -- SATA, PCIe, etc.
    description TEXT,
    max_throughput_gbps NUMERIC(8, 2)
);

CREATE TABLE port_types (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE, -- USB-A, USB-C, HDMI, etc.
    description TEXT,
    max_data_rate_gbps NUMERIC(8, 2),
    supports_power_delivery BOOLEAN DEFAULT FALSE,
    max_power_delivery_watts INT
);

-- Tabla principal de laptops
CREATE TABLE laptops (
    id SERIAL PRIMARY KEY,
    model_name VARCHAR(255) NOT NULL,
    brand_id INT NOT NULL REFERENCES brands(id),
    release_date DATE,
    description TEXT,
    image_url VARCHAR(512),
    is_available BOOLEAN DEFAULT TRUE,
    msrp NUMERIC(10, 2), -- Manufacturer's suggested retail price
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (brand_id, model_name)
);

-- Crear índice para búsquedas frecuentes
CREATE INDEX idx_laptops_model_brand ON laptops(model_name, brand_id);

-- Especificaciones de pantalla normalizadas
CREATE TABLE displays (
    id SERIAL PRIMARY KEY,
    laptop_id INT NOT NULL REFERENCES laptops(id),
    size_inches NUMERIC(4, 2) NOT NULL,
    resolution_id INT NOT NULL REFERENCES resolution_types(id),
    refresh_rate INT,
    panel_type_id INT REFERENCES panel_types(id),
    is_touchscreen BOOLEAN DEFAULT FALSE,
    brightness_nits INT,
    color_gamut VARCHAR(50),
    hdr_support BOOLEAN DEFAULT FALSE,
    response_time_ms INT -- Para gaming
);

-- Especificaciones físicas
CREATE TABLE physical_specs (
    id SERIAL PRIMARY KEY,
    laptop_id INT NOT NULL REFERENCES laptops(id) UNIQUE, -- Una laptop solo tiene una especificación física
    weight_kg NUMERIC(5, 2) NOT NULL,
    height_mm NUMERIC(6, 2),
    width_mm NUMERIC(6, 2),
    depth_mm NUMERIC(6, 2),
    material VARCHAR(100),
    color VARCHAR(100),
    has_fingerprint_reader BOOLEAN DEFAULT FALSE,
    has_webcam BOOLEAN DEFAULT TRUE,
    webcam_resolution VARCHAR(50),
    has_backlit_keyboard BOOLEAN DEFAULT FALSE
);

-- Componentes principales
CREATE TABLE cpus (
    id SERIAL PRIMARY KEY,
    manufacturer_id INT NOT NULL REFERENCES manufacturers(id),
    model VARCHAR(255) NOT NULL,
    generation VARCHAR(100),
    cores INT NOT NULL,
    threads INT NOT NULL,
    base_clock_ghz NUMERIC(4, 2) NOT NULL,
    boost_clock_ghz NUMERIC(4, 2),
    tdp_watts INT,
    cache_mb INT,
    architecture_id INT REFERENCES cpu_architectures(id),
    supports_avx512 BOOLEAN DEFAULT FALSE, -- Importante para algunos LLMs
    UNIQUE (manufacturer_id, model, generation)
);

CREATE TABLE gpus (
    id SERIAL PRIMARY KEY,
    manufacturer_id INT NOT NULL REFERENCES manufacturers(id),
    model VARCHAR(255) NOT NULL,
    vram_gb INT NOT NULL,
    memory_type_id INT REFERENCES memory_types(id),
    base_clock_mhz INT,
    boost_clock_mhz INT,
    tdp_watts INT,
    ray_tracing_support BOOLEAN DEFAULT FALSE,
    tensor_cores INT,
    cuda_cores INT, -- Para NVIDIA
    compute_units INT, -- Para AMD
    supports_dlss BOOLEAN DEFAULT FALSE, -- NVIDIA DLSS
    supports_fsr BOOLEAN DEFAULT FALSE, -- AMD FSR
    UNIQUE (manufacturer_id, model, vram_gb)
);

CREATE TABLE ram_configurations (
    id SERIAL PRIMARY KEY,
    memory_type_id INT NOT NULL REFERENCES memory_types(id),
    size_gb INT NOT NULL,
    speed_mhz INT NOT NULL,
    manufacturer_id INT REFERENCES manufacturers(id),
    is_dual_channel BOOLEAN DEFAULT FALSE,
    cas_latency INT -- Importante para rendimiento
);

CREATE TABLE storage_devices (
    id SERIAL PRIMARY KEY,
    interface_id INT NOT NULL REFERENCES storage_interfaces(id),
    capacity_gb INT NOT NULL,
    manufacturer_id INT REFERENCES manufacturers(id),
    read_speed_mbps INT,
    write_speed_mbps INT,
    is_nvme BOOLEAN DEFAULT FALSE,
    has_dram_cache BOOLEAN DEFAULT FALSE
);

-- Relaciones básicas
CREATE TABLE laptop_cpus (
    laptop_id INT NOT NULL REFERENCES laptops(id),
    cpu_id INT NOT NULL REFERENCES cpus(id),
    performance_score INT,
    PRIMARY KEY (laptop_id, cpu_id)
);

CREATE TABLE laptop_gpus (
    laptop_id INT NOT NULL REFERENCES laptops(id),
    gpu_id INT NOT NULL REFERENCES gpus(id),
    is_discrete BOOLEAN DEFAULT TRUE, -- Diferencia entre GPU dedicada e integrada
    performance_score INT,
    PRIMARY KEY (laptop_id, gpu_id)
);

CREATE TABLE laptop_ram (
    laptop_id INT NOT NULL REFERENCES laptops(id),
    ram_configuration_id INT NOT NULL REFERENCES ram_configurations(id),
    slots_used INT NOT NULL DEFAULT 1,
    max_slots INT,
    expandable BOOLEAN DEFAULT FALSE,
    max_supported_gb INT,
    PRIMARY KEY (laptop_id, ram_configuration_id)
);

CREATE TABLE laptop_storage (
    laptop_id INT NOT NULL REFERENCES laptops(id),
    storage_id INT NOT NULL REFERENCES storage_devices(id),
    is_primary BOOLEAN DEFAULT FALSE,
    slot_type VARCHAR(50), -- M.2, 2.5", etc.
    PRIMARY KEY (laptop_id, storage_id)
);

-- Precios y disponibilidad
CREATE TABLE laptop_listings (
    id SERIAL PRIMARY KEY,
    laptop_id INT NOT NULL REFERENCES laptops(id),
    source_id INT NOT NULL REFERENCES sources(id),
    price NUMERIC(10, 2) NOT NULL,
    url VARCHAR(512) NOT NULL,
    in_stock BOOLEAN DEFAULT TRUE,
    shipping_cost NUMERIC(8, 2),
    rating NUMERIC(3, 2),
    reviews_count INT,
    listing_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    free_shipping BOOLEAN DEFAULT FALSE,
    estimated_delivery_days INT,
    has_warranty BOOLEAN DEFAULT TRUE,
    warranty_months INT,
    UNIQUE (laptop_id, source_id, url)
);

-- Tabla de histórico de precios
CREATE TABLE price_history (
    id SERIAL PRIMARY KEY,
    listing_id INT NOT NULL REFERENCES laptop_listings(id),
    price NUMERIC(10, 2) NOT NULL,
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Vista para facilitar las consultas más comunes
CREATE VIEW laptop_basic_specs AS
SELECT 
    l.id, l.model_name, b.name AS brand_name, b.manufacturer_id,
    m.name AS manufacturer_name,
    d.size_inches AS screen_size, rt.name AS resolution, 
    pt.name AS panel_type, d.refresh_rate,
    cpu.model AS cpu_model, cpu.cores, cpu.threads, 
    gpu.model AS gpu_model, gpu.vram_gb,
    rc.size_gb AS ram_gb, rc.speed_mhz AS ram_speed,
    mt.name AS memory_type,
    sd.capacity_gb AS storage_capacity, 
    si.name AS storage_interface,
    ps.weight_kg
FROM laptops l
JOIN brands b ON l.brand_id = b.id
JOIN manufacturers m ON b.manufacturer_id = m.id
LEFT JOIN displays d ON d.laptop_id = l.id
LEFT JOIN resolution_types rt ON d.resolution_id = rt.id
LEFT JOIN panel_types pt ON d.panel_type_id = pt.id
LEFT JOIN laptop_cpus lc ON lc.laptop_id = l.id
LEFT JOIN cpus cpu ON lc.cpu_id = cpu.id
LEFT JOIN laptop_gpus lg ON lg.laptop_id = l.id
LEFT JOIN gpus gpu ON lg.gpu_id = gpu.id
LEFT JOIN laptop_ram lr ON lr.laptop_id = l.id
LEFT JOIN ram_configurations rc ON lr.ram_configuration_id = rc.id
LEFT JOIN memory_types mt ON rc.memory_type_id = mt.id
LEFT JOIN laptop_storage ls ON ls.laptop_id = l.id AND ls.is_primary = TRUE
LEFT JOIN storage_devices sd ON ls.storage_id = sd.id
LEFT JOIN storage_interfaces si ON sd.interface_id = si.id
LEFT JOIN physical_specs ps ON ps.laptop_id = l.id;

-- Vista para precio más bajo actual por laptop
CREATE VIEW laptop_best_prices AS
SELECT 
    l.id AS laptop_id, 
    l.model_name,
    b.name AS brand,
    MIN(ll.price) AS lowest_price,
    s.name AS source,
    ll.url
FROM laptops l
JOIN brands b ON l.brand_id = b.id
JOIN laptop_listings ll ON ll.laptop_id = l.id
JOIN sources s ON ll.source_id = s.id
WHERE ll.in_stock = TRUE
GROUP BY l.id, l.model_name, b.name, s.name, ll.url;

-- Crear función para actualizar el timestamp de updated_at
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Aplicar el trigger a la tabla laptops
CREATE TRIGGER update_laptops_modtime
BEFORE UPDATE ON laptops
FOR EACH ROW
EXECUTE PROCEDURE update_modified_column();
