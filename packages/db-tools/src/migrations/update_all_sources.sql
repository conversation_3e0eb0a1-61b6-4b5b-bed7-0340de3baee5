-- Script para actualizar todas las fuentes de datos
-- Este script ejecuta todos los scripts de actualización de fuentes

-- Primero, ejecutar las actualizaciones del esquema para asegurar compatibilidad
\i 05_api_compatibility_updates.sql

-- <PERSON><PERSON>, actualizar cada fuente individualmente
\i update_revolico_source.sql
\i update_laptop_ventas_source.sql
\i update_smart_things_source.sql

-- Verificar que todas las fuentes se han actualizado correctamente
SELECT name, url, api_endpoint, is_active, selectors 
FROM sources 
WHERE name IN ('Revolico', 'Laptop Ventas', 'Smart Things');

-- Verificar que los índices se han creado correctamente
SELECT indexname, indexdef 
FROM pg_indexes 
WHERE tablename = 'laptop_listings' 
ORDER BY indexname;

-- Verificar que las funciones y triggers se han creado correctamente
SELECT proname, prosrc 
FROM pg_proc 
WHERE proname IN ('extract_specs_from_description', 'update_specs_from_description');

SELECT tgname, tgrelid::regclass, tgtype, tgenabled 
FROM pg_trigger 
WHERE tgname = 'update_specs_trigger';

-- Mostrar mensaje de éxito
DO $$
BEGIN
  RAISE NOTICE 'Todas las fuentes de datos han sido actualizadas correctamente.';
END $$;
