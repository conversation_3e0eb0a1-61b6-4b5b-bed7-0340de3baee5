-- Script de migración para LLM Laptop Lens
-- Este script migra los datos del esquema actual al nuevo esquema refinado

-- Paso 1: Crear tablas temporales para almacenar los datos existentes
CREATE TEMP TABLE temp_scraping_sources AS
SELECT id, name, base_url, enabled, last_scraped_at, created_at, updated_at
FROM scraping_sources;

CREATE TEMP TABLE temp_laptop_listings AS
SELECT id, url, created_at, status, data, error
FROM laptop_listings;

-- Paso 2: Migrar datos a las nuevas tablas

-- Insertar fabricantes predeterminados
INSERT INTO manufacturers (name, website, country)
VALUES 
('Intel', 'https://www.intel.com', 'United States'),
('AMD', 'https://www.amd.com', 'United States'),
('NVIDIA', 'https://www.nvidia.com', 'United States'),
('Dell', 'https://www.dell.com', 'United States'),
('HP', 'https://www.hp.com', 'United States'),
('Lenovo', 'https://www.lenovo.com', 'China'),
('Apple', 'https://www.apple.com', 'United States'),
('Asus', 'https://www.asus.com', 'Taiwan'),
('Acer', 'https://www.acer.com', 'Taiwan'),
('MSI', 'https://www.msi.com', 'Taiwan'),
('Samsung', 'https://www.samsung.com', 'South Korea'),
('LG', 'https://www.lg.com', 'South Korea'),
('Microsoft', 'https://www.microsoft.com', 'United States'),
('Razer', 'https://www.razer.com', 'United States'),
('Gigabyte', 'https://www.gigabyte.com', 'Taiwan'),
('Toshiba', 'https://www.toshiba.com', 'Japan'),
('Huawei', 'https://www.huawei.com', 'China'),
('Xiaomi', 'https://www.mi.com', 'China'),
('Crucial', 'https://www.crucial.com', 'United States'),
('Kingston', 'https://www.kingston.com', 'United States'),
('Western Digital', 'https://www.westerndigital.com', 'United States'),
('Seagate', 'https://www.seagate.com', 'United States'),
('SanDisk', 'https://www.sandisk.com', 'United States'),
('Corsair', 'https://www.corsair.com', 'United States'),
('G.Skill', 'https://www.gskill.com', 'Taiwan');

-- Insertar marcas predeterminadas
INSERT INTO brands (name, manufacturer_id)
SELECT m.name, m.id
FROM manufacturers m
WHERE m.name IN ('Dell', 'HP', 'Lenovo', 'Apple', 'Asus', 'Acer', 'MSI', 'Samsung', 'LG', 'Microsoft', 'Razer', 'Huawei', 'Xiaomi');

-- Migrar fuentes de scraping
INSERT INTO sources (name, url, is_active, last_updated)
SELECT name, base_url, enabled, last_scraped_at
FROM temp_scraping_sources;

-- Insertar tipos de panel predeterminados
INSERT INTO panel_types (name, description, advantages, disadvantages)
VALUES 
('IPS', 'In-Plane Switching', 'Better color accuracy and viewing angles', 'Higher response times, more expensive'),
('TN', 'Twisted Nematic', 'Fast response times, cheaper', 'Poor viewing angles and color reproduction'),
('VA', 'Vertical Alignment', 'Good contrast ratios and color depth', 'Slower response times than TN'),
('OLED', 'Organic Light-Emitting Diode', 'Perfect blacks, vibrant colors, fast response', 'Burn-in risk, expensive'),
('Mini-LED', 'Mini Light-Emitting Diode', 'High brightness, good contrast', 'More expensive than traditional LED'),
('QLED', 'Quantum Dot LED', 'Enhanced color and brightness', 'Not as good as OLED for blacks');

-- Insertar tipos de resolución predeterminados
INSERT INTO resolution_types (name, width, height, aspect_ratio)
VALUES 
('HD', 1280, 720, '16:9'),
('FHD', 1920, 1080, '16:9'),
('WUXGA', 1920, 1200, '16:10'),
('QHD', 2560, 1440, '16:9'),
('WQHD', 3440, 1440, '21:9'),
('4K UHD', 3840, 2160, '16:9'),
('5K', 5120, 2880, '16:9'),
('8K UHD', 7680, 4320, '16:9');

-- Insertar arquitecturas de CPU predeterminadas
INSERT INTO cpu_architectures (name, description, technology_nm, release_year)
VALUES 
('Zen 3', 'AMD Ryzen 5000 series architecture', 7, 2020),
('Zen 4', 'AMD Ryzen 7000 series architecture', 5, 2022),
('Alder Lake', 'Intel 12th Gen architecture with hybrid design', 10, 2021),
('Raptor Lake', 'Intel 13th Gen architecture', 10, 2022),
('Meteor Lake', 'Intel 14th Gen architecture', 7, 2023),
('Apple Silicon', 'Apple custom ARM-based architecture', 5, 2020);

-- Insertar tipos de memoria predeterminados
INSERT INTO memory_types (name, description, max_bandwidth_gbps)
VALUES 
('DDR4', 'Double Data Rate 4 SDRAM', 25.6),
('DDR5', 'Double Data Rate 5 SDRAM', 51.2),
('LPDDR4X', 'Low Power Double Data Rate 4X', 21.3),
('LPDDR5', 'Low Power Double Data Rate 5', 44.8),
('HBM2', 'High Bandwidth Memory 2', 256.0),
('GDDR6', 'Graphics Double Data Rate 6', 56.0),
('GDDR6X', 'Graphics Double Data Rate 6X', 84.0);

-- Insertar interfaces de almacenamiento predeterminadas
INSERT INTO storage_interfaces (name, description, max_throughput_gbps)
VALUES 
('SATA III', 'Serial ATA 3.0', 6.0),
('PCIe 3.0 x4', 'PCI Express 3.0 with 4 lanes', 32.0),
('PCIe 4.0 x4', 'PCI Express 4.0 with 4 lanes', 64.0),
('PCIe 5.0 x4', 'PCI Express 5.0 with 4 lanes', 128.0),
('UFS 3.1', 'Universal Flash Storage 3.1', 23.2),
('eMMC 5.1', 'Embedded MultiMediaCard 5.1', 3.2);

-- Insertar tipos de puertos predeterminados
INSERT INTO port_types (name, description, max_data_rate_gbps, supports_power_delivery, max_power_delivery_watts)
VALUES 
('USB-A 3.2', 'USB Type-A with USB 3.2 protocol', 10.0, FALSE, 0),
('USB-C 3.2', 'USB Type-C with USB 3.2 protocol', 20.0, TRUE, 100),
('USB-C 4.0', 'USB Type-C with USB 4.0 protocol', 40.0, TRUE, 240),
('Thunderbolt 3', 'Thunderbolt 3 over USB-C', 40.0, TRUE, 100),
('Thunderbolt 4', 'Thunderbolt 4 over USB-C', 40.0, TRUE, 100),
('HDMI 2.0', 'High-Definition Multimedia Interface 2.0', 18.0, FALSE, 0),
('HDMI 2.1', 'High-Definition Multimedia Interface 2.1', 48.0, FALSE, 0),
('DisplayPort 1.4', 'DisplayPort 1.4', 32.4, FALSE, 0),
('DisplayPort 2.0', 'DisplayPort 2.0', 80.0, FALSE, 0),
('Ethernet', 'RJ45 Ethernet port', 1.0, FALSE, 0),
('SD Card', 'Secure Digital card reader', 0.624, FALSE, 0),
('3.5mm Audio', 'Standard audio jack', 0.0, FALSE, 0);

-- Paso 3: Migrar datos de laptop_listings a las nuevas tablas
-- Nota: Este es un ejemplo simplificado. En una implementación real, se necesitaría
-- un script más complejo para extraer y transformar los datos JSON.

-- Crear una función para procesar los datos JSON de laptop_listings
CREATE OR REPLACE FUNCTION migrate_laptop_data()
RETURNS void AS $$
DECLARE
    listing RECORD;
    brand_id INT;
    laptop_id INT;
    cpu_id INT;
    gpu_id INT;
    ram_id INT;
    storage_id INT;
    display_id INT;
    physical_id INT;
    listing_id INT;
BEGIN
    FOR listing IN SELECT * FROM temp_laptop_listings WHERE data IS NOT NULL LOOP
        -- Intentar encontrar la marca o usar una predeterminada
        SELECT id INTO brand_id FROM brands WHERE name = (listing.data->>'brand')::text LIMIT 1;
        IF brand_id IS NULL THEN
            SELECT id INTO brand_id FROM brands WHERE name = 'Dell' LIMIT 1; -- Marca predeterminada
        END IF;
        
        -- Insertar laptop
        INSERT INTO laptops (model_name, brand_id, description, image_url, msrp)
        VALUES (
            COALESCE((listing.data->>'name')::text, 'Unknown Model'),
            brand_id,
            'Migrated from previous database',
            (listing.data->>'image_url')::text,
            COALESCE((listing.data->>'price')::numeric, 0)
        )
        RETURNING id INTO laptop_id;
        
        -- Insertar CPU (simplificado)
        INSERT INTO cpus (manufacturer_id, model, cores, threads, base_clock_ghz, boost_clock_ghz)
        VALUES (
            (SELECT id FROM manufacturers WHERE name = 'Intel' LIMIT 1),
            COALESCE((listing.data->'specs'->>'cpu')::text, 'Unknown CPU'),
            4, -- Valores predeterminados
            8,
            2.5,
            3.5
        )
        RETURNING id INTO cpu_id;
        
        -- Relacionar CPU con laptop
        INSERT INTO laptop_cpus (laptop_id, cpu_id)
        VALUES (laptop_id, cpu_id);
        
        -- Insertar GPU (simplificado)
        INSERT INTO gpus (manufacturer_id, model, vram_gb)
        VALUES (
            (SELECT id FROM manufacturers WHERE name = 'NVIDIA' LIMIT 1),
            COALESCE((listing.data->'specs'->>'gpu')::text, 'Unknown GPU'),
            4 -- Valor predeterminado
        )
        RETURNING id INTO gpu_id;
        
        -- Relacionar GPU con laptop
        INSERT INTO laptop_gpus (laptop_id, gpu_id, is_discrete)
        VALUES (laptop_id, gpu_id, TRUE);
        
        -- Insertar RAM (simplificado)
        INSERT INTO ram_configurations (memory_type_id, size_gb, speed_mhz)
        VALUES (
            (SELECT id FROM memory_types WHERE name = 'DDR4' LIMIT 1),
            COALESCE((listing.data->'specs'->'ram'->>'size')::int, 8),
            2666
        )
        RETURNING id INTO ram_id;
        
        -- Relacionar RAM con laptop
        INSERT INTO laptop_ram (laptop_id, ram_configuration_id)
        VALUES (laptop_id, ram_id);
        
        -- Insertar almacenamiento (simplificado)
        INSERT INTO storage_devices (interface_id, capacity_gb)
        VALUES (
            (SELECT id FROM storage_interfaces WHERE name = 'PCIe 3.0 x4' LIMIT 1),
            COALESCE((listing.data->'specs'->'storage'->>'size')::int, 256)
        )
        RETURNING id INTO storage_id;
        
        -- Relacionar almacenamiento con laptop
        INSERT INTO laptop_storage (laptop_id, storage_id, is_primary)
        VALUES (laptop_id, storage_id, TRUE);
        
        -- Insertar pantalla (simplificado)
        INSERT INTO displays (laptop_id, size_inches, resolution_id, panel_type_id)
        VALUES (
            laptop_id,
            15.6, -- Valor predeterminado
            (SELECT id FROM resolution_types WHERE name = 'FHD' LIMIT 1),
            (SELECT id FROM panel_types WHERE name = 'IPS' LIMIT 1)
        )
        RETURNING id INTO display_id;
        
        -- Insertar especificaciones físicas (simplificado)
        INSERT INTO physical_specs (laptop_id, weight_kg)
        VALUES (
            laptop_id,
            2.0 -- Valor predeterminado
        )
        RETURNING id INTO physical_id;
        
        -- Insertar listing
        INSERT INTO laptop_listings_new (laptop_id, source_id, price, url, in_stock)
        VALUES (
            laptop_id,
            (SELECT id FROM sources LIMIT 1), -- Fuente predeterminada
            COALESCE((listing.data->>'price')::numeric, 0),
            listing.url,
            TRUE
        )
        RETURNING id INTO listing_id;
        
        -- Insertar historial de precios
        INSERT INTO price_history (listing_id, price)
        VALUES (
            listing_id,
            COALESCE((listing.data->>'price')::numeric, 0)
        );
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Ejecutar la función de migración
-- SELECT migrate_laptop_data();

-- Paso 4: Crear índices adicionales para mejorar el rendimiento
CREATE INDEX idx_laptops_brand_id ON laptops(brand_id);
CREATE INDEX idx_laptop_cpus_cpu_id ON laptop_cpus(cpu_id);
CREATE INDEX idx_laptop_gpus_gpu_id ON laptop_gpus(gpu_id);
CREATE INDEX idx_laptop_ram_ram_id ON laptop_ram(ram_configuration_id);
CREATE INDEX idx_laptop_storage_storage_id ON laptop_storage(storage_id);
CREATE INDEX idx_displays_laptop_id ON displays(laptop_id);
CREATE INDEX idx_physical_specs_laptop_id ON physical_specs(laptop_id);
CREATE INDEX idx_laptop_listings_new_laptop_id ON laptop_listings_new(laptop_id);
CREATE INDEX idx_laptop_listings_new_source_id ON laptop_listings_new(source_id);
CREATE INDEX idx_price_history_listing_id ON price_history(listing_id);

-- Paso 5: Limpiar tablas temporales
-- DROP TABLE temp_scraping_sources;
-- DROP TABLE temp_laptop_listings;

-- Nota: Las sentencias DROP TABLE y la llamada a migrate_laptop_data() están comentadas
-- para evitar ejecutarlas accidentalmente. Descoméntalas cuando estés listo para ejecutar
-- la migración completa.
