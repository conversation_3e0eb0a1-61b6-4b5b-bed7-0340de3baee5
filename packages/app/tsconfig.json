{"extends": "../../tsconfig.json", "compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "moduleResolution": "bundler", "allowImportingTsExtensions": true, "isolatedModules": true, "moduleDetection": "force", "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@llm-laptop-lens/common": ["../common/src"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"], "references": [{"path": "../common"}]}