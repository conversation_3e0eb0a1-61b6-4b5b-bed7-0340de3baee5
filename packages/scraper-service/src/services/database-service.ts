/**
 * Servicio para interactuar con la base de datos.
 */
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { SUPABASE_CONFIG } from '../config';
import { LaptopData, DbLaptopListing } from '../types';
import { transformLaptopsToDbFormat } from '../utils/data-transformer';
import { logger } from '../utils/logger';

// Cliente de Supabase
let supabaseClient: SupabaseClient;

/**
 * Inicializa el cliente de Supabase.
 */
export function initializeDatabase(): void {
  // Usar la clave de servicio si está disponible, de lo contrario usar la clave anónima
  const serviceKey = process.env.SUPABASE_SERVICE_KEY || SUPABASE_CONFIG.key;
  supabaseClient = createClient(SUPABASE_CONFIG.url, serviceKey);
  logger.info('Cliente de Supabase inicializado');
}

/**
 * Obtiene el cliente de Supabase.
 */
export function getSupabaseClient(): SupabaseClient {
  if (!supabaseClient) {
    initializeDatabase();
  }
  return supabaseClient;
}

/**
 * Guarda una lista de laptops en la base de datos.
 */
export async function saveLaptops(
  laptops: LaptopData[],
  sourceId: string,
  scrapingId: number | null = null
): Promise<{ success: boolean; count: number; error?: string }> {
  try {
    if (!laptops || laptops.length === 0) {
      return { success: true, count: 0 };
    }

    // Transformar laptops al formato de la base de datos
    const laptopsToInsert = transformLaptopsToDbFormat(laptops, sourceId, scrapingId);

    // Insertar en la base de datos
    const { error } = await getSupabaseClient()
      .from(SUPABASE_CONFIG.tables.laptopListings)
      .insert(laptopsToInsert);

    if (error) {
      logger.error(`Error al guardar laptops en la base de datos: ${error.message}`);
      return { success: false, count: 0, error: error.message };
    }

    logger.info(`Guardadas ${laptops.length} laptops en la base de datos`);
    return { success: true, count: laptops.length };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(`Error inesperado al guardar laptops: ${errorMessage}`);
    return { success: false, count: 0, error: errorMessage };
  }
}

/**
 * Obtiene las laptops de la base de datos.
 */
export async function getLaptops(limit: number = 100): Promise<{ success: boolean; laptops: DbLaptopListing[]; error?: string }> {
  try {
    const { data, error } = await getSupabaseClient()
      .from(SUPABASE_CONFIG.tables.laptopListings)
      .select('*')
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      logger.error(`Error al obtener laptops de la base de datos: ${error.message}`);
      return { success: false, laptops: [], error: error.message };
    }

    return { success: true, laptops: data || [] };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(`Error inesperado al obtener laptops: ${errorMessage}`);
    return { success: false, laptops: [], error: errorMessage };
  }
}

/**
 * Elimina laptops de una fuente específica.
 */
export async function deleteLaptopsBySource(sourceId: string): Promise<{ success: boolean; count: number; error?: string }> {
  try {
    const { data, error } = await getSupabaseClient()
      .from(SUPABASE_CONFIG.tables.laptopListings)
      .delete()
      .eq('source_id', sourceId)
      .select('id');

    if (error) {
      logger.error(`Error al eliminar laptops de la fuente ${sourceId}: ${error.message}`);
      return { success: false, count: 0, error: error.message };
    }

    const count = data?.length || 0;
    logger.info(`Eliminadas ${count} laptops de la fuente ${sourceId}`);
    return { success: true, count };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(`Error inesperado al eliminar laptops: ${errorMessage}`);
    return { success: false, count: 0, error: errorMessage };
  }
}
