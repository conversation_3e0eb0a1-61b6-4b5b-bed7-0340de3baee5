import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { getAvailableSources, runScrapers } from './scrapers';
import { logger } from './utils/logger';
import { initializeConfigs } from './config';
import { initializeDatabase, saveLaptops } from './services/database-service';

// Cargar variables de entorno
dotenv.config();

// Inicializar servicios
(async () => {
  try {
    // Inicializar base de datos
    initializeDatabase();

    // Inicializar configuraciones de scrapers
    await initializeConfigs();
    logger.info('Servicios inicializados correctamente');
  } catch (error) {
    logger.error(`Error al inicializar servicios: ${error instanceof Error ? error.message : String(error)}`);
  }
})();

// Crear aplicación Express
const app = express();
const port = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());

// Ruta para verificar el estado del servicio
app.get('/api/status', (_req, res) => {
  res.json({
    status: 'online',
    version: '1.0.0',
    timestamp: new Date().toISOString()
  });
});

// Ruta para obtener las fuentes disponibles
app.get('/api/sources', (_req, res) => {
  const sources = getAvailableSources();
  res.json({ sources });
});

// Ruta principal para iniciar el scraping
app.post('/api/scrape', async (req, res) => {
  try {
    const { sources = [] } = req.body;

    // Validar fuentes
    if (!Array.isArray(sources) || sources.length === 0) {
      return res.status(400).json({
        error: 'Se requiere al menos una fuente para el scraping'
      });
    }

    // Iniciar el proceso de scraping
    logger.info(`Iniciando scraping para ${sources.length} fuentes: ${sources.join(', ')}`);

    // Ejecutar scrapers
    const scrapingResults = await runScrapers(sources);

    // Guardar resultados en Supabase
    let successCount = 0;
    let failureCount = 0;
    let totalItems = 0;

    for (const result of scrapingResults) {
      if (result.success && result.laptops.length > 0) {
        try {
          // Guardar laptops en la base de datos
          const saveResult = await saveLaptops(
            result.laptops,
            result.sourceId,
            result.scrapingId
          );

          if (!saveResult.success) {
            logger.error(`Error al guardar laptops de ${result.sourceId}: ${saveResult.error}`);
            failureCount++;
          } else {
            logger.info(`Guardadas ${saveResult.count} laptops de ${result.sourceId}`);
            successCount++;
            totalItems += saveResult.count;
          }
        } catch (error) {
          logger.error(`Error al procesar resultados de ${result.sourceId}: ${error instanceof Error ? error.message : String(error)}`);
          failureCount++;
        }
      } else if (!result.success) {
        failureCount++;
      }
    }

    // Devolver resultados
    res.json({
      results: scrapingResults.map(result => ({
        sourceId: result.sourceId,
        count: result.laptops.length,
        success: result.success,
        error: result.error
      })),
      successCount,
      failureCount,
      totalItems
    });
  } catch (error) {
    logger.error(`Error en el endpoint de scraping: ${error instanceof Error ? error.message : String(error)}`);
    res.status(500).json({
      error: 'Error interno del servidor',
      message: error instanceof Error ? error.message : String(error)
    });
  }
});

// Iniciar servidor
app.listen(port, () => {
  logger.info(`Servidor de scraping iniciado en el puerto ${port}`);
});