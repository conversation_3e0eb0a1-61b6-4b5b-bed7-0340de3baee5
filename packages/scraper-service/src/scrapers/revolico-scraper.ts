/**
 * Scraper específico para Revolico.com utilizando su API GraphQL.
 * Basado en el reporte técnico y análisis de la API.
 *
 * Este scraper implementa:
 * - Filtrado por fecha de publicación configurable
 * - Rango de precios dinámico y configurable
 * - Extracción de especificaciones técnicas mediante análisis de texto
 * - Manejo de errores y reintentos
 *
 * @see docs/Reporte Técnico Interacción con la API de Revolico.com.md
 */
import axios, { AxiosResponse } from 'axios';
import { BaseScraper } from './base-scraper';
import {
  ScraperConfig,
  LaptopData,
  Selectors,
  CpuSpecification,
  RamSpecification,
  StorageSpecification,
  DisplaySpecification,
  GpuSpecification
} from '../types';
import { logger } from '../utils/logger';
import { normalizeLaptopData } from '../utils/data-transformer';
import { withRetry } from '../utils/retry';

/**
 * Interfaz para las especificaciones extraídas del texto de descripción
 */
interface ExtractedSpecs {
  brand: string | null;
  cpu: string | null;
  cores: number | null;
  threads: number | null;
  ram: number | null;
  ramType: string | null;
  storage: number | null;
  storageType: string | null;
  display: number | null;
  resolution: string | null;
  displayType: string | null;
  gpu: string | null;
  gpuType: string | null;
  vram: number | null;
  battery?: {
    capacity_wh?: number;
    life_hours?: number;
  } | null;
  weight_kg?: number | null;
}

/**
 * Interfaz para las variables de la consulta GraphQL
 */
interface GraphQLVariables {
  subcategorySlug: string;
  hasImage: boolean;
  provinceSlug: string;
  page: number;
  pageLength: number;
  price: {
    currency: string;
    gte: number;
    lte: number;
  };
  sort: Array<{
    order: string;
    field: string;
  }>;
  [key: string]: unknown;
}

export class RevolicoScraper extends BaseScraper {
  private apiUrl: string;
  private initialized: boolean = false;
  private dateLimit: Date | null = null;

  constructor(config: ScraperConfig) {
    super(config);
    // Inicializar apiUrl desde la configuración o usar valor por defecto
    this.apiUrl = config.selectors.apiUrl || 'https://graphql-api.revolico.app/';
  }

  async initialize(): Promise<void> {
    logger.info('Inicializando RevolicoScraper');
    // No necesitamos inicializar Puppeteer para este scraper ya que usamos la API GraphQL directamente
    this.initialized = true;
  }

  async scrapeAllPages(): Promise<LaptopData[]> {
    if (!this.initialized) {
      await this.initialize();
    }

    logger.info('Iniciando scraping de Revolico');
    const allLaptops: LaptopData[] = [];
    let page = 1;
    let hasNextPage = true;

    while (hasNextPage) {
      try {
        logger.info(`Scraping página ${page} de Revolico`);
        const { laptops, hasMore } = await this.scrapePage(page);

        if (laptops.length === 0) {
          hasNextPage = false;
        } else {
          allLaptops.push(...laptops);

          // Verificar si hay más páginas
          hasNextPage = hasMore;

          // Avanzar a la siguiente página
          page++;

          // Implementar un retraso para evitar sobrecargar la API
          if (hasNextPage) {
            const delay = this.config.rateLimit ? (this.config.rateLimit.period / this.config.rateLimit.requests) : 1000;
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }
      } catch (error) {
        logger.error(`Error al scrapear página ${page} de Revolico: ${error instanceof Error ? error.message : String(error)}`);
        hasNextPage = false;
      }
    }

    logger.info(`Scraping de Revolico completado. Se encontraron ${allLaptops.length} laptops`);
    return allLaptops;
  }

  /**
   * Obtiene una página de resultados de la API GraphQL de Revolico
   *
   * @param page Número de página a obtener
   * @returns Objeto con los datos de laptops encontrados y si hay más páginas
   */
  async scrapePage(page: number): Promise<{ laptops: LaptopData[], hasMore: boolean }> {
    try {
      // Preparar variables para la consulta GraphQL
      const variables: GraphQLVariables = {
        subcategorySlug: this.config.selectors.subcategorySlug || 'computadoras_laptop',
        hasImage: this.config.selectors.hasImage !== undefined ? this.config.selectors.hasImage : true,
        provinceSlug: this.config.selectors.provinceSlug || 'la-habana',
        page: page,
        pageLength: this.config.selectors.pageLength || 20,
        price: {
          currency: 'USD',
          gte: 800,
          lte: 2000
        },
        sort: [
          {
            order: 'desc',
            field: 'updated_on_to_order'
          }
        ]
      };

      // Configurar el rango de precios
      if (this.config.selectors.price) {
        variables.price = {
          currency: this.config.selectors.price.currency || 'USD',
          gte: this.config.selectors.price.gte || 800,
          lte: this.config.selectors.price.lte || 2000
        };
      } else {
        variables.price = {
          currency: 'USD',
          gte: 800,
          lte: 2000
        };
      }

      // Configurar el ordenamiento
      if (this.config.selectors.sort && Array.isArray(this.config.selectors.sort)) {
        variables.sort = this.config.selectors.sort;
      } else {
        variables.sort = [
          {
            order: 'desc',
            field: 'updated_on_to_order'
          }
        ];
      }

      // Aplicar filtro de fecha si está habilitado
      if (this.config.selectors.dateFilter && this.config.selectors.dateFilter.enabled) {
        const daysBack = this.config.selectors.dateFilter.daysBack || 7;
        const dateLimit = new Date();
        dateLimit.setDate(dateLimit.getDate() - daysBack);

        // Añadir filtro de fecha a la consulta
        // Nota: La API de Revolico no tiene un filtro directo por fecha, pero podemos filtrar
        // los resultados después de recibirlos basándonos en el campo updatedOnToOrder
        logger.info(`Aplicando filtro de fecha: anuncios de los últimos ${daysBack} días`);

        // Guardar el límite de fecha para filtrar los resultados después
        this.dateLimit = dateLimit;
      } else {
        // Si el filtro de fecha no está habilitado, no aplicamos filtro
        this.dateLimit = null;
      }

      // Consulta GraphQL para buscar laptops en Revolico según la configuración
      const searchData = [{
        operationName: 'AdsSearch',
        variables,
        query: `query AdsSearch($category: ID, $subcategory: ID, $contains: String, $price: BasePriceFilterInput, $sort: [adsPerPageSort], $hasImage: Boolean, $categorySlug: String, $subcategorySlug: String, $page: Int, $provinceSlug: String, $municipalitySlug: String, $pageLength: Int) {
          adsPerPage(
            category: $category
            subcategory: $subcategory
            contains: $contains
            price: $price
            hasImage: $hasImage
            sort: $sort
            categorySlug: $categorySlug
            subcategorySlug: $subcategorySlug
            page: $page
            provinceSlug: $provinceSlug
            municipalitySlug: $municipalitySlug
            pageLength: $pageLength
          ) {
            pageInfo {
              startCursor
              endCursor
              hasNextPage
              hasPreviousPage
              pageCount
              __typename
            }
            edges {
              node {
                id
                title
                price
                currency
                permalink
                imagesCount
                updatedOnToOrder
                description
                shortDescription
                isPromoted
                provinceId
                municipalityId
                mainImage {
                  gcsKey
                  __typename
                }
                viewCount
                __typename
              }
              __typename
            }
            meta {
              total
              __typename
            }
            __typename
          }
        }`
      }];

      // Realizar la solicitud a la API GraphQL con reintentos
      const response = await withRetry<AxiosResponse>(
        async () => {
          // Añadir más headers para evitar el error 403
          return await axios.post(this.apiUrl, searchData, {
            headers: {
              'Content-Type': 'application/json',
              'Origin': 'https://www.revolico.com',
              'Referer': 'https://www.revolico.com/',
              'User-Agent': this.getRandomUserAgent(),
              'Accept': 'application/json, text/plain, */*',
              'Accept-Language': 'es-ES,es;q=0.9,en;q=0.8',
              'Sec-Fetch-Dest': 'empty',
              'Sec-Fetch-Mode': 'cors',
              'Sec-Fetch-Site': 'cross-site',
              'Connection': 'keep-alive',
              'Cache-Control': 'no-cache',
              'Pragma': 'no-cache'
            },
            timeout: this.config.timeout || 30000
          });
        },
        this.config.retryConfig || {
          maxRetries: 3,
          initialDelay: 1000,
          maxDelay: 5000,
          factor: 2
        },
        (error, attempt) => {
          logger.warn(`Error en intento ${attempt} al consultar la API de Revolico: ${error.message}. Reintentando...`);
        }
      );

      // Verificar si hay errores en la respuesta GraphQL
      if (response.data && response.data[0] && response.data[0].errors) {
        const errorMessages = response.data[0].errors.map((e: { message: string }) => e.message).join(', ');
        throw new Error(`Error en la respuesta GraphQL: ${errorMessages}`);
      }

      // Verificar si la respuesta es válida
      if (!response.data || !response.data[0] || !response.data[0].data || !response.data[0].data.adsPerPage) {
        logger.error('Respuesta de API de Revolico inválida o vacía');
        return { laptops: [], hasMore: false };
      }

      const searchResults = response.data[0].data.adsPerPage;
      const hasNextPage = searchResults.pageInfo.hasNextPage;
      const laptops: LaptopData[] = [];

      // Procesar cada anuncio
      for (const edge of searchResults.edges) {
        const node = edge.node;

        try {
          // Aplicar filtro de fecha si está configurado
          if (this.dateLimit && node.updatedOnToOrder) {
            const updatedDate = new Date(node.updatedOnToOrder);
            // Saltar este anuncio si es más antiguo que el límite de fecha
            if (updatedDate < this.dateLimit) {
              logger.debug(`Saltando anuncio ${node.id} por ser más antiguo que el límite de fecha`);
              continue;
            }
          }

          // Extraer especificaciones de la descripción
          const specs = this.extractSpecsFromDescription(node.description || node.shortDescription || '');

          // Crear objeto LaptopData
          const laptopData: Partial<LaptopData> = {
            id: node.id,
            name: node.title,
            brand: specs.brand || 'Desconocida',
            model_name: node.title,
            price: parseFloat(node.price) || 0,
            currency: node.currency || 'USD',
            specifications: {
              cpu: {
                name: specs.cpu || 'No especificado',
                cores: specs.cores || undefined,
                threads: specs.threads || undefined,
                manufacturer: this.detectCpuManufacturer(specs.cpu || '')
              },
              ram: {
                size: specs.ram || 0,
                type: specs.ramType || 'No especificado',
                speed: undefined
              },
              storage: {
                size: specs.storage || 0,
                type: specs.storageType || 'No especificado',
                interface: this.detectStorageInterface(specs.storageType || '')
              },
              display: {
                size: specs.display || undefined,
                resolution: specs.resolution || undefined,
                type: specs.displayType || undefined,
                refresh_rate: this.extractRefreshRate(node.description || '')
              },
              gpu: {
                name: specs.gpu || 'No especificado',
                type: specs.gpuType || 'Integrada',
                vram: specs.vram || undefined,
                is_discrete: specs.gpuType === 'Dedicada'
              },
              physical: specs.weight_kg ? {
                weight_kg: specs.weight_kg
              } : undefined,
              battery: specs.battery || undefined
            },
            inStock: true,
            imageUrl: node.mainImage?.gcsKey ? `https://img.revolico.com/${node.mainImage.gcsKey}` : '',
            description: node.description || node.shortDescription || '',
            url: `https://www.revolico.com/item/${node.id}`,
            source_url: `https://www.revolico.com/item/${node.id}`,
            posted_date: node.updatedOnToOrder,
            seller: 'Vendedor en Revolico',
            is_available: true
          };

          laptops.push(normalizeLaptopData(laptopData));
        } catch (error) {
          logger.error(`Error al procesar anuncio ${node.id}: ${error instanceof Error ? error.message : String(error)}`);
        }
      }

      return { laptops, hasMore: hasNextPage };
    } catch (error) {
      logger.error(`Error al scrapear página ${page} de Revolico: ${error instanceof Error ? error.message : String(error)}`);
      return { laptops: [], hasMore: false };
    }
  }

  /**
   * Extrae especificaciones técnicas del texto de descripción utilizando expresiones regulares
   *
   * @param description Texto de descripción del anuncio
   * @returns Especificaciones extraídas del texto
   */
  extractSpecsFromDescription(description: string): ExtractedSpecs {
    const specs: ExtractedSpecs = {
      brand: null,
      cpu: null,
      cores: null,
      threads: null,
      ram: null,
      ramType: null,
      storage: null,
      storageType: null,
      display: null,
      resolution: null,
      displayType: null,
      gpu: null,
      gpuType: null,
      vram: null
    };

    // Normalizar texto para búsqueda más fácil
    const normalizedDesc = description.toLowerCase().replace(/[\n\r]+/g, ' ');

    // Detectar marca
    const brandRegex = /\b(hp|dell|lenovo|asus|acer|msi|apple|toshiba|samsung|huawei)\b/i;
    const brandMatch = normalizedDesc.match(brandRegex);
    if (brandMatch) {
      specs.brand = brandMatch[0].charAt(0).toUpperCase() + brandMatch[0].slice(1);
    }

    // Detectar CPU
    const cpuRegex = /\b(i3|i5|i7|i9|ryzen\s*\d|celeron|pentium|amd)[-\s]?\d{1,4}([-]\d{1,4}[a-z]?)?/i;
    const cpuMatch = normalizedDesc.match(cpuRegex);
    if (cpuMatch) {
      specs.cpu = cpuMatch[0];
    }

    // Detectar núcleos y threads
    const coresRegex = /\b(\d+)\s*(?:núcleos|cores)\b/i;
    const coresMatch = normalizedDesc.match(coresRegex);
    if (coresMatch) {
      specs.cores = parseInt(coresMatch[1]);
    }

    const threadsRegex = /\b(\d+)\s*(?:hilos|threads)\b/i;
    const threadsMatch = normalizedDesc.match(threadsRegex);
    if (threadsMatch) {
      specs.threads = parseInt(threadsMatch[1]);
    }

    // Detectar RAM
    const ramRegex = /\b(\d+)\s*(gb|g|gigas?)\s*(ram|ddr\d?|memoria)\b/i;
    const ramMatch = normalizedDesc.match(ramRegex);
    if (ramMatch) {
      specs.ram = parseInt(ramMatch[1]);

      // Detectar tipo de RAM
      const ramTypeRegex = /\b(ddr\d)\b/i;
      const ramTypeMatch = normalizedDesc.match(ramTypeRegex);
      if (ramTypeMatch) {
        specs.ramType = ramTypeMatch[0].toUpperCase();
      }
    }

    // Detectar almacenamiento
    const storageRegex = /\b(\d+)\s*(gb|g|tb|t)\s*(ssd|hdd|disco|almacenamiento)\b/i;
    const storageMatch = normalizedDesc.match(storageRegex);
    if (storageMatch) {
      let size = parseInt(storageMatch[1]);
      // Convertir TB a GB si es necesario
      if (storageMatch[2].toLowerCase().startsWith('t')) {
        size *= 1024;
      }
      specs.storage = size;

      // Detectar tipo de almacenamiento
      if (storageMatch[3].toLowerCase().includes('ssd')) {
        specs.storageType = 'SSD';
      } else if (storageMatch[3].toLowerCase().includes('hdd')) {
        specs.storageType = 'HDD';
      }
    }

    // Detectar pantalla
    const displayRegex = /\b(\d+(\.\d+)?)\s*(pulgadas|inch|"|'')\b/i;
    const displayMatch = normalizedDesc.match(displayRegex);
    if (displayMatch) {
      specs.display = parseFloat(displayMatch[1]);
    }

    // Detectar resolución
    const resolutionRegex = /\b(\d+\s*x\s*\d+)\b/i;
    const resolutionMatch = normalizedDesc.match(resolutionRegex);
    if (resolutionMatch) {
      specs.resolution = resolutionMatch[0];
    }

    // Detectar GPU
    const gpuRegex = /\b(nvidia|geforce|gtx|rtx|radeon|intel\s+iris|intel\s+uhd|amd)\b\s*(rtx|gtx)?\s*\d{3,4}(\s*ti)?\b/i;
    const gpuMatch = normalizedDesc.match(gpuRegex);
    if (gpuMatch) {
      specs.gpu = gpuMatch[0];

      // Determinar si es dedicada o integrada
      if (normalizedDesc.includes('nvidia') || normalizedDesc.includes('geforce') ||
          normalizedDesc.includes('gtx') || normalizedDesc.includes('rtx') ||
          normalizedDesc.includes('radeon')) {
        specs.gpuType = 'Dedicada';
      } else {
        specs.gpuType = 'Integrada';
      }

      // Detectar VRAM
      const vramRegex = /\b(\d+)\s*(gb|g)\s*(vram|gddr\d?)\b/i;
      const vramMatch = normalizedDesc.match(vramRegex);
      if (vramMatch) {
        specs.vram = parseInt(vramMatch[1]);
      }
    }

    return specs;
  }

  /**
   * Detecta el fabricante de la CPU basado en el nombre
   *
   * @param cpuName Nombre de la CPU
   * @returns Fabricante de la CPU
   */
  detectCpuManufacturer(cpuName: string): string | undefined {
    const normalizedName = cpuName.toLowerCase();

    if (normalizedName.includes('intel') ||
        normalizedName.includes('i3') ||
        normalizedName.includes('i5') ||
        normalizedName.includes('i7') ||
        normalizedName.includes('i9') ||
        normalizedName.includes('celeron') ||
        normalizedName.includes('pentium')) {
      return 'Intel';
    } else if (normalizedName.includes('amd') ||
               normalizedName.includes('ryzen') ||
               normalizedName.includes('athlon')) {
      return 'AMD';
    } else if (normalizedName.includes('apple') ||
               normalizedName.includes('m1') ||
               normalizedName.includes('m2') ||
               normalizedName.includes('m3')) {
      return 'Apple';
    }

    return undefined;
  }

  /**
   * Detecta la interfaz de almacenamiento basado en el tipo
   *
   * @param storageType Tipo de almacenamiento
   * @returns Interfaz de almacenamiento
   */
  detectStorageInterface(storageType: string): string | undefined {
    const normalizedType = storageType.toLowerCase();

    if (normalizedType.includes('ssd')) {
      if (normalizedType.includes('nvme')) {
        return 'NVMe';
      } else if (normalizedType.includes('sata')) {
        return 'SATA';
      } else {
        return 'SSD';
      }
    } else if (normalizedType.includes('hdd')) {
      return 'SATA';
    }

    return undefined;
  }

  /**
   * Extrae la tasa de refresco de la pantalla desde la descripción
   *
   * @param description Descripción del anuncio
   * @returns Tasa de refresco en Hz
   */
  extractRefreshRate(description: string): number | undefined {
    const normalizedDesc = description.toLowerCase();

    // Buscar patrones como "120hz", "144 hz", etc.
    const refreshRateRegex = /\b(\d+)\s*hz\b/i;
    const match = normalizedDesc.match(refreshRateRegex);

    if (match && match[1]) {
      const rate = parseInt(match[1]);
      // Validar que sea un valor razonable
      if (rate >= 30 && rate <= 360) {
        return rate;
      }
    }

    return undefined;
  }

  /**
   * Obtiene un User-Agent aleatorio para las solicitudes HTTP
   *
   * @returns User-Agent aleatorio
   */
  getRandomUserAgent(): string {
    const userAgents = [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
      'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59'
    ];

    return userAgents[Math.floor(Math.random() * userAgents.length)];
  }

  async close(): Promise<void> {
    logger.info('Cerrando RevolicoScraper');
    this.initialized = false;
    this.dateLimit = null;
  }
}
