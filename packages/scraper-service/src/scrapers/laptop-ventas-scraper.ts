import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import { BaseScraper } from './base-scraper';
import { LaptopData, ScraperConfig } from '../types';
import { logger } from '../utils/logger';
import { DataValidator } from '../utils/data-validator';

export class LaptopVentasScraper extends BaseScraper {
  constructor(config: ScraperConfig) {
    super(config);
  }

  /**
   * Scrapea todas las páginas del sitio.
   */
  async scrapeAllPages(): Promise<LaptopData[]> {
    if (!this.browser) {
      await this.initialize();
    }

    const laptops: LaptopData[] = [];
    let currentPage = 1;
    let hasNextPage = true;

    try {
      while (hasNextPage) {
        logger.info(`Scrapeando página ${currentPage} de ${this.config.name}`);

        const pageUrl = currentPage === 1
          ? this.config.baseUrl
          : `${this.config.baseUrl}?page=${currentPage}`;

        const page = await this.createPage();

        try {
          await this.navigateTo(page, pageUrl);

          // Esperar a que los productos se carguen
          const productCardSelector = this.config.selectors.productCard || '.product-card';
          await page.waitForSelector(productCardSelector, { timeout: 10000 });

          // Obtener todos los productos de la página
          const productHandles = await page.$$(productCardSelector);
          logger.info(`Encontrados ${productHandles.length} productos en la página ${currentPage}`);

          // Procesar cada producto
          for (const productHandle of productHandles) {
            try {
              const laptop = await this.extractLaptopData(page, productHandle);
              if (laptop) {
                laptops.push(laptop);
              }
            } catch (error) {
              logger.error(`Error al extraer datos de un producto: ${error instanceof Error ? error.message : String(error)}`);
            }
          }

          // Verificar si hay una página siguiente
          const nextPageButton = await page.$('a.pagination-next:not(.is-disabled)');
          hasNextPage = !!nextPageButton && currentPage < 5; // Limitar a 5 páginas por seguridad

          currentPage++;
        } finally {
          await page.close();
        }
      }
    } catch (error) {
      logger.error(`Error al scrapear ${this.config.name}: ${error instanceof Error ? error.message : String(error)}`);
    }

    logger.info(`Scraping completado para ${this.config.name}. Encontrados ${laptops.length} laptops.`);
    return laptops;
  }

  /**
   * Extrae los datos de una laptop de un elemento de producto.
   */
  private async extractLaptopData(page: Page, productHandle: ElementHandle<Element>): Promise<LaptopData | null> {
    try {
      // Extraer nombre del producto
      const productNameSelector = this.config.selectors.productName || '.product-name';
      const nameElement = await productHandle.$(productNameSelector);
      const name = await page.evaluate(el => el?.textContent?.trim() || '', nameElement);

      if (!name) {
        logger.warn('Producto sin nombre, omitiendo');
        return null;
      }

      // Extraer URL del producto
      const linkElement = await productHandle.$('a');
      const productUrl = await page.evaluate(el => el?.href || '', linkElement);

      // Extraer precio
      const productPriceSelector = this.config.selectors.productPrice || '.product-price';
      const priceElement = await productHandle.$(productPriceSelector);
      const priceText = await page.evaluate(el => el?.textContent?.trim() || '', priceElement);
      const { price, currency } = DataValidator.parsePrice(priceText);

      // Extraer imagen
      const productImageSelector = this.config.selectors.productImage || '.product-image';
      const imageElement = await productHandle.$(productImageSelector);
      const imageUrl = await page.evaluate(el => el?.getAttribute('src') || el?.getAttribute('data-src') || '', imageElement);

      // Extraer especificaciones
      const specificationsSelector = this.config.selectors.specifications || '.product-specs';
      const specsElement = await productHandle.$(specificationsSelector);
      const specsText = await page.evaluate(el => el?.textContent?.trim() || '', specsElement);

      // Extraer marca
      const brand = DataValidator.extractBrand(name);

      // Extraer disponibilidad
      let inStock = true;
      if (this.config.selectors.availability) {
        const availabilitySelector = this.config.selectors.availability;
        const availabilityElement = await productHandle.$(availabilitySelector);
        const availabilityText = await page.evaluate(el => el?.textContent?.trim() || '', availabilityElement);
        inStock = !availabilityText.toLowerCase().includes('agotado') &&
                 !availabilityText.toLowerCase().includes('no disponible');
      }

      // Parsear especificaciones
      const cpuInfo = DataValidator.parseCpu(specsText);
      const ramInfo = DataValidator.parseRam(specsText);
      const storageInfo = DataValidator.parseStorage(specsText);
      const displayInfo = DataValidator.parseDisplay(specsText);
      const gpuInfo = DataValidator.parseGpu(specsText);

      // Generar ID único
      const id = DataValidator.generateId({ name, brand, price });

      return {
        id,
        name,
        brand,
        price,
        currency,
        specifications: {
          cpu: cpuInfo,
          ram: ramInfo,
          storage: storageInfo,
          display: displayInfo,
          gpu: gpuInfo
        },
        inStock,
        imageUrl,
        description: specsText,
        url: productUrl,
        source_url: this.config.baseUrl
      };
    } catch (error) {
      logger.error(`Error al extraer datos de laptop: ${error instanceof Error ? error.message : String(error)}`);
      return null;
    }
  }
}