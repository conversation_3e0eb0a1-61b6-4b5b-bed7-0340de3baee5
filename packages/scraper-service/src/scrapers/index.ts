import { SCRAPER_CONFIGS, registerScrapingStart, updateScrapingStatus } from '../config';
import { BaseScraper } from './base-scraper';
import { LaptopVentasScraper } from './laptop-ventas-scraper';
import { RevolicoScraper } from './revolico-scraper';
import { ScraperConfig, LaptopData } from '../types';
import { logger } from '../utils/logger';

// Registro de implementaciones de scrapers
const SCRAPER_IMPLEMENTATIONS: Record<string, new (config: ScraperConfig) => BaseScraper> = {
  'laptop-ventas': LaptopVentasScraper,
  'revolico': RevolicoScraper,
  // Aquí se pueden registrar más implementaciones de scrapers
  // 'gaminglaptop': GamingLaptopScraper,
};

/**
 * Crea una instancia de scraper basada en el ID de la fuente.
 */
export function createScraper(sourceId: string): BaseScraper | null {
  const config = SCRAPER_CONFIGS.find(config => config.id === sourceId);

  if (!config) {
    logger.error(`No se encontró configuración para el scraper con ID: ${sourceId}`);
    return null;
  }

  // Buscar una implementación específica para este sourceId
  const ScraperImplementation = SCRAPER_IMPLEMENTATIONS[sourceId];

  if (ScraperImplementation) {
    logger.info(`Usando implementación específica para ${sourceId}`);
    return new ScraperImplementation(config);
  }

  // Si no hay una implementación específica, usar LaptopVentasScraper como fallback
  logger.warn(`No se encontró una implementación específica para ${sourceId}, usando implementación genérica`);
  return new LaptopVentasScraper(config);
}

/**
 * Devuelve la lista de fuentes disponibles.
 */
export function getAvailableSources(): {
  id: string;
  name: string;
  baseUrl: string;
}[] {
  return SCRAPER_CONFIGS.map(config => ({
    id: config.id,
    name: config.name,
    baseUrl: config.baseUrl
  }));
}

/**
 * Ejecuta el scraping para una lista de fuentes.
 */
export async function runScrapers(sourceIds: string[]): Promise<{
  sourceId: string;
  laptops: LaptopData[];
  success: boolean;
  error?: string;
  scrapingId?: number | null;
}[]> {
  const results: {
    sourceId: string;
    laptops: LaptopData[];
    success: boolean;
    error?: string;
    scrapingId?: number | null;
  }[] = [];

  for (const sourceId of sourceIds) {
    logger.info(`Iniciando scraping para la fuente: ${sourceId}`);

    // Registrar el inicio del scraping en la base de datos
    const scrapingId = await registerScrapingStart(sourceId);

    const scraper = createScraper(sourceId);

    if (!scraper) {
      const errorMessage = `Scraper no encontrado para la fuente: ${sourceId}`;
      logger.error(errorMessage);

      // Actualizar el estado en la base de datos
      if (scrapingId) {
        await updateScrapingStatus(scrapingId, 'failed', 0, errorMessage);
      }

      results.push({
        sourceId,
        laptops: [],
        success: false,
        error: errorMessage,
        scrapingId
      });
      continue;
    }

    try {
      await scraper.initialize();
      const laptops = await scraper.scrapeAllPages();

      // Actualizar el estado en la base de datos
      if (scrapingId) {
        await updateScrapingStatus(scrapingId, 'completed', laptops.length);
      }

      results.push({
        sourceId,
        laptops,
        success: true,
        scrapingId
      });

      logger.info(`Scraping completado para la fuente ${sourceId}: ${laptops.length} laptops encontradas`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`Error durante el scraping de la fuente ${sourceId}: ${errorMessage}`);

      // Actualizar el estado en la base de datos
      if (scrapingId) {
        await updateScrapingStatus(scrapingId, 'failed', 0, errorMessage);
      }

      results.push({
        sourceId,
        laptops: [],
        success: false,
        error: errorMessage,
        scrapingId
      });
    } finally {
      await scraper.close();
    }
  }

  return results;
}
