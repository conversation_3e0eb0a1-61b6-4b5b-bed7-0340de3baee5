import { RevolicoScraper } from '../revolico-scraper';
import axios from 'axios';
import { ScraperConfig } from '../../types';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock logger
jest.mock('../../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn()
  }
}));

// Mock data transformer
jest.mock('../../utils/data-transformer', () => ({
  normalizeLaptopData: jest.fn((data) => data)
}));

describe('RevolicoScraper', () => {
  let scraper: RevolicoScraper;
  let mockConfig: ScraperConfig;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Create mock config
    mockConfig = {
      id: 'revolico',
      name: '<PERSON><PERSON><PERSON>',
      baseUrl: 'https://www.revolico.com',
      selectors: {
        apiUrl: 'https://graphql-api.revolico.app/',
        subcategorySlug: 'computadoras_laptop',
        price: {
          currency: 'USD',
          gte: 800,
          lte: 2000,
          configurable: true,
          defaultRange: {
            min: 800,
            max: 2000
          }
        },
        dateFilter: {
          enabled: true,
          field: 'updatedOnToOrder',
          daysBack: 7,
          configurable: true
        },
        sort: [
          {
            order: 'desc',
            field: 'updated_on_to_order'
          }
        ],
        hasImage: true,
        provinceSlug: 'la-habana',
        pageLength: 20,
        requiredFields: [
          'id',
          'title',
          'price',
          'currency',
          'permalink',
          'shortDescription',
          'updatedOnToOrder'
        ]
      },
      rateLimit: {
        requests: 5,
        period: 10000
      },
      timeout: 30000,
      retryConfig: {
        maxRetries: 3,
        initialDelay: 1000,
        maxDelay: 5000,
        factor: 2
      }
    };

    // Create scraper instance
    scraper = new RevolicoScraper(mockConfig);
  });

  describe('scrapePage', () => {
    it('should filter results by date when dateFilter is enabled', async () => {
      // Setup mock response
      const mockResponse = {
        data: [
          {
            data: {
              adsPerPage: {
                pageInfo: {
                  hasNextPage: false,
                  hasPreviousPage: false,
                  pageCount: 1,
                  __typename: 'PageInfo'
                },
                edges: [
                  {
                    node: {
                      id: '12345',
                      title: 'Laptop HP i5 16GB RAM',
                      price: '1200',
                      currency: 'USD',
                      permalink: 'laptop-hp-i5',
                      shortDescription: 'Laptop HP con i5, 16GB RAM, 512GB SSD',
                      updatedOnToOrder: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(), // 10 days old (should be filtered out)
                      description: 'Laptop HP con procesador i5, 16GB RAM DDR4, 512GB SSD, pantalla 15.6"',
                      mainImage: { gcsKey: 'image1.jpg', __typename: 'Image' },
                      __typename: 'Ad'
                    },
                    __typename: 'AdEdge'
                  },
                  {
                    node: {
                      id: '67890',
                      title: 'Laptop Dell i7 32GB RAM',
                      price: '1500',
                      currency: 'USD',
                      permalink: 'laptop-dell-i7',
                      shortDescription: 'Laptop Dell con i7, 32GB RAM, 1TB SSD',
                      updatedOnToOrder: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days old (should be included)
                      description: 'Laptop Dell con procesador i7, 32GB RAM DDR4, 1TB SSD, pantalla 15.6"',
                      mainImage: { gcsKey: 'image2.jpg', __typename: 'Image' },
                      __typename: 'Ad'
                    },
                    __typename: 'AdEdge'
                  }
                ],
                meta: {
                  total: 2,
                  __typename: 'Meta'
                },
                __typename: 'AdConnection'
              }
            }
          }
        ]
      };

      mockedAxios.post.mockResolvedValueOnce(mockResponse);

      // Call scrapePage
      const result = await scraper.scrapePage(1);

      // Verify that only one laptop is returned (the one that's 3 days old)
      expect(result.laptops.length).toBe(1);
      expect(result.laptops[0].id).toBe('67890');
      expect(result.hasMore).toBe(false);
    });

    it('should not filter results by date when dateFilter is disabled', async () => {
      // Disable date filter
      if (mockConfig.selectors.dateFilter) {
        mockConfig.selectors.dateFilter.enabled = false;
      }
      scraper = new RevolicoScraper(mockConfig);

      // Setup mock response (same as previous test)
      const mockResponse = {
        data: [
          {
            data: {
              adsPerPage: {
                pageInfo: {
                  hasNextPage: false,
                  hasPreviousPage: false,
                  pageCount: 1,
                  __typename: 'PageInfo'
                },
                edges: [
                  {
                    node: {
                      id: '12345',
                      title: 'Laptop HP i5 16GB RAM',
                      price: '1200',
                      currency: 'USD',
                      permalink: 'laptop-hp-i5',
                      shortDescription: 'Laptop HP con i5, 16GB RAM, 512GB SSD',
                      updatedOnToOrder: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(), // 10 days old
                      description: 'Laptop HP con procesador i5, 16GB RAM DDR4, 512GB SSD, pantalla 15.6"',
                      mainImage: { gcsKey: 'image1.jpg', __typename: 'Image' },
                      __typename: 'Ad'
                    },
                    __typename: 'AdEdge'
                  },
                  {
                    node: {
                      id: '67890',
                      title: 'Laptop Dell i7 32GB RAM',
                      price: '1500',
                      currency: 'USD',
                      permalink: 'laptop-dell-i7',
                      shortDescription: 'Laptop Dell con i7, 32GB RAM, 1TB SSD',
                      updatedOnToOrder: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days old
                      description: 'Laptop Dell con procesador i7, 32GB RAM DDR4, 1TB SSD, pantalla 15.6"',
                      mainImage: { gcsKey: 'image2.jpg', __typename: 'Image' },
                      __typename: 'Ad'
                    },
                    __typename: 'AdEdge'
                  }
                ],
                meta: {
                  total: 2,
                  __typename: 'Meta'
                },
                __typename: 'AdConnection'
              }
            }
          }
        ]
      };

      mockedAxios.post.mockResolvedValueOnce(mockResponse);

      // Call scrapePage
      const result = await scraper.scrapePage(1);

      // Verify that both laptops are returned
      expect(result.laptops.length).toBe(2);
      expect(result.hasMore).toBe(false);
    });

    it('should apply price filtering according to configuration', async () => {
      // Setup mock response with different prices
      const mockResponse = {
        data: [
          {
            data: {
              adsPerPage: {
                pageInfo: {
                  hasNextPage: false,
                  hasPreviousPage: false,
                  pageCount: 1,
                  __typename: 'PageInfo'
                },
                edges: [
                  {
                    node: {
                      id: '12345',
                      title: 'Laptop HP i5 16GB RAM',
                      price: '1200',
                      currency: 'USD',
                      permalink: 'laptop-hp-i5',
                      shortDescription: 'Laptop HP con i5, 16GB RAM, 512GB SSD',
                      updatedOnToOrder: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
                      description: 'Laptop HP con procesador i5, 16GB RAM DDR4, 512GB SSD, pantalla 15.6"',
                      mainImage: { gcsKey: 'image1.jpg', __typename: 'Image' },
                      __typename: 'Ad'
                    },
                    __typename: 'AdEdge'
                  }
                ],
                meta: {
                  total: 1,
                  __typename: 'Meta'
                },
                __typename: 'AdConnection'
              }
            }
          }
        ]
      };

      mockedAxios.post.mockResolvedValueOnce(mockResponse);

      // Call scrapePage
      const result = await scraper.scrapePage(1);

      // Verify that the price filter was applied in the request
      expect(mockedAxios.post).toHaveBeenCalledTimes(1);
      const requestData = mockedAxios.post.mock.calls[0][1] as { variables: { price: { gte: number, lte: number, currency: string } } }[];
      expect(requestData[0]?.variables?.price).toBeDefined();
      expect(requestData[0]?.variables?.price?.gte).toBe(800);
      expect(requestData[0]?.variables?.price?.lte).toBe(2000);
      expect(requestData[0]?.variables?.price?.currency).toBe('USD');
    });

    it('should handle API errors gracefully', async () => {
      // Mock a network error
      mockedAxios.post.mockRejectedValueOnce(new Error('Network Error'));

      // Call scrapePage
      const result = await scraper.scrapePage(1);

      // Verify that an empty result is returned
      expect(result.laptops).toEqual([]);
      expect(result.hasMore).toBe(false);
    });

    it('should handle invalid API responses gracefully', async () => {
      // Mock an invalid response
      const mockInvalidResponse = {
        data: [{ data: null }]
      };

      mockedAxios.post.mockResolvedValueOnce(mockInvalidResponse);

      // Call scrapePage
      const result = await scraper.scrapePage(1);

      // Verify that an empty result is returned
      expect(result.laptops).toEqual([]);
      expect(result.hasMore).toBe(false);
    });
  });

  describe('extractSpecsFromDescription', () => {
    it('should extract CPU information correctly', () => {
      const description = 'Laptop con procesador Intel Core i7-10750H, 6 núcleos, 12 hilos';
      const specs = scraper.extractSpecsFromDescription(description);

      expect(specs.cpu).toContain('i7');
      expect(specs.cores).toBe(6);
      expect(specs.threads).toBe(12);
    });

    it('should extract RAM information correctly', () => {
      const description = 'Laptop con 16GB RAM DDR4';
      const specs = scraper.extractSpecsFromDescription(description);

      expect(specs.ram).toBe(16);
      expect(specs.ramType).toBe('DDR4');
    });

    it('should extract storage information correctly', () => {
      const description = 'Laptop con 512GB SSD y 1TB HDD';
      const specs = scraper.extractSpecsFromDescription(description);

      // Note: Currently the implementation only extracts the first storage mention
      expect(specs.storage).toBe(512);
      expect(specs.storageType).toBe('SSD');
    });

    it('should extract display information correctly', () => {
      const description = 'Laptop con pantalla de 15.6 pulgadas, resolución 1920 x 1080';
      const specs = scraper.extractSpecsFromDescription(description);

      expect(specs.display).toBe(15.6);
      expect(specs.resolution).toBe('1920 x 1080');
    });

    it('should extract GPU information correctly', () => {
      const description = 'Laptop con tarjeta gráfica NVIDIA GeForce RTX 3060 6GB VRAM';
      const specs = scraper.extractSpecsFromDescription(description);

      expect(specs.gpu?.toLowerCase() || '').toContain('rtx 3060');
      expect(specs.gpuType).toBe('Dedicada');
      expect(specs.vram).toBe(6);
    });
  });

  describe('helper methods', () => {
    it('should detect CPU manufacturer correctly', () => {
      expect(scraper.detectCpuManufacturer('Intel Core i7-10750H')).toBe('Intel');
      expect(scraper.detectCpuManufacturer('AMD Ryzen 7 5800H')).toBe('AMD');
      expect(scraper.detectCpuManufacturer('Apple M1 Pro')).toBe('Apple');
      expect(scraper.detectCpuManufacturer('Unknown CPU')).toBeUndefined();
    });

    it('should detect storage interface correctly', () => {
      expect(scraper.detectStorageInterface('SSD NVMe')).toBe('NVMe');
      expect(scraper.detectStorageInterface('SSD SATA')).toBe('SATA');
      expect(scraper.detectStorageInterface('SSD')).toBe('SSD');
      expect(scraper.detectStorageInterface('HDD')).toBe('SATA');
      expect(scraper.detectStorageInterface('Unknown')).toBeUndefined();
    });

    it('should extract refresh rate correctly', () => {
      expect(scraper.extractRefreshRate('Pantalla 144Hz IPS')).toBe(144);
      expect(scraper.extractRefreshRate('Monitor de 60 hz')).toBe(60);
      expect(scraper.extractRefreshRate('No refresh rate info')).toBeUndefined();
    });
  });
});
