# Servicio de Scraping para LLM Laptop Lens

Este servicio proporciona una API para realizar web scraping de tiendas de laptops y almacenar los resultados en Supabase.

## Características

- Scraping de múltiples fuentes de datos
- Arquitectura modular y extensible
- Manejo de errores y reintentos
- Limitación de velocidad para evitar bloqueos
- Rotación de User-Agents
- Integración con Supabase
- Configuración centralizada en base de datos
- Registro de historial de scraping
- Transformación de datos para normalización

## Requisitos

- Node.js v18+
- npm o yarn
- Supabase (para almacenamiento de datos)

## Instalación

```bash
# Clonar el repositorio
git clone https://github.com/tu-usuario/llm-laptop-lens-scraper.git
cd llm-laptop-lens-scraper

# Instalar dependencias
npm install

# Configurar variables de entorno
cp .env.example .env
# Editar .env con tus configuraciones
```

## Configuración

Crea un archivo `.env` con las siguientes variables:

```env
PORT=3000
SUPABASE_URL=https://tu-proyecto.supabase.co
SUPABASE_ANON_KEY=tu-clave-anonima
LOG_LEVEL=info
```

## Uso

### Iniciar el servicio

```bash
# Desarrollo
npm run dev

# Producción
npm run build
npm start
```

### API Endpoints

#### GET /api/status

Verifica el estado del servicio.

#### GET /api/sources

Obtiene la lista de fuentes disponibles para scraping.

#### POST /api/scrape

Inicia el proceso de scraping.

Parámetros:

- `sources`: Array de IDs de fuentes a scrapear (ej. ["laptop-ventas", "gaminglaptop"])
- `forceRefresh`: Booleano que indica si se debe forzar una actualización

## Integración con Supabase Functions

Para integrar este servicio con Supabase Functions, actualiza la función `scrape-laptops` para que llame a este servicio en lugar de generar datos simulados.

## Arquitectura

El servicio está organizado en los siguientes componentes:

### Módulo de Configuración

- `src/config/index.ts`: Gestiona la configuración centralizada, cargando los ajustes desde la base de datos.
- `src/types/index.ts`: Define los tipos de datos utilizados en todo el servicio.

### Scrapers

- `src/scrapers/base-scraper.ts`: Clase base abstracta que implementa la funcionalidad común para todos los scrapers.
- `src/scrapers/index.ts`: Registro de implementaciones de scrapers y funciones para ejecutarlos.
- `src/scrapers/laptop-ventas-scraper.ts`: Implementación específica para la fuente "Laptop Ventas".

### Servicios

- `src/services/database-service.ts`: Gestiona la interacción con la base de datos Supabase.

### Utilidades

- `src/utils/data-transformer.ts`: Transforma los datos extraídos al formato de la base de datos.
- `src/utils/data-validator.ts`: Valida y normaliza los datos extraídos.
- `src/utils/logger.ts`: Sistema de registro centralizado.
- `src/utils/retry.ts`: Implementa mecanismos de reintento para operaciones propensas a fallos.

## Añadir nuevas fuentes

1. Añade la configuración en la tabla `sources` de la base de datos con los selectores adecuados.
2. Implementa un nuevo scraper en `src/scrapers/` que extienda `BaseScraper`.
3. Registra el nuevo scraper en `SCRAPER_IMPLEMENTATIONS` en `src/scrapers/index.ts`.

## Despliegue

Este servicio puede desplegarse en cualquier plataforma que soporte Node.js:

- Vercel
- Railway
- Heroku
- AWS Lambda
- Google Cloud Run
- DigitalOcean App Platform

## Licencia

MIT