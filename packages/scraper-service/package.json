{"name": "@llm-laptop-lens/scraper-service", "version": "1.0.0", "description": "Scraper service for LLM Laptop Lens", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev src/index.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint ."}, "dependencies": {"@llm-laptop-lens/common": "^1.0.0", "@supabase/supabase-js": "^2.49.4", "axios": "^1.9.0", "cors": "^2.8.5", "express": "^4.18.2", "lru-cache": "^10.2.0", "puppeteer": "^24.6.1", "winston": "^3.17.0"}, "devDependencies": {"@types/cors": "^2.8.15", "@types/express": "^4.17.21", "@types/jest": "^29.5.14", "@types/node": "^22.5.5", "fast-glob": "^3.3.2", "jest": "^29.7.0", "del": "^7.1.0", "ts-jest": "^29.3.2", "ts-node-dev": "^2.0.0", "typescript": "^5.5.3"}}