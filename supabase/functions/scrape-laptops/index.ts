
import 'https://deno.land/x/xhr@0.1.0/mod.ts';
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.0';

// Configure Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// URL del servicio de scraping
const SCRAPER_SERVICE_URL = Deno.env.get('SCRAPER_SERVICE_URL') || 'http://localhost:3000';

// CORS headers for API endpoints
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Parse request body for any parameters
    let requestParams = {};
    try {
      if (req.method === 'POST') {
        requestParams = await req.json();
      }
    } catch (error) {
      console.log('No request parameters or invalid JSON');
    }

    // Get active scraping sources from database
    const { data: dbSources, error: sourcesError } = await supabase
      .from('scraping_sources')
      .select('*')
      .eq('enabled', true);

    if (sourcesError) {
      throw new Error(`Failed to get scraping sources: ${sourcesError.message}`);
    }

    // Identify the sources to scrape (either from request or database)
    const sourcesToScrape = requestParams.sources || 
      (dbSources?.length > 0 ? dbSources.map(source => source.id) : ['laptop-ventas']);

    // Call the scraper service
    console.log(`Calling scraper service for sources: ${sourcesToScrape.join(', ')}`);
    
    const response = await fetch(`${SCRAPER_SERVICE_URL}/api/scrape`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sources: sourcesToScrape,
        forceRefresh: requestParams.forceRefresh || false
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Scraper service error: ${response.status} ${errorText}`);
    }

    const scrapingResults = await response.json();
    
    // Log the results
    console.log(`Scraping completed. Success: ${scrapingResults.successCount}, Failure: ${scrapingResults.failureCount}, Total items: ${scrapingResults.totalItems}`);

    // Return the results
    return new Response(
      JSON.stringify(scrapingResults),
      {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json',
        },
        status: 200,
      },
    );
  } catch (error) {
    console.error(`Error in scrape-laptops function: ${error.message}`);
    
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json',
        },
        status: 500,
      },
    );
  }
});
