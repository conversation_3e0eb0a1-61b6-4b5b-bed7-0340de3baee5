---
id: 767743aa-8d06-415a-8b77-20187ddc1686
---
erDiagram
    manufacturers ||--o{ brands : has
    brands ||--o{ laptops : produces
    laptops ||--o{ displays : has
    laptops ||--o{ physical_specs : has
    laptops ||--o{ laptop_cpus : has
    laptops ||--o{ laptop_gpus : has
    laptops ||--o{ laptop_ram : has
    laptops ||--o{ laptop_storage : has
    laptops ||--o{ laptop_scores : has
    laptops ||--o{ laptop_tags : has
    laptops ||--o{ laptop_llm_compatibility : has
    laptops ||--o{ laptop_os_compatibility : has
    laptops ||--o{ laptop_listings : listed_in
    
    cpus ||--o{ laptop_cpus : used_in
    gpus ||--o{ laptop_gpus : used_in
    ram_configurations ||--o{ laptop_ram : used_in
    storage_devices ||--o{ laptop_storage : used_in
    
    resolution_types ||--o{ displays : resolution
    panel_types ||--o{ displays : panel
    
    cpu_architectures ||--o{ cpus : architecture
    memory_types ||--o{ ram_configurations : memory_type
    storage_interfaces ||--o{ storage_devices : interface
    
    sources ||--o{ laptop_listings : source
    sources ||--o{ scraping_history : history
    
    llm_models ||--o{ laptop_llm_compatibility : compatible_with
    llm_models ||--o{ model_config : configured_as
    
    operating_systems ||--o{ laptop_os_compatibility : compatible_with
    
    tag_categories ||--o{ tags : categorizes
    tags ||--o{ laptop_tags : tagged_with
    
    laptop_listings ||--o{ price_history : price_tracked
    
    manufacturers {
        int id PK
        varchar name
        varchar website
        varchar country
        int founded_year
    }
    
    brands {
        int id PK
        varchar name
        varchar website
        varchar logo_url
        int manufacturer_id FK
        timestamp created_at
    }
    
    laptops {
        int id PK
        varchar model_name
        int brand_id FK
        date release_date
        text description
        varchar image_url
        boolean is_available
        numeric msrp
        timestamp created_at
        timestamp updated_at
    }
    
    displays {
        int id PK
        int laptop_id FK
        numeric size_inches
        int resolution_id FK
        int refresh_rate
        int panel_type_id FK
        boolean is_touchscreen
        int brightness_nits
        varchar color_gamut
        boolean hdr_support
        int response_time_ms
    }
    
    physical_specs {
        int id PK
        int laptop_id FK
        numeric weight_kg
        numeric height_mm
        numeric width_mm
        numeric depth_mm
        varchar material
        varchar color
        boolean has_fingerprint_reader
        boolean has_webcam
        varchar webcam_resolution
        boolean has_backlit_keyboard
    }
    
    cpus {
        int id PK
        int manufacturer_id FK
        varchar model
        varchar generation
        int cores
        int threads
        numeric base_clock_ghz
        numeric boost_clock_ghz
        int tdp_watts
        int cache_mb
        int architecture_id FK
        boolean supports_avx512
    }
    
    laptop_cpus {
        int laptop_id PK,FK
        int cpu_id PK,FK
        int performance_score
    }
    
    gpus {
        int id PK
        int manufacturer_id FK
        varchar model
        int vram_gb
        int memory_type_id FK
        int base_clock_mhz
        int boost_clock_mhz
        int tdp_watts
        boolean ray_tracing_support
        int tensor_cores
        int cuda_cores
        int compute_units
        boolean supports_dlss
        boolean supports_fsr
    }
    
    laptop_gpus {
        int laptop_id PK,FK
        int gpu_id PK,FK
        boolean is_discrete
        int performance_score
    }
    
    ram_configurations {
        int id PK
        int memory_type_id FK
        int size_gb
        int speed_mhz
        int manufacturer_id FK
        boolean is_dual_channel
        int cas_latency
    }
    
    laptop_ram {
        int laptop_id PK,FK
        int ram_configuration_id PK,FK
        int slots_used
        int max_slots
        boolean expandable
        int max_supported_gb
    }
    
    storage_devices {
        int id PK
        int interface_id FK
        int capacity_gb
        int manufacturer_id FK
        int read_speed_mbps
        int write_speed_mbps
        boolean is_nvme
        boolean has_dram_cache
    }
    
    laptop_storage {
        int laptop_id PK,FK
        int storage_id PK,FK
        boolean is_primary
        varchar slot_type
    }
    
    sources {
        int id PK
        varchar name
        varchar url
        varchar api_endpoint
        boolean is_active
        timestamp last_updated
        varchar update_frequency
        jsonb selectors
        timestamp last_scrape_attempt
        int scrape_success_count
        int scrape_failure_count
    }
    
    laptop_listings {
        int id PK
        int laptop_id FK
        int source_id FK
        numeric price
        varchar url
        boolean in_stock
        numeric shipping_cost
        numeric rating
        int reviews_count
        timestamp listing_date
        boolean free_shipping
        int estimated_delivery_days
        boolean has_warranty
        int warranty_months
        text name
        text brand
        text source_url
        jsonb specs
    }
    
    price_history {
        int id PK
        int listing_id FK
        numeric price
        timestamp recorded_at
    }
    
    llm_models {
        int id PK
        varchar name
        numeric parameters_billions
        int quantization_bits
        int min_ram_gb
        int min_vram_gb
        boolean requires_gpu
        text description
        varchar model_card_url
        timestamp created_at
        timestamp updated_at
    }
    
    model_config {
        int id PK
        int model_id FK
        varchar name
        varchar provider
        varchar version
        int context_length
        int max_tokens
        decimal cost_per_1k_tokens
        text[] capabilities
        timestamp last_updated
        text endpoint
        timestamp created_at
    }
    
    laptop_llm_compatibility {
        int laptop_id PK,FK
        int llm_id PK,FK
        int estimated_tokens_per_second
        int max_context_length
        int score
        text qualitative_assessment
        boolean can_run_offline
        int recommended_batch_size
        numeric estimated_memory_usage_gb
    }
    
    operating_systems {
        int id PK
        varchar name
        varchar version
        boolean is_linux
        boolean is_windows
        boolean is_macos
        int min_ram_gb
        int min_storage_gb
    }
    
    laptop_os_compatibility {
        int laptop_id PK,FK
        int os_id PK,FK
        boolean is_officially_supported
        boolean has_driver_issues
        text notes
    }
    
    tag_categories {
        int id PK
        varchar name
        text description
    }
    
    tags {
        int id PK
        varchar name
        int category_id FK
        text description
    }
    
    laptop_tags {
        int laptop_id PK,FK
        int tag_id PK,FK
        int relevance_score
    }
    
    laptop_scores {
        int id PK
        int laptop_id FK
        int overall_score
        int llm_performance_score
        int battery_life_score
        int build_quality_score
        int display_score
        int keyboard_score
        int performance_score
        int value_score
        int thermal_score
        int noise_score
        int portability_score
        timestamp last_evaluated_at
    }
    
    scraping_history {
        int id PK
        int source_id FK
        timestamp start_time
        timestamp end_time
        varchar status
        int items_found
        text error_message
    }