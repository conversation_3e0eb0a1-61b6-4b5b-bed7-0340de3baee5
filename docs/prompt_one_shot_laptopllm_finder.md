## LaptopLLM-Finder

Crie uma aplicação web completa chamada "LaptopLLM-Finder" que realize web scraping de sites de venda de laptops, processe os dados, e apresente uma interface de usuário intuitiva para ajudar os usuários a encontrar laptops com a melhor relação custo-benefício para executar modelos LLM open-source do Ollama. A aplicação deve ser desenvolvida utilizando Next.js (com React e TypeScript) para o frontend, Node.js para o backend, Prisma ORM com SQLite para o banco de dados, e Puppeteer/Python (com BeautifulSoup/Scrapy) para o web scraping.

**Requisitos Detalhados:**

**1. Configuração Inicial:**

- A aplicação deve ser capaz de configurar múltiplas fontes de dados (URLs base).
- URLs base iniciais: `revolico.com`, `smart-things.ola.click`, `laptop-ventas.ola.click`.
- Deve haver um mecanismo para gerenciar credenciais e tokens de acesso (se aplicável) e limites de taxa por site.

**2. Filtros de Busca:**

- Implementar filtros de busca configuráveis pelo usuário na interface:
  - **Termos principais:** `["laptop", "notebook", "portátil"]`
  - **Termos de exclusão:** `["accesorios", "partes", "repuestos"]`
  - **Categoria:** `"Laptops/Computadoras portáteis"`
  - **Rango de preços:** Configurável pelo usuário (mínimo e máximo).
  - **Antiguidade máxima:** 30 dias (para resultados de scraping).

**3. Extração de Dados (Web Scraping):**

- Para cada site, a aplicação deve usar os seguintes seletores e parâmetros:
  
  ```json
  {
    "selectores": {
      "producto": ".product-item, .item-card, .laptop-listing",
      "titulo": ".product-title, .item-name, .laptop-title",
      "precio": ".price, .item-price, .laptop-price",
      "categoria": ".category, .item-category, .laptop-category"
    },
    "paginacion": {
      "selector": ".pagination a, .next-page, .page-next",
      "limite_paginas": [5, 3, 4] // Exemplo: 5 para revolico, 3 para smart-things, 4 para laptop-ventas
    },
    "parametros": {
      "intervalo": 2000, // Milissegundos entre requisições
      "timeout": 30000, // Milissegundos para timeout da requisição
      "reintentos": 3, // Número de tentativas em caso de falha
      "user_agents": ["Mozilla/5.0...", "Chrome/91.0..."] // Lista de User-Agents para rotacionar
    }
  }
  ```

- O scraping deve ser robusto e lidar com diferentes estruturas HTML.

- A URL `laptop-ventas.ola.click` deve ser acessada via `https://laptop-ventas.ola.click/products` para os produtos.

**4. Processamento e Armazenamento de Dados:**

- **Validação:** Validar a integridade e o formato dos dados extraídos.

- **Normalização:** Normalizar preços (para uma moeda comum, se necessário) e especificações (para um formato consistente).

- **Eliminação de Duplicados:** Implementar lógica para identificar e remover entradas duplicadas.

- **Armazenamento:** Salvar os dados em um banco de dados SQLite (via Prisma ORM) com o seguinte esquema:
  
  ```json
  {
    "id": "string",
    "titulo": "string",
    "precio": "number",
    "categoria": "string",
    "url": "string",
    "fecha_extraccion": "timestamp",
    "especificacoes": "object", // JSON string ou objeto para detalhes como processador, RAM, armazenamento, GPU, etc.
    "fuente": "string" // Ex: "Revolico", "LaptopVentas"
  }
  ```

**5. Visualização Integrada (UI/UX):**

- **Dashboard Principal:** Criar um dashboard interativo que inclua:
  - Resumo das extrações (número de laptops, última atualização).
  - Tendências de preços (gráficos simples).
  - Alertas configuráveis (ex: novo laptop abaixo de X preço).
  - Filtros dinâmicos (marca, modelo, preço, especificações, etc.).
- **Navegação Fluida:** Permitir navegação intuitiva entre os módulos da aplicação.
- **Acessos Rápidos:** Implementar acessos rápidos personalizáveis para funcionalidades comuns.
- **Interface Unificada:** A UI deve permitir configurar fontes e filtros, visualizar resultados em tempo real, exportar dados, gerenciar alertas e analisar tendências.
- **Responsividade:** A interface deve ser totalmente responsiva para funcionar em diferentes tamanhos de tela (desktop, tablet, mobile).

**6. Monitoramento e Manutenção:**

- **Logs Detalhados:** Gerar logs detalhados de cada execução do scraping (sucesso, falhas, erros).
- **Métricas de Desempenho:** Coletar e exibir métricas de desempenho do scraping (tempo de execução, número de itens processados).
- **Sistema de Alertas:** Notificações para falhas no scraping ou quando novos laptops que atendam a critérios específicos são encontrados.
- **Atualização Automática de Seletores:** Considerar um mecanismo para facilitar a atualização de seletores de scraping caso as estruturas dos sites mudem (pode ser manual via interface de configuração ou um sistema mais avançado).

**Instruções de Implementação:**

- Inicie criando a estrutura básica do projeto Next.js e configure o Prisma ORM.
- Desenvolva os módulos de scraping separadamente para cada fonte.
- Implemente as APIs de backend para gerenciar o scraping, dados e progresso.
- Construa o frontend iterativamente, começando pelo dashboard e filtros.
- Garanta que a aplicação seja modular e fácil de estender.
- Priorize a estabilidade e a robustez do scraping.
