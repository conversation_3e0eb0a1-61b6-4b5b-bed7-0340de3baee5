# LLM Laptop Lens

LLM Laptop Lens es una aplicación web que ayuda a los usuarios a encontrar laptops compatibles con modelos de lenguaje (LLMs) para ejecutarlos localmente. La aplicación recopila datos de laptops de diversas fuentes, analiza sus especificaciones y proporciona recomendaciones basadas en la compatibilidad con diferentes modelos LLM.

![LLM Laptop Lens](https://via.placeholder.com/800x400?text=LLM+Laptop+Lens)

## Características

- **Exploración de Laptops**: Busca y filtra laptops por marca, precio, especificaciones y compatibilidad con LLMs.
- **Compatibilidad con LLMs**: Verifica qué modelos de lenguaje pueden ejecutarse en cada laptop, con puntuaciones detalladas.
- **Comparación de Laptops**: Compara diferentes laptops para encontrar la mejor opción según tus necesidades de LLM.
- **Scraping Automatizado**: Recopila datos de laptops de diversas fuentes web mediante Supabase Edge Functions.
- **Análisis de Especificaciones**: Analiza las especificaciones técnicas para determinar la compatibilidad con diferentes modelos LLM.
- **Visualización de Datos**: Gráficos interactivos y tablas para comparar rendimiento, compatibilidad y relación precio-rendimiento.
- **Dashboard de Scraping**: Interfaz para monitorear y gestionar el proceso de recopilación de datos.
- **Sistema de Logs**: Registro detallado de errores y soluciones para facilitar el mantenimiento.
- **Base de Datos Normalizada**: Esquema optimizado para consultas eficientes sobre laptops y modelos LLM.

## Tecnologías Utilizadas

- **Frontend**: React 18, TypeScript, Tailwind CSS, shadcn/ui
- **Backend**: Supabase (PostgreSQL)
- **Scraping**: Puppeteer
- **Estado**: Tanstack React Query, Context API
- **Routing**: React Router
- **Testing**: Jest, Testing Library (React, User Event)
- **Logging**: Winston, Logger Service
- **Desarrollo**: Vite, ESLint, TypeScript
- **Optimización**: Code Splitting, Dynamic Imports

## Información del Proyecto

**URL del Proyecto**: [Lovable Project](https://lovable.dev/projects/e7249627-c290-4518-a584-4d1f91c38594)

## Edición del Código

Hay varias formas de editar esta aplicación:

### Uso de Lovable

Simplemente visita el [Proyecto en Lovable](https://lovable.dev/projects/e7249627-c290-4518-a584-4d1f91c38594) y comienza a interactuar mediante prompts.

Los cambios realizados a través de Lovable se confirmarán automáticamente en este repositorio.

### Desarrollo Local

Si deseas trabajar localmente usando tu propio IDE, puedes clonar este repositorio y enviar cambios. Los cambios enviados también se reflejarán en Lovable.

#### Requisitos previos

- Node.js (v18+) y npm - [instalar con nvm](https://github.com/nvm-sh/nvm#installing-and-updating)
- Docker y Docker Compose (para Supabase local)

#### Pasos para configurar el entorno

```sh
# Paso 1: Clonar el repositorio
git clone https://github.com/almarales/llm-laptop-lens.git

# Paso 2: Navegar al directorio del proyecto
cd llm-laptop-lens

# Paso 3: Instalar las dependencias necesarias
npm install

# Paso 4: Configurar variables de entorno
cp .env.example .env
# Editar .env con tus configuraciones

# Paso 5: Iniciar Supabase local
npm run db:start

# Paso 6: Ejecutar migraciones de la base de datos
npm run db:migrate

# Paso 7: Insertar datos de ejemplo
npm run db:seed
npm run db:seed:laptops

# Paso 8: Verificar la configuración
npm run test:app

# Paso 9: Iniciar el servidor de desarrollo
npm run dev
```

#### Verificación de Dependencias

Para verificar que todas las dependencias estén correctamente instaladas:

```sh
npm run check:deps
```

Este comando identificará dependencias faltantes o no utilizadas en el proyecto.

### Edición en GitHub

- Navega a los archivos deseados.
- Haz clic en el botón "Edit" (icono de lápiz) en la parte superior derecha de la vista del archivo.
- Realiza tus cambios y confírmalos.

### Uso de GitHub Codespaces

- Navega a la página principal del repositorio.
- Haz clic en el botón "Code" (botón verde) cerca de la parte superior derecha.
- Selecciona la pestaña "Codespaces".
- Haz clic en "New codespace" para lanzar un nuevo entorno de Codespace.
- Edita los archivos directamente dentro del Codespace y confirma y envía tus cambios cuando hayas terminado.

## Contribución

Si deseas contribuir a este proyecto, sigue estos pasos:

1. Haz un fork del repositorio
2. Crea una rama para tu característica (`git checkout -b feature/amazing-feature`)
3. Realiza tus cambios y haz commit (`git commit -m 'Add some amazing feature'`)
4. Envía tus cambios a la rama (`git push origin feature/amazing-feature`)
5. Abre un Pull Request

Antes de enviar tu contribución, asegúrate de:

- Ejecutar `npm run lint` para verificar que tu código cumple con las reglas de estilo
- Ejecutar `npm run check:deps` para verificar que no hay dependencias faltantes
- Documentar cualquier cambio importante en los archivos de log correspondientes

## Base de Datos

La base de datos utiliza un esquema normalizado para almacenar información sobre laptops, componentes, modelos LLM y compatibilidad. Las principales tablas son:

- **manufacturers**: Fabricantes de componentes y laptops
- **brands**: Marcas de laptops
- **laptops**: Información básica de laptops
- **cpus, gpus, ram_configurations, storage_devices**: Componentes de laptops
- **llm_models**: Modelos de lenguaje
- **model_config**: Configuraciones específicas de modelos LLM, incluyendo proveedor, versión, longitud de contexto, tokens máximos y costos
- **laptop_llm_compatibility**: Compatibilidad entre laptops y modelos LLM
- **laptop_listings**: Listados de laptops en diferentes fuentes con precios, URLs y especificaciones detalladas en formato JSON (GPU, RAM, CPU, almacenamiento, pantalla)
- **laptop_scores**: Puntuaciones de laptops en diferentes categorías, incluyendo rendimiento con LLMs

Para más detalles sobre el esquema de la base de datos, consulta la documentación en:

- `scripts/db/README.md`: Información general y scripts disponibles
- `scripts/db/DATABASE_SCHEMA.md`: Documentación detallada de todas las tablas y relaciones

## Scripts

### Scripts de Base de Datos

- **db:migrate**: Ejecuta la migración de la base de datos (incluye todas las fases)
- **db:seed**: Inserta datos de ejemplo de modelos LLM en la base de datos
- **db:seed:laptops**: Inserta datos de ejemplo de laptops y compatibilidades
- **db:start**: Inicia la instancia local de Supabase
- **db:check**: Verifica la configuración de la base de datos

### Scripts de Desarrollo

- **dev**: Inicia la aplicación en modo desarrollo
- **build**: Compila la aplicación para producción
- **preview**: Previsualiza la aplicación compilada localmente
- **lint**: Ejecuta el linter para verificar el código
- **check:deps**: Verifica dependencias faltantes o no utilizadas

### Scripts de Testing

- **test**: Ejecuta todas las pruebas
- **test:watch**: Ejecuta pruebas en modo watch
- **test:coverage**: Genera informe de cobertura de pruebas
- **test:app**: Prueba la aplicación después de la migración

## Estructura del Proyecto

```bash
llm-laptop-lens/
├── docs/                  # Documentación del proyecto
│   ├── testing-plan.md    # Plan de pruebas
│   ├── database_schema.md # Esquema de base de datos
│   ├── implementation-guide.md # Guía de implementación
│   └── supabase-integration.md # Integración con Supabase
├── logs/                  # Logs de errores y depuración
│   ├── errors/            # Registro de errores y soluciones
│   └── debug/             # Logs de depuración y análisis
├── public/                # Archivos estáticos
├── scripts/               # Scripts de utilidad
│   ├── db/                # Scripts para la base de datos y esquema
│   │   ├── 01_initial_schema.sql      # Esquema inicial
│   │   ├── 02_llm_compatibility_schema.sql # Esquema de compatibilidad LLM
│   │   ├── 03_migration_script.sql    # Script de migración
│   │   ├── 04_schema_updates.sql      # Actualizaciones recientes al esquema
│   │   ├── seed_llm_data.mjs          # Datos de ejemplo para modelos LLM
│   │   ├── seed_laptops.mjs           # Datos de ejemplo para laptops
│   │   ├── simple_seed_laptops.sql    # Script simplificado para datos de laptops
│   │   └── README.md                  # Documentación de la base de datos
│   └── check-dependencies.mjs # Verificación de dependencias
├── src/                   # Código fuente
│   ├── components/        # Componentes React
│   │   ├── laptops/       # Componentes específicos para laptops
│   │   ├── llm/           # Componentes específicos para modelos LLM
│   │   ├── scraper/       # Componentes para el scraper
│   │   │   └── dashboard/ # Dashboard del scraper
│   │   └── ui/            # Componentes de interfaz de usuario
│   ├── config/            # Configuración de la aplicación
│   │   └── logger.ts      # Configuración de Winston logger
│   ├── contexts/          # Contextos de React
│   ├── data/              # Datos estáticos
│   ├── hooks/             # Hooks personalizados
│   │   ├── data/          # Hooks para manejo de datos
│   │   └── ui/            # Hooks para interfaz de usuario
│   ├── integrations/      # Integraciones con servicios externos
│   │   └── supabase/      # Integración con Supabase
│   ├── lib/               # Utilidades y funciones
│   ├── pages/             # Páginas de la aplicación
│   ├── providers/         # Proveedores de React
│   │   └── query-provider.tsx # Proveedor de React Query
│   ├── repositories/      # Repositorios para acceso a datos
│   ├── scrapers/          # Lógica de scraping
│   ├── services/          # Servicios de la aplicación
│   │   └── logger.service.ts # Servicio de logging
│   ├── tests/             # Pruebas unitarias
│   │   ├── environment/   # Pruebas de configuración del entorno
│   │   ├── error-handling/ # Pruebas de manejo de errores
│   │   ├── exports/       # Pruebas de exportaciones
│   │   ├── integration/   # Pruebas de integración
│   │   ├── mocks/         # Mocks para pruebas
│   │   └── utils/         # Utilidades para pruebas
│   ├── types/             # Definiciones de tipos TypeScript
│   └── utils/             # Funciones de utilidad
└── supabase/              # Configuración de Supabase
    └── functions/         # Funciones Edge de Supabase
        └── scrape-laptops/# Función para scraping de laptops
```

## Sistema de Logs y Gestión de Errores

El proyecto incluye un sistema de logs para facilitar el seguimiento y resolución de problemas:

- **logs/errors/**: Contiene registros detallados de errores encontrados y sus soluciones
- **logs/debug/**: Contiene logs de depuración y análisis para optimización

Cada archivo de log sigue una estructura específica:

1. Timestamp del error
2. Descripción del problema
3. Análisis detallado
4. Solución aplicada o propuesta
5. Estado actual

El proyecto utiliza dos sistemas de logging complementarios:

- **Winston Logger**: Configurado en `src/config/logger.ts` para registrar errores en archivos y consola
- **Logger Service**: Implementado en `src/services/logger.service.ts` como servicio singleton para uso en componentes

Además, se ha implementado un script de verificación de dependencias (`npm run check:deps`) que ayuda a identificar:

- Dependencias faltantes en el proyecto
- Dependencias no utilizadas
- Posibles vulnerabilidades

## Estrategia de Testing

El proyecto implementa una estrategia de testing completa que cubre:

### Componentes

- **Tests unitarios**: Verifican el funcionamiento correcto de componentes individuales
- **Tests de integración**: Verifican la interacción entre componentes
- **Mocks**: Simulan servicios externos y estados para pruebas aisladas

### Estructura de Tests

- **environment/**: Pruebas de configuración del entorno
- **error-handling/**: Pruebas de manejo de errores
- **exports/**: Verificación de exportaciones correctas
- **integration/**: Pruebas de integración entre componentes
- **mocks/**: Objetos simulados para pruebas

### Cobertura

El proyecto tiene como objetivo una cobertura de pruebas del 80% para componentes y utilidades críticas.

Para ejecutar las pruebas:

```bash
# Ejecutar todas las pruebas
npm test

# Ejecutar pruebas en modo watch
npm run test:watch

# Generar informe de cobertura
npm run test:coverage
```

Para más detalles, consulta el plan de pruebas en `docs/testing-plan.md`.

## Tecnologías y Componentes

Este proyecto está construido con:

### Frontend

- **React 18**: Biblioteca para construir interfaces de usuario
- **TypeScript**: Superset tipado de JavaScript
- **Tailwind CSS**: Framework CSS utilitario
- **shadcn/ui**: Componentes de UI reutilizables y accesibles
- **Tanstack React Query**: Gestión de estado del servidor y caché
- **React Router**: Enrutamiento para aplicaciones React
- **Recharts**: Biblioteca para visualización de datos
- **Lucide React**: Iconos SVG para la interfaz de usuario
- **Sonner**: Notificaciones toast modernas

### Backend

- **Supabase**: Plataforma de backend como servicio
- **PostgreSQL**: Sistema de gestión de bases de datos relacional
- **Supabase Edge Functions**: Funciones serverless para scraping

### Logging y Monitoreo

- **Winston**: Biblioteca de logging avanzada
- **Logger Service**: Servicio personalizado para gestión de logs
- **Error Boundary**: Componente para capturar errores en React

### Herramientas de Desarrollo

- **Vite**: Bundler y servidor de desarrollo
- **ESLint**: Linter para identificar problemas en el código
- **Jest**: Framework de testing para JavaScript
- **Testing Library**: Biblioteca para pruebas de componentes React
- **Puppeteer**: Biblioteca para automatización y scraping web
- **Docker**: Contenedores para desarrollo local de Supabase

### Optimización

- **Code Splitting**: División del código para carga bajo demanda
- **Dynamic Imports**: Importaciones dinámicas para reducir el tamaño del bundle
- **React Query DevTools**: Herramientas de desarrollo para React Query

## Despliegue

Para desplegar este proyecto:

1. Configura una instancia de Supabase (cloud o local)
2. Ejecuta las migraciones de base de datos
3. Construye la aplicación con `npm run build`
4. Despliega los archivos estáticos en tu servidor web preferido

También puedes usar [Lovable](https://lovable.dev/projects/e7249627-c290-4518-a584-4d1f91c38594) y hacer clic en Share -> Publish para un despliegue rápido.

### Dominio Personalizado

Puedes conectar un dominio personalizado a tu proyecto Lovable:

1. Navega a Project > Settings > Domains
2. Haz clic en Connect Domain
3. Sigue las instrucciones para configurar los registros DNS

Para más información, consulta: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)

## Licencia

Este proyecto está licenciado bajo la Licencia MIT - ver el archivo [LICENSE](LICENSE) para más detalles.
