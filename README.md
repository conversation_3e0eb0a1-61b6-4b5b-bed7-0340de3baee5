
# LLM Laptop Lens

LLM Laptop Lens es una aplicación web que ayuda a los usuarios a encontrar laptops compatibles con modelos de lenguaje (LLMs) para ejecutarlos localmente. La aplicación recopila datos de laptops de diversas fuentes, analiza sus especificaciones y proporciona recomendaciones basadas en la compatibilidad con diferentes modelos LLM.

![LLM Laptop Lens](https://via.placeholder.com/800x400?text=LLM+Laptop+Lens)

## Características Principales

- **Exploración de Laptops**: Busca y filtra laptops por marca, precio, especificaciones y compatibilidad con LLMs.
- **Compatibilidad con LLMs**: Verifica qué modelos de lenguaje pueden ejecutarse en cada laptop, con puntuaciones detalladas.
- **Comparación de Laptops**: Compara diferentes laptops para encontrar la mejor opción según tus necesidades de LLM.
- **Scraping Automatizado**: Recopila datos de laptops de diversas fuentes web mediante Supabase Edge Functions.
- **Análisis de Especificaciones**: Analiza las especificaciones técnicas para determinar la compatibilidad con diferentes modelos LLM.
- **Visualización de Datos**: Gráficos interactivos y tablas para comparar rendimiento, compatibilidad y relación precio-rendimiento.
- **Dashboard de Scraping**: Interfaz avanzada para monitorear y gestionar el proceso de recopilación de datos.
- **Sistema de Logs**: Registro detallado de errores y soluciones para facilitar el mantenimiento.
- **Base de Datos Normalizada**: Esquema optimizado para consultas eficientes sobre laptops y modelos LLM.
- **API GraphQL**: Integración con APIs modernas para obtener datos estructurados.

## Tecnologías Utilizadas

- **Frontend**: React 18, TypeScript, Tailwind CSS, shadcn/ui
- **Backend**: Supabase (PostgreSQL, Edge Functions)
- **Scraping**: Puppeteer con soporte para APIs GraphQL
- **Estado**: React Query (@tanstack/react-query), Context API
- **Routing**: React Router DOM
- **Testing**: Testing Library (React, User Event)
- **Desarrollo**: Vite, ESLint, TypeScript
- **UI Components**: Radix UI, Lucide React Icons
- **Visualización**: Recharts

## Información del Proyecto

**URL del Proyecto**: [Lovable Project](https://lovable.dev/projects/e7249627-c290-4518-a584-4d1f91c38594)

## Edición del Código

### Uso de Lovable

Simplemente visita el [Proyecto en Lovable](https://lovable.dev/projects/e7249627-c290-4518-a584-4d1f91c38594) y comienza a interactuar mediante prompts.

Los cambios realizados a través de Lovable se confirmarán automáticamente en este repositorio.

### Desarrollo Local

Si deseas trabajar localmente usando tu propio IDE, puedes clonar este repositorio y enviar cambios.

#### Requisitos previos

- Node.js (v18+) y npm - [instalar con nvm](https://github.com/nvm-sh/nvm#installing-and-updating)
- Docker y Docker Compose (para Supabase local)

#### Configuración del entorno

```sh
# Paso 1: Clonar el repositorio
git clone https://github.com/almarales/llm-laptop-lens.git

# Paso 2: Navegar al directorio del proyecto
cd llm-laptop-lens

# Paso 3: Instalar las dependencias necesarias
npm install

# Paso 4: Configurar variables de entorno
cp .env.example .env
# Editar .env con tus configuraciones

# Paso 5: Iniciar Supabase local
npm run db:start

# Paso 6: Ejecutar migraciones de la base de datos
npm run db:migrate

# Paso 7: Insertar datos de ejemplo
npm run db:seed
npm run db:seed:laptops

# Paso 8: Verificar la configuración
npm run test:app

# Paso 9: Iniciar el servidor de desarrollo
npm run dev
```

#### Verificación de Dependencias

Para verificar que todas las dependencias estén correctamente instaladas:

```sh
npm run check:deps
```

### Otras Opciones de Edición

- **GitHub**: Edita archivos directamente en el navegador
- **GitHub Codespaces**: Entorno de desarrollo completo en la nube

## Estructura del Proyecto

```bash
llm-laptop-lens/
├── docs/                  # Documentación técnica
├── logs/                  # Logs de errores y depuración
├── scripts/               # Scripts de utilidad y base de datos
│   └── db/                # Scripts para la base de datos
├── src/                   # Código fuente
│   ├── components/        # Componentes React
│   │   ├── scraper/       # Componentes para el scraper
│   │   └── ui/            # Componentes de interfaz de usuario
│   ├── contexts/          # Contextos de React
│   ├── hooks/             # Hooks personalizados
│   ├── integrations/      # Integraciones con servicios externos
│   ├── pages/             # Páginas de la aplicación
│   ├── repositories/      # Repositorios para acceso a datos
│   ├── scrapers/          # Lógica de scraping
│   ├── services/          # Servicios de la aplicación
│   ├── tests/             # Pruebas unitarias y utilidades
│   └── types/             # Definiciones de tipos TypeScript
└── supabase/              # Configuración de Supabase
    └── functions/         # Funciones Edge de Supabase
```

## Base de Datos

La aplicación utiliza un esquema normalizado en PostgreSQL a través de Supabase para almacenar:

- **Información de laptops**: Marcas, modelos, especificaciones
- **Componentes**: CPUs, GPUs, RAM, almacenamiento
- **Modelos LLM**: Configuraciones y requisitos
- **Compatibilidad**: Puntuaciones y análisis de compatibilidad
- **Historial de scraping**: Registro de fuentes y resultados

Para más detalles sobre el esquema, consulta:
- `scripts/db/README.md`: Información general y scripts
- `scripts/db/DATABASE_SCHEMA.md`: Documentación detallada de tablas

## Scripts Disponibles

- **dev**: Inicia la aplicación en modo desarrollo
- **build**: Compila la aplicación para producción
- **lint**: Ejecuta el linter para verificar el código
- **test**: Ejecuta las pruebas unitarias
- **db:start**: Inicia la instancia local de Supabase
- **db:migrate**: Ejecuta migraciones de la base de datos
- **db:seed**: Inserta datos de ejemplo de modelos LLM
- **db:seed:laptops**: Inserta datos de ejemplo de laptops
- **test:app**: Prueba la aplicación después de la migración
- **check:deps**: Verifica dependencias del proyecto

## Características Técnicas Avanzadas

### Sistema de Scraping

- **Scraping Configurable**: Configuraciones almacenadas en base de datos
- **Soporte para APIs GraphQL**: Integración con APIs modernas como Revolico.com
- **Filtrado Dinámico**: Rango de precios y fechas de publicación configurables
- **Mapeo de Datos**: Transformación automática de datos a formatos estándar
- **Manejo de Errores**: Sistema robusto de logs y recuperación

### Arquitectura de Componentes

- **Componentes Modulares**: Arquitectura basada en componentes pequeños y reutilizables
- **Hooks Personalizados**: Lógica de negocio encapsulada en hooks
- **Context API**: Gestión de estado global eficiente
- **TypeScript**: Tipado estricto para mayor robustez

### Testing y Calidad

- **Testing Library**: Pruebas centradas en el usuario
- **Test Utilities**: Utilidades personalizadas para pruebas
- **Mock Components**: Componentes simulados para pruebas aisladas
- **Verificación de Dependencias**: Scripts automatizados de verificación

## Contribución

Si deseas contribuir a este proyecto:

1. Haz un fork del repositorio
2. Crea una rama para tu característica (`git checkout -b feature/amazing-feature`)
3. Realiza tus cambios y haz commit (`git commit -m 'Add some amazing feature'`)
4. Envía tus cambios a la rama (`git push origin feature/amazing-feature`)
5. Abre un Pull Request

### Antes de contribuir

- Ejecuta `npm run lint` para verificar el código
- Ejecuta `npm run check:deps` para verificar dependencias
- Documenta cambios importantes en los archivos de log

## Despliegue

### Despliegue con Lovable

Usa [Lovable](https://lovable.dev/projects/e7249627-c290-4518-a584-4d1f91c38594) y haz clic en Share → Publish para un despliegue rápido.

### Despliegue Manual

1. Configura una instancia de Supabase
2. Ejecuta las migraciones: `npm run db:migrate`
3. Construye la aplicación: `npm run build`
4. Despliega los archivos estáticos

### Dominio Personalizado

Para conectar un dominio personalizado en Lovable:

1. Ve a Project > Settings > Domains
2. Haz clic en Connect Domain
3. Sigue las instrucciones para configurar DNS

## Documentación Técnica

- `docs/Reporte Técnico Interacción con la API de Revolico.com.md`: Integración con APIs GraphQL
- `logs/errors/`: Registro de errores y soluciones
- `logs/debug/`: Logs de depuración y optimización

## Licencia

Este proyecto está licenciado bajo la Licencia MIT - ver el archivo [LICENSE](LICENSE) para más detalles.

## Estado del Proyecto

✅ **Funcionalidades Implementadas**:
- Sistema de scraping configurable
- Dashboard de administración
- Compatibilidad con APIs GraphQL
- Base de datos normalizada
- Sistema de testing robusto

🚧 **En Desarrollo**:
- Mejoras en la interfaz de usuario
- Optimización de rendimiento
- Funcionalidades de análisis avanzado

## Soporte

Para obtener ayuda o reportar problemas:
- Abre un issue en GitHub
- Consulta la documentación en `docs/`
- Revisa los logs en `logs/` para errores conocidos
