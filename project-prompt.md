# LLM Laptop Lens Project Description

## Overview
LLM Laptop Lens is a web application that helps users find laptops compatible with running Large Language Models (LLMs) locally. The app collects laptop data from various sources, analyzes specifications, and provides recommendations based on compatibility with different LLM models.

## Purpose
The primary goal is to simplify the process of finding suitable hardware for running LLMs locally by:
- Evaluating laptop specifications against LLM requirements
- Providing detailed compatibility scores and performance estimates
- Comparing price-to-performance ratios across different laptop models
- Tracking price history and availability from multiple sources

## Technical Stack
- **Frontend**: React 18, TypeScript, Tailwind CSS, shadcn/ui
- **Backend**: Supabase (PostgreSQL)
- **Data Collection**: Puppeteer/Playwright for web scraping
- **State Management**: Tanstack React Query, Context API
- **Routing**: React Router
- **Testing**: Jest, Testing Library
- **Development**: Vite, ESLint, TypeScript

## Core Features
1. **Laptop Exploration**: Search and filter laptops by brand, price, specifications, and LLM compatibility
2. **LLM Compatibility Analysis**: Verify which language models can run on each laptop with detailed scoring
3. **Laptop Comparison**: Compare different laptops to find the best option for specific LLM needs
4. **Automated Scraping**: Collect laptop data from various web sources using Supabase Edge Functions
5. **Specification Analysis**: Analyze technical specifications to determine compatibility with different LLM models
6. **Data Visualization**: Interactive charts and tables for comparing performance, compatibility, and price-performance ratio
7. **Price Tracking**: Monitor price changes across different retailers

## Database Structure
The database uses a normalized schema with these key entities:

### Core Tables
- `manufacturers`: Component and laptop manufacturers
- `brands`: Laptop brands associated with manufacturers
- `laptops`: Basic laptop information (model, brand, release date, etc.)
- `displays`: Display specifications (size, resolution, refresh rate)
- `physical_specs`: Physical characteristics (weight, dimensions, materials)

### Components
- `cpus`: CPU specifications (model, cores, clock speed)
- `gpus`: GPU specifications (model, VRAM, memory type)
- `ram_configurations`: RAM specifications (size, speed, type)
- `storage_devices`: Storage specifications (capacity, interface)

### LLM-Related
- `llm_models`: Language model information (parameters, quantization, requirements)
- `model_config`: Specific configurations (provider, version, context length)
- `laptop_llm_compatibility`: Compatibility between laptops and LLMs (scores, performance estimates)

### Pricing and Listings
- `laptop_listings`: Laptop listings from different sources with prices
- `price_history`: Historical price tracking
- `sources`: Data sources (stores, websites)

### Views
- `laptop_basic_specs`: Combined view of laptop specifications
- `llm_compatible_laptops`: View of laptops compatible with specific LLMs

## User Experience
The application allows users to:
1. Browse a catalog of laptops with detailed specifications
2. Filter laptops based on LLM compatibility requirements
3. Compare multiple laptops side-by-side
4. View estimated performance metrics for specific LLM models
5. Track price changes and find the best deals
6. Receive personalized recommendations based on their LLM usage needs