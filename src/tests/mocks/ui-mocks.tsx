
import React from 'react';

// Mock UI components for testing
export const MockCard = ({ children }: { children: React.ReactNode }) => (
  <div data-testid="mock-card">{children}</div>
);

export const MockButton = ({ children, onClick }: { children: React.ReactNode; onClick?: () => void }) => (
  <button data-testid="mock-button" onClick={onClick}>
    {children}
  </button>
);

export const MockDialog = ({ children }: { children: React.ReactNode }) => (
  <div data-testid="mock-dialog">{children}</div>
);

export const MockAlert = ({ children }: { children: React.ReactNode }) => (
  <div data-testid="mock-alert">{children}</div>
);

export const MockTabs = ({ children }: { children: React.ReactNode }) => (
  <div data-testid="mock-tabs">{children}</div>
);

export const MockTable = ({ children }: { children: React.ReactNode }) => (
  <table data-testid="mock-table">{children}</table>
);

export const MockForm = ({ children }: { children: React.ReactNode }) => (
  <form data-testid="mock-form">{children}</form>
);

export const MockInput = ({ placeholder }: { placeholder?: string }) => (
  <input data-testid="mock-input" placeholder={placeholder} />
);

export const MockSelect = ({ children }: { children: React.ReactNode }) => (
  <select data-testid="mock-select">{children}</select>
);

export const MockTooltip = ({ children }: { children?: React.ReactNode }) => (
  <div data-testid="mock-tooltip">{children}</div>
);
