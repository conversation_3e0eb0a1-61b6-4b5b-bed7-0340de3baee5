import * as React from 'react';
import LaptopSpecsCard from '@/components/LaptopSpecsCard';
import ScraperPageBase from '@/components/scraper/ScraperPageBase';
import ErrorBoundary from '@/components/ErrorBoundary';
import LoadingState from '@/components/LoadingState';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs } from '@/components/ui/tabs';

describe('Component Exports', () => {
  test('LaptopSpecsCard is exported correctly', () => {
    expect(LaptopSpecsCard).toBeDefined();
    // React.memo components are objects, not functions
    expect(typeof LaptopSpecsCard === 'function' || typeof LaptopSpecsCard === 'object').toBe(true);
    // Verify it's a valid React component
    expect(React.isValidElement(React.createElement(LaptopSpecsCard, { laptop: {} as any }))).toBe(true);
  });

  test('ScraperPageBase is exported correctly', () => {
    expect(ScraperPageBase).toBeDefined();
    expect(typeof ScraperPageBase).toBe('function');
  });

  test('ErrorBoundary is exported correctly', () => {
    expect(ErrorBoundary).toBeDefined();
    expect(typeof ErrorBoundary).toBe('function');
  });

  test('LoadingState is exported correctly', () => {
    expect(LoadingState).toBeDefined();
    expect(typeof LoadingState).toBe('function');
  });

  test('UI components are exported correctly', () => {
    expect(Card).toBeDefined();
    expect(CardContent).toBeDefined();
    expect(CardHeader).toBeDefined();
    expect(Button).toBeDefined();
    expect(Tabs).toBeDefined();
  });
});
