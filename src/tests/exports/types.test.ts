
import { DetailedLaptop } from '@/types/laptop';
import { LaptopListing, ScrapingHistory, ScrapingSource, ScrapingStats } from '@/types';
import { SiteConfig } from '@/types/database';
import { LaptopLlmCompatibility } from '@/types/entities';

describe('Type Exports', () => {
  test('laptop types are exported correctly', () => {
    // Types can't be directly tested at runtime, but we can check if they're defined
    // This is more of a TypeScript compilation check
    const typeCheck = true;
    expect(typeCheck).toBe(true);
    
    // We can also check if we can create objects with these types
    const laptopObj: DetailedLaptop = {
      id: 1,
      model_name: 'Test',
      msrp: 999
    };
    expect(laptopObj.id).toBe(1);
    
    const compatibilityObj: LaptopLlmCompatibility = {
      laptop_id: 1,
      llm_id: 1,
      score: 85,
      estimated_tokens_per_second: 20,
      can_run_offline: false
    };
    expect(compatibilityObj.score).toBe(85);
  });
  
  test('scraping types are exported correctly', () => {
    const sourceObj: ScrapingSource = {
      id: '1',
      name: 'Test Source',
      base_url: 'https://example.com',
      is_active: true,
      selectors: {},
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    expect(sourceObj.name).toBe('Test Source');
    
    const historyObj: ScrapingHistory = {
      id: '1',
      source_id: '1',
      status: 'completed',
      started_at: '2023-01-01T00:00:00Z',
      completed_at: '2023-01-01T00:00:00Z',
      items_found: 10,
      error_message: null,
      metadata: null
    };
    expect(historyObj.items_found).toBe(10);
    
    const statsObj: ScrapingStats = {
      totalItems: 10,
      lastUpdated: '2023-01-01T00:00:00Z',
      averagePrice: 999,
      successRate: 100,
      brandDistribution: { 'Test Brand': 10 }
    };
    expect(statsObj.totalItems).toBe(10);
  });
  
  test('scraper configuration types are exported correctly', () => {
    const configObj: SiteConfig = {
      id: 'test-config',
      name: 'Test Config',
      url: 'https://example.com',
      enabled: true,
      selectors: {
        productCard: '.product',
        productName: '.name',
        productPrice: '.price',
        productImage: '.image',
        specifications: '.specs'
      }
    };
    expect(configObj.name).toBe('Test Config');
    expect(configObj.selectors.productCard).toBe('.product');
  });
});
