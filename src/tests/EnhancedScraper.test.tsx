
import { render, screen, waitFor } from './utils/test-utils';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Mock component for testing
const MockEnhancedScraper = () => {
  return (
    <div>
      <h1>Enhanced Scraper</h1>
      <button>Start Scraping</button>
      <div data-testid="scraper-status">Ready</div>
    </div>
  );
};

describe('EnhancedScraper', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
      },
    });
  });

  test('renders Enhanced Scraper component', async () => {
    render(
      <QueryClientProvider client={queryClient}>
        <MockEnhancedScraper />
      </QueryClientProvider>
    );

    expect(screen.getByText('Enhanced Scraper')).toBeInTheDocument();
    expect(screen.getByText('Start Scraping')).toBeInTheDocument();
    expect(screen.getByTestId('scraper-status')).toHaveTextContent('Ready');
  });

  test('handles scraper interactions', async () => {
    render(
      <QueryClientProvider client={queryClient}>
        <MockEnhancedScraper />
      </QueryClientProvider>
    );

    const startButton = screen.getByText('Start Scraping');
    expect(startButton).toBeInTheDocument();
    
    await userEvent.click(startButton);
    
    await waitFor(() => {
      expect(screen.getByTestId('scraper-status')).toBeInTheDocument();
    });
  });
});
