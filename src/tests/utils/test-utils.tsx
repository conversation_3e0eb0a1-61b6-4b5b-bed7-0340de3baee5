
import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Import testing utilities separately to avoid conflicts
import { screen } from '@testing-library/react';
import { fireEvent } from '@testing-library/react';
import { waitFor } from '@testing-library/react';

// Re-export everything from testing-library/react
export * from '@testing-library/react';
export { default as userEvent } from '@testing-library/user-event';

// Explicitly export the commonly used utilities
export { screen, fireEvent, waitFor };

// Mock components
const MockBrowserRouter = ({ children }: { children: React.ReactNode }) => <div>{children}</div>;
const MockTooltipProvider = ({ children }: { children: React.ReactNode }) => <div>{children}</div>;
const MockToaster = () => <div data-testid="toaster">Toaster</div>;

// Create a custom render function that includes providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <MockBrowserRouter>
        <MockTooltipProvider>
          <MockToaster />
          {children}
        </MockTooltipProvider>
      </MockBrowserRouter>
    </QueryClientProvider>
  );
};

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>,
) => render(ui, { wrapper: AllTheProviders, ...options });

// Override the default render with our custom render
export { customRender as render };
