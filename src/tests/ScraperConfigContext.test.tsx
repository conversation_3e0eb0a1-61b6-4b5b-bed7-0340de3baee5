
import { render, waitFor, screen } from './utils/test-utils';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ScraperConfigProvider, useScraperConfig } from '@/contexts/ScraperConfigContext';

// Mock para localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString();
    },
    clear: () => {
      store = {};
    }
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

// Componente de prueba que utiliza el hook
const TestComponent = () => {
  const { configs, isLoading, error, updateConfig, deleteConfig } = useScraperConfig();

  return (
    <div>
      <h1>Scraper Configs</h1>
      {isLoading && <div>Loading...</div>}
      {error && <div>Error: {error.message}</div>}
      <ul>
        {configs.map((config, index) => (
          <li key={config.id}>{config.name}</li>
        ))}
      </ul>
      {configs.length > 0 && (
        <>
          <button onClick={() => updateConfig(configs[0].id, { ...configs[0], name: 'Updated Config' })}>
            Update Config
          </button>
          <button onClick={() => deleteConfig(configs[0].id)}>Delete Config</button>
        </>
      )}
    </div>
  );
};

describe('ScraperConfigContext', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
      },
    });
    localStorageMock.clear();
  });

  test('should load initial configs from localStorage', async () => {
    localStorageMock.setItem(
      'scraperConfigs',
      JSON.stringify([
        {
          id: 'initial-config',
          name: 'Initial Config',
          url: 'http://example.com',
          enabled: true,
          selectors: {
            productCard: '.product',
            productName: '.name',
            productPrice: '.price',
            productImage: '.image',
            specifications: '.specs'
          }
        },
      ])
    );

    render(
      <QueryClientProvider client={queryClient}>
        <ScraperConfigProvider>
          <TestComponent />
        </ScraperConfigProvider>
      </QueryClientProvider>
    );

    await waitFor(() => {
      expect(screen.getByText('Initial Config')).toBeInTheDocument();
    });
  });

  test('should handle empty configs state', async () => {
    render(
      <QueryClientProvider client={queryClient}>
        <ScraperConfigProvider>
          <TestComponent />
        </ScraperConfigProvider>
      </QueryClientProvider>
    );

    await waitFor(() => {
      expect(screen.getByText('Scraper Configs')).toBeInTheDocument();
    });
  });
});
