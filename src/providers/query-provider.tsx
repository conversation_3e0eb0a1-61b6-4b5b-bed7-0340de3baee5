
/**
 * Proveedor de consultas para React Query
 *
 * Este componente configura React Query para la aplicación,
 * proporcionando configuración por defecto y manejo de errores.
 */

import { ReactNode } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { toast } from '@/hooks/use-toast';

// Crear cliente de consulta con configuración personalizada
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Tiempo de vida de los datos en caché (5 minutos)
      staleTime: 5 * 60 * 1000,
      // Tiempo de vida en caché antes de ser eliminados (10 minutos)
      gcTime: 10 * 60 * 1000,
      // Número de reintentos en caso de error
      retry: 3,
      // Intervalo de reintentos
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      // Refetch en foco de ventana
      refetchOnWindowFocus: false,
      // Refetch al reconectar
      refetchOnReconnect: true,
    },
    mutations: {
      // Configuración para mutaciones
      retry: 1,
      // Handle mutation errors globally
      onError: (error: Error) => {
        console.error('Mutation error:', error);
        toast({
          variant: 'destructive',
          title: 'Error',
          description: error.message || 'An unexpected error occurred',
        });
      },
    },
  },
});

interface QueryProviderProps {
  children: ReactNode;
}

export function QueryProvider({ children }: QueryProviderProps) {
  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
}

export { queryClient };
