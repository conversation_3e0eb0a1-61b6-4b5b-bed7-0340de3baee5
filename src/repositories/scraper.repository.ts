
/**
 * Repositorio de scraper
 * 
 * Este repositorio proporciona métodos para interactuar con las tablas relacionadas con el scraping en Supabase.
 * Extiende el repositorio base para heredar los métodos CRUD básicos.
 */

import { SUPABASE_TABLES } from '@/integrations/supabase/config';
import { ApiResponse, ScrapingHistory, ScrapingSource } from '@/types';
import { BaseRepository } from './base.repository';

export class ScraperRepository extends BaseRepository<ScrapingSource> {
  constructor() {
    super(SUPABASE_TABLES.SOURCES);
  }

  /**
   * Obtiene todas las fuentes de scraping
   */
  async getSources(): Promise<ApiResponse<ScrapingSource[]>> {
    return this.getAll({
      order: { column: 'name', ascending: true },
    });
  }

  /**
   * Obtiene las fuentes de scraping activas
   */
  async getActiveSources(): Promise<ApiResponse<ScrapingSource[]>> {
    return this.findByCriteria(
      { is_active: true },
      { order: { column: 'name', ascending: true } }
    );
  }

  /**
   * Obtiene el historial de scraping
   * @param limit Límite de registros
   * @param offset Desplazamiento para paginación
   */
  async getScrapingHistory(
    limit: number = 10,
    offset: number = 0
  ): Promise<ApiResponse<ScrapingHistory[]>> {
    return this.service.get<ScrapingHistory>(SUPABASE_TABLES.SCRAPING_HISTORY, {
      select: '*, sources(name)',
      order: { column: 'started_at', ascending: false },
      limit,
      offset,
    });
  }

  /**
   * Obtiene el historial de scraping para una fuente específica
   * @param sourceId ID de la fuente
   * @param limit Límite de registros
   * @param offset Desplazamiento para paginación
   */
  async getSourceScrapingHistory(
    sourceId: string | number,
    limit: number = 10,
    offset: number = 0
  ): Promise<ApiResponse<ScrapingHistory[]>> {
    return this.service.get<ScrapingHistory>(SUPABASE_TABLES.SCRAPING_HISTORY, {
      filter: { source_id: sourceId },
      select: '*, sources(name)',
      order: { column: 'started_at', ascending: false },
      limit,
      offset,
    });
  }

  /**
   * Crea un nuevo registro de historial de scraping
   * @param data Datos del historial
   */
  async createScrapingHistory(data: Partial<ScrapingHistory>): Promise<ApiResponse<ScrapingHistory>> {
    return this.service.post<ScrapingHistory>(SUPABASE_TABLES.SCRAPING_HISTORY, data as Record<string, any>);
  }

  /**
   * Actualiza un registro de historial de scraping
   * @param id ID del historial
   * @param data Datos a actualizar
   */
  async updateScrapingHistory(
    id: string | number,
    data: Partial<ScrapingHistory>
  ): Promise<ApiResponse<ScrapingHistory>> {
    const updateData = {
      id,
      ...data,
    };
    
    return this.service.put<ScrapingHistory>(
      SUPABASE_TABLES.SCRAPING_HISTORY,
      updateData as Record<string, any>
    );
  }

  /**
   * Obtiene estadísticas de scraping
   */
  async getScrapingStats(): Promise<ApiResponse<any>> {
    // Return mock stats for now since we don't have the RPC function
    return Promise.resolve({
      data: {
        totalItems: 0,
        lastUpdated: new Date().toISOString(),
        successRate: 0,
        averagePrice: 0,
        brandDistribution: {}
      },
      status: 200
    });
  }
}
