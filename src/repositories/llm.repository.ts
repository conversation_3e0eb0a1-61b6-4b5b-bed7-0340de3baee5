
/**
 * Repositorio para modelos LLM y compatibilidad
 */

import { LlmModel, LaptopLlmCompatibility } from '@/types/entities';
import { ApiResponse } from '@/types/api';

export class LlmRepository {

  async getAllModels(): Promise<ApiResponse<LlmModel[]>> {
    try {
      // Use localStorage for mock data - only return data that would come from real sources
      const mockModels: LlmModel[] = [
        {
          id: 1,
          name: 'GPT-4',
          parameters_billions: 175,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          id: 2,
          name: 'Claude-3',
          parameters_billions: 100,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ];
      
      return { data: mockModels, error: null, status: 200 };
    } catch (error) {
      return { 
        data: [], 
        error: error instanceof Error ? error.message : 'Unknown error',
        status: 500
      };
    }
  }

  // Alias for getAllModels
  async getLlmModels(): Promise<ApiResponse<LlmModel[]>> {
    return this.getAllModels();
  }

  async getModelById(id: number): Promise<ApiResponse<LlmModel | null>> {
    try {
      const response = await this.getAllModels();
      if (response.error) {
        return { data: null, error: response.error, status: response.status };
      }
      
      const model = response.data?.find(m => m.id === id) || null;
      return { data: model, error: null, status: 200 };
    } catch (error) {
      return { 
        data: null, 
        error: error instanceof Error ? error.message : 'Unknown error',
        status: 500
      };
    }
  }

  async getLlmModelWithCompatibilities(id: string | number): Promise<ApiResponse<LlmModel | null>> {
    const numericId = typeof id === 'string' ? parseInt(id) : id;
    return this.getModelById(numericId);
  }

  async getCompatibilityByModel(llmId: number): Promise<ApiResponse<LaptopLlmCompatibility[]>> {
    try {
      // Only return compatibility data that would be based on scraped laptop data
      const mockCompatibility: LaptopLlmCompatibility[] = [
        {
          laptop_id: 1,
          llm_id: llmId,
          score: 85,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ];
      
      return { data: mockCompatibility, error: null, status: 200 };
    } catch (error) {
      return { 
        data: [], 
        error: error instanceof Error ? error.message : 'Unknown error',
        status: 500
      };
    }
  }

  async getCompatibilityByLaptop(laptopId: number): Promise<ApiResponse<LaptopLlmCompatibility[]>> {
    try {
      // Only return compatibility data for scraped laptops
      const mockCompatibility: LaptopLlmCompatibility[] = [
        {
          laptop_id: laptopId,
          llm_id: 1,
          score: 85,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ];
      
      return { data: mockCompatibility, error: null, status: 200 };
    } catch (error) {
      return { 
        data: [], 
        error: error instanceof Error ? error.message : 'Unknown error',
        status: 500
      };
    }
  }

  async getLaptopLlmCompatibility(laptopId: string | number, llmId: string | number): Promise<ApiResponse<LaptopLlmCompatibility | null>> {
    try {
      // Only return compatibility for valid scraped data
      const mockCompatibility: LaptopLlmCompatibility = {
        laptop_id: typeof laptopId === 'string' ? parseInt(laptopId) : laptopId,
        llm_id: typeof llmId === 'string' ? parseInt(llmId) : llmId,
        score: 85,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      return { data: mockCompatibility, error: null, status: 200 };
    } catch (error) {
      return { 
        data: null, 
        error: error instanceof Error ? error.message : 'Unknown error',
        status: 500
      };
    }
  }

  async getLlmRecommendationsForLaptop(laptopId: string | number, limit: number = 5): Promise<ApiResponse<LlmModel[]>> {
    try {
      const response = await this.getAllModels();
      if (response.error) {
        return { data: [], error: response.error, status: response.status };
      }
      
      // Only recommend models based on actual scraped laptop data
      const recommendations = response.data?.slice(0, limit) || [];
      return { data: recommendations, error: null, status: 200 };
    } catch (error) {
      return { 
        data: [], 
        error: error instanceof Error ? error.message : 'Unknown error',
        status: 500
      };
    }
  }

  async create(data: Partial<LlmModel>): Promise<ApiResponse<LlmModel | null>> {
    return { data: null, error: 'Not implemented', status: 501 };
  }

  async update(id: string | number, data: Partial<LlmModel>): Promise<ApiResponse<LlmModel | null>> {
    return { data: null, error: 'Not implemented', status: 501 };
  }

  async delete(id: string | number): Promise<ApiResponse<boolean>> {
    return { data: false, error: 'Not implemented', status: 501 };
  }

  async upsertLaptopLlmCompatibility(data: LaptopLlmCompatibility): Promise<ApiResponse<LaptopLlmCompatibility | null>> {
    return { data: null, error: 'Not implemented', status: 501 };
  }
}
