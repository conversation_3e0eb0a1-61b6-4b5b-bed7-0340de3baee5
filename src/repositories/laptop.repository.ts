
/**
 * Repositorio de laptops
 * 
 * Este repositorio proporciona métodos para interactuar con las tablas relacionadas con laptops en Supabase.
 * Extiende el repositorio base para heredar los métodos CRUD básicos.
 */

import { SUPABASE_TABLES } from '@/integrations/supabase/config';
import { ApiResponse, DatabaseLaptop } from '@/types';
import { BaseRepository } from './base.repository';

export class LaptopRepository extends BaseRepository<DatabaseLaptop> {
  constructor() {
    super(SUPABASE_TABLES.LAPTOPS);
  }

  /**
   * Obtiene laptops con paginación y filtros
   */
  async getAll(options: {
    limit?: number;
    offset?: number;
    order?: { column: string; ascending: boolean };
  } = {}): Promise<ApiResponse<DatabaseLaptop[]>> {
    return super.getAll(options);
  }

  /**
   * Obtiene una laptop con detalles completos
   * @param id ID de la laptop
   */
  async getDetailedLaptop(id: number): Promise<ApiResponse<DatabaseLaptop>> {
    return this.getById(id);
  }

  /**
   * Obtiene laptops con información de marcas incluida
   */
  async getLaptopsWithBrands(): Promise<ApiResponse<any[]>> {
    // Mock implementation for now - returns structured data with brand information
    const mockLaptops = [
      {
        id: 1,
        model_name: 'Dell XPS 13',
        msrp: 1200,
        brand_id: 1,
        brands: { name: 'Dell' },
        price: 1200,
        specs: {
          cpu: 'Intel Core i7',
          gpu: 'Intel Iris Xe',
          ram: 16,
          storage: '512GB SSD'
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 2,
        model_name: 'HP Pavilion 15',
        msrp: 800,
        brand_id: 2,
        brands: { name: 'HP' },
        price: 800,
        specs: {
          cpu: 'AMD Ryzen 5',
          gpu: 'AMD Radeon',
          ram: 8,
          storage: '256GB SSD'
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];

    return Promise.resolve({
      data: mockLaptops,
      status: 200,
      message: 'Laptops with brands retrieved successfully'
    });
  }

  /**
   * Busca laptops por criterios
   * @param query Término de búsqueda
   * @param filters Filtros adicionales
   */
  async searchLaptops(query: string, filters?: Record<string, any>): Promise<ApiResponse<DatabaseLaptop[]>> {
    // For now, return mock search results
    console.log(`Mock search for query: "${query}" with filters:`, filters);
    
    return Promise.resolve({
      data: [] as DatabaseLaptop[],
      status: 200,
      message: 'Search completed'
    });
  }

  /**
   * Obtiene laptops por marca
   * @param brand Marca de la laptop
   */
  async getLaptopsByBrand(brand: string): Promise<ApiResponse<DatabaseLaptop[]>> {
    return this.findByCriteria({ brand });
  }

  /**
   * Obtiene laptops por rango de precio
   * @param minPrice Precio mínimo
   * @param maxPrice Precio máximo
   */
  async getLaptopsByPriceRange(
    minPrice: number,
    maxPrice: number
  ): Promise<ApiResponse<DatabaseLaptop[]>> {
    // For mock implementation, return empty results
    console.log(`Mock price range search: ${minPrice} - ${maxPrice}`);
    
    return Promise.resolve({
      data: [] as DatabaseLaptop[],
      status: 200,
      message: 'Price range search completed'
    });
  }
}
