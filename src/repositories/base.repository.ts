
/**
 * Repositorio base
 * 
 * Este repositorio proporciona métodos CRUD básicos para interactuar con una tabla de Supabase.
 * Puede ser extendido para crear repositorios específicos para cada entidad.
 */

import { ApiResponse } from '@/types';
import { SupabaseService } from '@/services/supabase.service';

export class BaseRepository<T> {
  protected service: SupabaseService;
  protected tableName: string;

  constructor(tableName: string) {
    this.service = new SupabaseService();
    this.tableName = tableName;
  }

  /**
   * Obtiene todos los registros de la tabla
   * @param params Parámetros de consulta (filtros, ordenamiento, etc.)
   */
  async getAll(params: Record<string, any> = {}): Promise<ApiResponse<T[]>> {
    return this.service.get<T>(this.tableName, params);
  }

  /**
   * Obtiene un registro por su ID
   * @param id ID del registro
   * @param select Campos a seleccionar
   */
  async getById(id: string | number, select: string = '*'): Promise<ApiResponse<T>> {
    const response = await this.service.get<T>(this.tableName, {
      filter: { id },
      select,
    });

    if (response.error) {
      return response as ApiResponse<T>;
    }

    const data = response.data as T[];
    
    return {
      data: data && data.length > 0 ? data[0] : null,
      status: data && data.length > 0 ? 200 : 404,
      message: data && data.length > 0 ? undefined : 'Registro no encontrado',
    };
  }

  /**
   * Crea un nuevo registro
   * @param data Datos del registro
   */
  async create(data: Partial<T>): Promise<ApiResponse<T>> {
    return this.service.post<T>(this.tableName, data as Record<string, any>);
  }

  /**
   * Actualiza un registro existente
   * @param id ID del registro
   * @param data Datos a actualizar
   */
  async update(id: string | number, data: Partial<T>): Promise<ApiResponse<T>> {
    const updateData = {
      id,
      ...data,
    };
    
    return this.service.put<T>(this.tableName, updateData as Record<string, any>);
  }

  /**
   * Elimina un registro
   * @param id ID del registro
   */
  async delete(id: string | number): Promise<ApiResponse<T>> {
    return this.service.delete<T>(this.tableName, { id });
  }

  /**
   * Busca registros que coincidan con los criterios especificados
   * @param criteria Criterios de búsqueda
   * @param params Parámetros adicionales (ordenamiento, paginación, etc.)
   */
  async findByCriteria(
    criteria: Record<string, any>,
    params: Record<string, any> = {}
  ): Promise<ApiResponse<T[]>> {
    return this.service.get<T>(this.tableName, {
      ...params,
      filter: criteria,
    });
  }
}
