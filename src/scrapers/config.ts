
export const SCRAPING_CONFIG = {
  targetUrl: "https://laptop-ventas.ola.click/products",
  rateLimit: {
    requests: 2, // Number of requests
    period: 1000, // Time period in milliseconds
  },
  retries: {
    max: 3,
    delay: 2000,
  },
  timeout: 30000, // 30 seconds
  userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  outputPath: "./scraped-data.json",
  logFile: "./scraping-log.txt",
};

// Selectors for the target website
// These may need to be updated if the website structure changes
export const SELECTORS = {
  // productCard: ".product-card",
  productTitle: ".product-card__title mb-0 d-block text-truncate-2-line",
  // productBrand: ".product-card_brand",
  productPrice: ".product__price",
  productImage: ".v-image__image v-image__image--cover",
  productDescription: ".product-card__description mt-2 text-truncate-2-line",
  stockStatus: ".out-of-stock product-card__out-of-stock",
  // specifications: ".product-card_specifications",
  // pagination: ".pagination_container .pagination",
  // nextPageButton: ".pagination_next",
  // promoOffer: ".product-card_promo",
};
