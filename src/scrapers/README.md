
# Módulo de Scraping para LLM Laptop Lens

Este directorio contiene los componentes del frontend para interactuar con el servicio de scraping. El scraping real se realiza en el servicio backend (`scraper-service`).

## Arquitectura Refactorizada

La funcionalidad de scraping ha sido refactorizada para seguir una arquitectura cliente-servidor:

1. **Frontend (este directorio)**:
   - Proporciona la interfaz de usuario para configurar y ejecutar el scraping
   - Se comunica con el servicio de scraping a través de Supabase Edge Functions
   - Muestra los resultados del scraping al usuario

2. **Supabase Edge Function**:
   - Actúa como intermediario entre el frontend y el servicio de scraping
   - Gestiona la autenticación y autorización
   - Proporciona una API RESTful para el frontend

3. **Servicio de Scraping**:
   - Implementa la lógica de scraping real utilizando Puppeteer
   - Gestiona la limitación de velocidad, reintentos y manejo de errores
   - Almacena los resultados en la base de datos Supabase

## Integración con el Servicio de Scraping

El frontend se comunica con el servicio de scraping a través de Supabase Edge Functions:

```typescript
// En src/hooks/use-scraper.ts
const startScraping = async (options: ScraperOptions = {}) => {
  setIsLoading(true);
  setProgress(0);

  try {
    // Obtener fuentes habilitadas
    const sources = await getEnabledSources();

    if (sources.length === 0) {
      toast({
        variant: "destructive",
        title: "No Data Sources",
        description: "Please configure at least one active data source before starting the scraper.",
      });
      return null;
    }

    setProgress(10);

    // Filtrar fuentes si se especifican
    const sourcesToScrape = options.sources
      ? sources.filter(s => options.sources?.includes(s.id))
      : sources;

    // Llamar a la función de Supabase
    const { data, error } = await supabase.functions.invoke('scrape-laptops', {
      body: {
        sources: sourcesToScrape.map(s => s.id),
        forceRefresh: options.forceRefresh || false
      }
    });

    if (error) {
      throw new Error(`Scraping function error: ${error.message}`);
    }

    setProgress(100);

    // Actualizar las consultas para obtener los datos más recientes
    await queryClient.invalidateQueries({ queryKey: ['laptop-listings'] });

    // Mostrar mensaje de éxito
    toast({
      title: "Scraping Completed",
      description: `Found ${data.totalItems} laptops from ${data.successCount} sources.`,
    });

    return data;
  } catch (error: any) {
    console.error('Scraping error:', error);
    toast({
      variant: "destructive",
      title: "Scraping Failed",
      description: error.message || 'An unknown error occurred.',
    });
    return null;
  } finally {
    setIsLoading(false);
    setTimeout(() => setProgress(0), 1000);
  }
};
```

## Supabase Edge Function

La función de Supabase Edge actúa como intermediario entre el frontend y el servicio de scraping:

```typescript
// En supabase/functions/scrape-laptops/index.ts
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.0';

// URL del servicio de scraping
const SCRAPER_SERVICE_URL = Deno.env.get('SCRAPER_SERVICE_URL') || 'http://localhost:3000';

serve(async (req) => {
  try {
    // Obtener parámetros de la solicitud
    const requestParams = await req.json();

    // Obtener fuentes activas de la base de datos
    const supabase = createClient(Deno.env.get('SUPABASE_URL'), Deno.env.get('SUPABASE_SERVICE_ROLE_KEY'));
    const { data: dbSources } = await supabase
      .from('sources')
      .select('*')
      .eq('is_active', true);

    // Identificar las fuentes a scrapear
    const sourcesToScrape = requestParams.sources ||
      (dbSources?.length > 0 ? dbSources.map(source => source.id) : ['laptop-ventas']);

    // Llamar al servicio de scraping
    const response = await fetch(`${SCRAPER_SERVICE_URL}/api/scrape`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        sources: sourcesToScrape,
        forceRefresh: requestParams.forceRefresh || false
      })
    });

    if (!response.ok) {
      throw new Error(`Scraper service error: ${response.status}`);
    }

    const scrapingResults = await response.json();

    // Devolver los resultados
    return new Response(
      JSON.stringify(scrapingResults),
      { headers: { 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
});
```

## Configuración

La configuración de las fuentes de scraping ahora se almacena en la base de datos Supabase, en la tabla `sources`. Esto permite una gestión centralizada de las configuraciones y facilita la adición de nuevas fuentes sin necesidad de modificar el código.

## Mantenimiento

Si la estructura de un sitio web cambia, actualiza los selectores en la tabla `sources` de la base de datos. El servicio de scraping cargará automáticamente la configuración actualizada.
