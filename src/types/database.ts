/**
 * Tipos para las entidades de la base de datos (esquema antiguo)
 *
 * Estos tipos representan las entidades del esquema original de la base de datos.
 * Se mantienen para compatibilidad con el código existente mientras se migra al nuevo esquema.
 */

// Tipos relacionados con el scraping
export interface ScrapingMetadata {
  duration_seconds?: number;
  pages_scraped?: number;
  errors?: string[];
  browser_version?: string;
  user_agent?: string;
  proxy_used?: boolean;
  filters_applied?: Record<string, string | number | boolean>;
  [key: string]: unknown;
}

export interface ScrapingSource {
  id: string;
  name: string;
  base_url: string;
  is_active: boolean;
  selectors: Record<string, string>;
  created_at: string;
  updated_at: string;
}

// Add Source as alias for compatibility
export type Source = ScrapingSource;

export interface ScrapingHistory {
  id: string;
  source_id: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  started_at: string;
  completed_at: string | null;
  items_found: number;
  error_message: string | null;
  metadata: ScrapingMetadata | null;
}

export interface DataSourceConfig {
  id: string;
  name: string;
  url: string;
  enabled: boolean;
  selectors: Record<string, string>;
  lastScraped?: string;
  schedule?: 'daily' | 'weekly' | 'monthly';
}

export interface ScrapingStats {
  totalItems: number;
  lastUpdated: string;
  successRate: number;
  averagePrice: number;
  brandDistribution: Record<string, number>;
}

// Tipos relacionados con laptops (esquema antiguo)
export interface LaptopSpecs {
  processor?: string;
  ram?: string | number;
  storage?: string;
  display?: string;
  graphics?: string;
  os?: string;
  weight?: string | number;
  battery?: string;
  color?: string;
  dimensions?: string;
  ports?: string[];
  features?: string[];
  brand?: string;
  cpu?: { name: string };
  gpu?: { name: string; type?: string };
  [key: string]: string | number | string[] | { name: string; type?: string } | undefined;
}

export interface LaptopListing {
  id: string;
  source_id: string;
  scraping_id: string;
  name: string;
  price: number;
  specs: LaptopSpecs;
  url: string | null;
  image_url: string | null;
  source_url: string;
  created_at: string;
  updated_at: string;
}

// Basic Laptop type for compatibility
export interface Laptop {
  id: number;
  model_name: string;
  brand_id?: number;
  msrp?: number | null;
  description?: string;
  image_url?: string | null;
  is_available?: boolean;
  created_at?: string;
  updated_at?: string;
  // Optional relations
  brands?: { name: string };
}

// Site configuration with proper selectors type
export interface SiteSelectors {
  productCard: string;
  productName: string;
  productPrice: string;
  productImage: string;
  specifications: string;
  [key: string]: string;
}

export interface SiteConfig {
  id: string;
  name: string;
  url: string;
  enabled: boolean;
  selectors: SiteSelectors;
  base_url?: string;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface ScraperConfigContextType {
  configs: SiteConfig[];
  isLoading: boolean;
  error: Error | null;
  createConfig: (config: Omit<SiteConfig, 'id' | 'created_at' | 'updated_at'>) => Promise<void>;
  updateConfig: (id: string, config: Partial<SiteConfig>) => Promise<void>;
  deleteConfig: (id: string) => Promise<void>;
  hasEnabledConfigs: boolean;
}

// LLM Model types
export interface LlmModel {
  id: number;
  name: string;
  parameters_billions: number;
  quantization_bits?: number;
  min_ram_gb?: number;
  min_vram_gb?: number;
  requires_gpu?: boolean;
  description?: string;
  model_card_url?: string;
  created_at?: string;
  updated_at?: string;
}

export interface LaptopLlmCompatibility {
  laptop_id: number;
  llm_id: number;
  score: number;
  estimated_tokens_per_second?: number;
  can_run_offline?: boolean;
  qualitative_assessment?: string;
  recommended_batch_size?: number;
  estimated_memory_usage_gb?: number;
  created_at?: string;
  updated_at?: string;
}

// Tipos relacionados con modelos LLM
export interface ModelParameters {
  temperature?: number;
  top_p?: number;
  max_tokens?: number;
  presence_penalty?: number;
  frequency_penalty?: number;
  stop_sequences?: string[];
  model_version?: string;
  context_window?: number;
  timeout_ms?: number;
  stream?: boolean;
  [key: string]: string | number | boolean | string[] | undefined;
}

export type ModelProvider =
  | 'ollama'
  | 'openai'
  | 'anthropic'
  | 'google'
  | 'mistral'
  | 'cohere'
  | 'huggingface'
  | 'custom';

export interface ModelConfig {
  id: string;
  name: string;
  provider: ModelProvider;
  endpoint: string;
  apiKey?: string;
  parameters: ModelParameters;
  description?: string;
  isDefault?: boolean;
  maxContextLength?: number;
  costPer1kTokens?: number;
  lastUsed?: string;
  createdAt?: string;
  updatedAt?: string;
}
