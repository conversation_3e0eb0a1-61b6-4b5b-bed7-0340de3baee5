
// Re-export all types with explicit disambiguation
export type { 
  Laptop as DatabaseLaptop,
  ScrapingSource as DatabaseSource 
} from './database';

export type { 
  Laptop as EntityLaptop,
  Source as EntitySource 
} from './entities';

// Export other types without conflicts
export * from './api';
export * from './laptop';

// Re-export specific types that don't conflict from database
export type {
  LaptopListing,
  ScrapingSource,
  ScrapingHistory,
  ScrapingStats,
  SiteConfig,
  ScraperConfigContextType,
  LlmModel,
  LaptopLlmCompatibility
} from './database';

// Add missing aliases for compatibility
export type { ScrapingSource as Source } from './database';
export type { Laptop } from './database';

// Export entities types that don't conflict
export type {
  LaptopScore,
  LaptopListingNew
} from './entities';
