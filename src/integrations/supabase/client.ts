// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://ixzqoksbfdiunxqhqyrk.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml4enFva3NiZmRpdW54cWhxeXJrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMzM0MzIsImV4cCI6MjA2NjYwOTQzMn0.5n6zBSRg-fbcVJIiLeeV_tO4k0iP-mzc86we0frq2Dc";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);