
import { useMemo } from 'react';
import { LaptopListing } from '@/types/database';

export interface ChartDataPoint {
  name: string;
  value: number;
  fill?: string;
}

export interface PriceDistributionData {
  range: string;
  count: number;
  fill?: string;
}

export interface SpecsComparisonData {
  spec: string;
  value: string | number;
  count: number;
}

export function useVisualization(laptops: LaptopListing[] = []) {
  // Price distribution data
  const priceDistribution = useMemo(() => {
    if (!laptops || laptops.length === 0) return [];

    const ranges = [
      { min: 0, max: 500, label: '$0-500', color: '#8884d8' },
      { min: 501, max: 1000, label: '$501-1000', color: '#82ca9d' },
      { min: 1001, max: 1500, label: '$1001-1500', color: '#ffc658' },
      { min: 1501, max: 2000, label: '$1501-2000', color: '#ff7300' },
      { min: 2001, max: Infinity, label: '$2000+', color: '#8dd1e1' }
    ];

    const distribution = ranges.map(range => {
      const count = laptops.filter(laptop => 
        laptop.price >= range.min && laptop.price <= range.max
      ).length;

      return {
        range: range.label,
        count,
        fill: range.color
      };
    });

    return distribution.filter(item => item.count > 0);
  }, [laptops]);

  // Brand distribution data
  const brandDistribution = useMemo(() => {
    if (!laptops || laptops.length === 0) return [];

    const brandCounts = laptops.reduce((acc: Record<string, number>, laptop) => {
      const brand = laptop.specs?.brand?.toString() || 'Unknown';
      acc[brand] = (acc[brand] || 0) + 1;
      return acc;
    }, {});

    const colors = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#8dd1e1', '#d084d0', '#ffb347'];

    return Object.entries(brandCounts)
      .map(([brand, count], index) => ({
        name: brand,
        value: count,
        fill: colors[index % colors.length]
      }))
      .sort((a, b) => b.value - a.value);
  }, [laptops]);

  // RAM distribution data
  const ramDistribution = useMemo(() => {
    if (!laptops || laptops.length === 0) return [];

    const ramCounts = laptops.reduce((acc: Record<string, number>, laptop) => {
      const ram = laptop.specs?.ram?.toString() || 'Unknown';
      acc[ram] = (acc[ram] || 0) + 1;
      return acc;
    }, {});

    const colors = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#8dd1e1'];

    return Object.entries(ramCounts)
      .map(([ram, count], index) => ({
        name: `${ram}GB`,
        value: count,
        fill: colors[index % colors.length]
      }))
      .sort((a, b) => b.value - a.value);
  }, [laptops]);

  // Storage type distribution
  const storageDistribution = useMemo(() => {
    if (!laptops || laptops.length === 0) return [];

    const storageCounts = laptops.reduce((acc: Record<string, number>, laptop) => {
      const storage = laptop.specs?.storage?.toString() || 'Unknown';
      // Extract storage type (SSD/HDD) from storage string
      const type = storage.toLowerCase().includes('ssd') ? 'SSD' : 
                   storage.toLowerCase().includes('hdd') ? 'HDD' : 'Unknown';
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {});

    const colors = ['#8884d8', '#82ca9d', '#ffc658'];

    return Object.entries(storageCounts)
      .map(([type, count], index) => ({
        name: type,
        value: count,
        fill: colors[index % colors.length]
      }))
      .sort((a, b) => b.value - a.value);
  }, [laptops]);

  // Specs comparison data
  const specsComparison = useMemo(() => {
    if (!laptops || laptops.length === 0) return [];

    const specs: SpecsComparisonData[] = [];

    // CPU analysis
    const cpuCounts = laptops.reduce((acc: Record<string, number>, laptop) => {
      const cpu = laptop.specs?.processor?.toString() || 'Unknown';
      acc[cpu] = (acc[cpu] || 0) + 1;
      return acc;
    }, {});

    Object.entries(cpuCounts).forEach(([cpu, count]) => {
      specs.push({
        spec: 'CPU',
        value: cpu,
        count
      });
    });

    // GPU analysis
    const gpuCounts = laptops.reduce((acc: Record<string, number>, laptop) => {
      const gpu = laptop.specs?.graphics?.toString() || 'Integrated';
      acc[gpu] = (acc[gpu] || 0) + 1;
      return acc;
    }, {});

    Object.entries(gpuCounts).forEach(([gpu, count]) => {
      specs.push({
        spec: 'GPU',
        value: gpu,
        count
      });
    });

    return specs.sort((a, b) => b.count - a.count);
  }, [laptops]);

  // Average price by brand
  const avgPriceByBrand = useMemo(() => {
    if (!laptops || laptops.length === 0) return [];

    const brandData = laptops.reduce((acc: Record<string, { total: number; count: number }>, laptop) => {
      const brand = laptop.specs?.brand?.toString() || 'Unknown';
      if (!acc[brand]) {
        acc[brand] = { total: 0, count: 0 };
      }
      acc[brand].total += laptop.price;
      acc[brand].count += 1;
      return acc;
    }, {});

    return Object.entries(brandData).map(([brand, data]) => ({
      brand,
      avgPrice: Math.round(data.total / data.count),
      count: data.count
    }));
  }, [laptops]);

  return {
    priceDistribution,
    brandDistribution,
    ramDistribution,
    storageDistribution,
    specsComparison,
    avgPriceByBrand,
    totalLaptops: laptops?.length || 0,
    avgPrice: laptops?.length ? Math.round(laptops.reduce((sum, laptop) => sum + laptop.price, 0) / laptops.length) : 0
  };
}
