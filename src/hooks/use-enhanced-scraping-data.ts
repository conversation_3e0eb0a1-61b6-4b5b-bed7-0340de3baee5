
/**
 * Hook mejorado para obtener datos de scraping
 * 
 * Este hook utiliza los nuevos servicios de base de datos para obtener
 * datos de scraping, incluyendo fuentes, historial y laptops.
 * Solo muestra datos obtenidos a través de web scraping.
 */

import { useQuery } from '@tanstack/react-query';
import { useLlmModels, useLaptops } from '@/hooks/use-database';
import { ScrapingSource, ScrapingHistory, Laptop } from '@/types/database';

export function useEnhancedScrapingData() {
  // Obtener fuentes de datos - solo de configuraciones válidas
  const { data: sources, isLoading: sourcesLoading, error: sourcesError } = useQuery({
    queryKey: ['enhanced-scraping-sources'],
    queryFn: async () => {
      try {
        // Solo obtener fuentes que han sido configuradas para scraping
        const savedConfig = localStorage.getItem('scraperConfigs');
        if (savedConfig) {
          const config = JSON.parse(savedConfig);
          if (Array.isArray(config) && config.length > 0) {
            return config.filter(source => source.enabled && source.url).map(source => ({
              id: source.id,
              name: source.name,
              base_url: source.url,
              is_active: source.enabled,
              selectors: source.selectors || {},
              created_at: source.created_at || new Date().toISOString(),
              updated_at: source.updated_at || new Date().toISOString()
            })) as ScrapingSource[];
          }
        }
        return [] as ScrapingSource[];
      } catch (e) {
        console.error('Error loading scraping sources:', e);
        return [] as ScrapingSource[];
      }
    }
  });
  
  // Obtener historial de scraping - solo procesos completados
  const { data: history, isLoading: historyLoading, error: historyError } = useQuery({
    queryKey: ['enhanced-scraping-history'],
    queryFn: async () => {
      try {
        // Solo mostrar historial de scraping real
        const scrapingHistory = localStorage.getItem('scrapingHistory');
        if (scrapingHistory) {
          const history = JSON.parse(scrapingHistory);
          if (Array.isArray(history)) {
            return history.filter(item => 
              item.status === 'completed' && 
              item.items_found > 0
            ) as ScrapingHistory[];
          }
        }
        return [];
      } catch (e) {
        console.error('Error fetching scraping history:', e);
        return [];
      }
    }
  });
  
  // Obtener laptops - solo datos scraped
  const { data: laptops, isLoading: laptopsLoading, error: laptopsError } = useQuery({
    queryKey: ['enhanced-scraped-laptops'],
    queryFn: async () => {
      try {
        // Solo obtener datos que provienen de scraping
        const mockLaptopData = localStorage.getItem('mockLaptopData');
        if (mockLaptopData) {
          try {
            const mockLaptops = JSON.parse(mockLaptopData);
            if (Array.isArray(mockLaptops) && mockLaptops.length > 0) {
              // Filtrar solo datos válidos de scraping
              return mockLaptops.filter(laptop => 
                laptop.name && 
                laptop.name !== 'Unknown Model' &&
                laptop.source_url &&
                laptop.price > 0 &&
                laptop.specs &&
                Object.keys(laptop.specs).length > 0
              ).map((laptop: any) => ({
                id: parseInt(laptop.id.replace('laptop-', '')) || Math.floor(Math.random() * 1000),
                model_name: laptop.name,
                brand_id: 1,
                description: `Scraped from ${laptop.source_url}`,
                image_url: laptop.image_url || null,
                is_available: true,
                msrp: laptop.price || 0,
                created_at: laptop.created_at || new Date().toISOString(),
                updated_at: laptop.updated_at || new Date().toISOString(),
                // Datos adicionales para mostrar en la UI
                specs: laptop.specs || {},
                brands: { name: laptop.specs?.brand || 'Unknown' },
                source_url: laptop.source_url || ''
              })) as (Laptop & { specs: any, brands: { name: string }, source_url: string })[];
            }
          } catch (e) {
            console.error('Error parsing scraped laptop data:', e);
          }
        }
        
        return [];
      } catch (e) {
        console.error('Error fetching scraped laptops:', e);
        return [];
      }
    }
  });
  
  // Calcular estadísticas solo para datos scraped
  const stats = {
    totalItems: laptops?.length || 0,
    lastUpdated: laptops && laptops.length > 0 ? laptops[0].updated_at : null,
    averagePrice: laptops && laptops.length > 0
      ? laptops.reduce((sum, item) => sum + (item.msrp || 0), 0) / laptops.length
      : 0,
    successRate: history && history.length > 0
      ? (history.filter(item => item.status === 'completed').length / history.length) * 100
      : 0,
    brandDistribution: laptops && laptops.length > 0
      ? laptops.reduce((acc: Record<string, number>, item) => {
          const brand = (item.brands?.name || 'Unknown').toString();
          acc[brand] = (acc[brand] || 0) + 1;
          return acc;
        }, {})
      : {}
  };
  
  return {
    sources,
    history,
    laptops,
    stats,
    isLoading: sourcesLoading || historyLoading || laptopsLoading,
    isError: !!(sourcesError || historyError || laptopsError)
  };
}
