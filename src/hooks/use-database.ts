
/**
 * Hook principal para interactuar con la base de datos
 * 
 * Este hook proporciona acceso unificado a todas las operaciones de base de datos
 * utilizando los repositorios especializados y React Query para el estado.
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ScrapingSource, ScrapingHistory, LaptopListing, Laptop } from '@/types/database';
import { Source } from '@/types/entities';
import { LaptopRepository } from '@/repositories/laptop.repository';
import { ScraperRepository } from '@/repositories/scraper.repository';
import { LlmRepository } from '@/repositories/llm.repository';

// Repositorios singleton para evitar múltiples instancias
const laptopRepo = new LaptopRepository();
const scraperRepo = new ScraperRepository();
const llmRepo = new LlmRepository();

export function useDatabase() {
  const queryClient = useQueryClient();

  // Obtener todas las laptops
  const getLaptopsData = () => {
    return useQuery({
      queryKey: ['laptops-data'],
      queryFn: async () => {
        // Usar localStorage como fuente temporal
        const laptops = localStorage.getItem('laptops');
        return laptops ? JSON.parse(laptops) : [];
      },
    });
  };

  // Obtener el historial de scraping
  const getScrapingHistory = () => {
    return useQuery({
      queryKey: ['scraping-history'],
      queryFn: async () => {
        // Usar localStorage como fuente temporal
        const history = localStorage.getItem('scrapingHistory');
        return history ? JSON.parse(history) : [];
      },
    });
  };

  // Obtener fuentes de scraping
  const getScrapingSources = () => {
    return useQuery({
      queryKey: ['scraping-sources'],
      queryFn: async (): Promise<Source[]> => {
        // Usar localStorage como fuente temporal
        const sources = localStorage.getItem('scrapingSources');
        if (sources) {
          const parsed = JSON.parse(sources) as ScrapingSource[];
          // Convertir ScrapingSource a Source
          return parsed.map(source => ({
            id: parseInt(source.id) || 0,
            name: source.name,
            base_url: source.base_url,
            is_active: source.is_active,
            selectors: source.selectors,
            created_at: source.created_at,
            updated_at: source.updated_at
          }));
        }
        return [];
      },
    });
  };

  // Crear fuente de scraping
  const createSource = useMutation({
    mutationFn: async (source: Omit<Source, 'id'>): Promise<Source> => {
      const newSource: ScrapingSource = {
        id: Date.now().toString(),
        name: source.name,
        base_url: source.base_url,
        is_active: source.is_active,
        selectors: source.selectors,
        created_at: source.created_at || new Date().toISOString(),
        updated_at: source.updated_at || new Date().toISOString()
      };
      
      const existingSources = localStorage.getItem('scrapingSources');
      const sources = existingSources ? JSON.parse(existingSources) : [];
      sources.push(newSource);
      localStorage.setItem('scrapingSources', JSON.stringify(sources));
      
      return {
        id: parseInt(newSource.id),
        name: newSource.name,
        base_url: newSource.base_url,
        is_active: newSource.is_active,
        selectors: newSource.selectors,
        created_at: newSource.created_at,
        updated_at: newSource.updated_at
      };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['scraping-sources'] });
    },
  });

  // Actualizar fuente de scraping
  const updateSource = useMutation({
    mutationFn: async ({ id, source }: { id: number; source: Partial<Source> }): Promise<Source> => {
      const existingSources = localStorage.getItem('scrapingSources');
      const sources = existingSources ? JSON.parse(existingSources) as ScrapingSource[] : [];
      const index = sources.findIndex(s => parseInt(s.id) === id);
      
      if (index !== -1) {
        sources[index] = {
          ...sources[index],
          ...source,
          id: id.toString(),
          updated_at: new Date().toISOString()
        };
        localStorage.setItem('scrapingSources', JSON.stringify(sources));
        
        return {
          id,
          name: sources[index].name,
          base_url: sources[index].base_url,
          is_active: sources[index].is_active,
          selectors: sources[index].selectors,
          created_at: sources[index].created_at,
          updated_at: sources[index].updated_at
        };
      }
      throw new Error('Source not found');
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['scraping-sources'] });
    },
  });

  // Eliminar fuente de scraping
  const deleteSource = useMutation({
    mutationFn: async (id: string | number): Promise<void> => {
      const numericId = typeof id === 'string' ? parseInt(id) : id;
      const existingSources = localStorage.getItem('scrapingSources');
      let sources = existingSources ? JSON.parse(existingSources) as ScrapingSource[] : [];
      sources = sources.filter(s => parseInt(s.id) !== numericId);
      localStorage.setItem('scrapingSources', JSON.stringify(sources));
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['scraping-sources'] });
    },
  });

  // Obtener laptops usando el repositorio correcto
  const getLaptops = () => {
    return useQuery({
      queryKey: ['laptops'],
      queryFn: async (): Promise<Laptop[]> => {
        const response = await laptopRepo.getLaptopsWithBrands();
        if (response.error) {
          throw new Error(response.error);
        }
        return response.data || [];
      },
    });
  };

  return {
    // Sources
    getScrapingSources,
    createSource,
    updateSource,
    deleteSource,
    
    // Laptops
    getLaptops,
    getLaptopsData,
    
    // History
    getScrapingHistory,
  };
}

// Export additional hooks
export function useSources() {
  const { getScrapingSources } = useDatabase();
  const { data: sources, isLoading, error } = getScrapingSources();
  
  return {
    sources: sources || [],
    isLoading,
    error
  };
}

export function useLlmModels() {
  return useQuery({
    queryKey: ['llm-models'],
    queryFn: async () => {
      const response = await llmRepo.getAllModels();
      if (response.error) {
        throw new Error(response.error);
      }
      return response.data || [];
    },
  });
}

export function useLaptops() {
  return useQuery({
    queryKey: ['laptops'],
    queryFn: async () => {
      const response = await laptopRepo.getLaptopsWithBrands();
      if (response.error) {
        throw new Error(response.error);
      }
      return response.data || [];
    },
  });
}

export function useLaptopLlmCompatibility(laptopId: string | number, llmId: string | number) {
  return useQuery({
    queryKey: ['laptop-llm-compatibility', laptopId, llmId],
    queryFn: async () => {
      const response = await llmRepo.getLaptopLlmCompatibility(laptopId, llmId);
      if (response.error) {
        throw new Error(response.error);
      }
      return response.data;
    },
    enabled: !!laptopId && !!llmId,
  });
}
