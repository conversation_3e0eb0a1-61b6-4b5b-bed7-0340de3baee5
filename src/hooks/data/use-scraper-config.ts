
/**
 * Hook personalizado para el manejo de configuraciones de scraper
 * Proporciona funcionalidades de gestión de configuraciones de sitios web
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ScraperRepository } from '@/repositories/scraper.repository';
import { toast } from '@/hooks/use-toast';
import { ScrapingSource, SiteConfig, ScrapingHistory } from '@/types';

const repository = new ScraperRepository();

export function useScraperConfig() {
  const queryClient = useQueryClient();

  // Obtener todas las fuentes de scraping
  const getSources = () => {
    return useQuery({
      queryKey: ['scraper-sources'],
      queryFn: async () => {
        const response = await repository.getSources();
        if (response.error) {
          throw new Error(response.error);
        }
        return response.data;
      },
    });
  };

  // Obtener fuentes de scraping activas
  const getActiveSources = () => {
    return useQuery({
      queryKey: ['scraper-active-sources'],
      queryFn: async () => {
        const response = await repository.getActiveSources();
        if (response.error) {
          throw new Error(response.error);
        }
        return response.data;
      },
    });
  };

  // Obtener historial de scraping
  const getScrapingHistory = (limit: number = 10, offset: number = 0) => {
    return useQuery({
      queryKey: ['scraping-history', limit, offset],
      queryFn: async () => {
        const response = await repository.getScrapingHistory(limit, offset);
        if (response.error) {
          throw new Error(response.error);
        }
        return response.data;
      },
    });
  };

  // Obtener historial de scraping para una fuente específica
  const getSourceScrapingHistory = (sourceId: string | number, limit: number = 10, offset: number = 0) => {
    return useQuery({
      queryKey: ['source-scraping-history', sourceId, limit, offset],
      queryFn: async () => {
        const response = await repository.getSourceScrapingHistory(sourceId, limit, offset);
        if (response.error) {
          throw new Error(response.error);
        }
        return response.data;
      },
      enabled: !!sourceId,
    });
  };

  // Obtener estadísticas de scraping
  const getScrapingStats = () => {
    return useQuery({
      queryKey: ['scraping-stats'],
      queryFn: async () => {
        const response = await repository.getScrapingStats();
        if (response.error) {
          throw new Error(response.error);
        }
        return response.data;
      },
    });
  };

  // Crear una fuente de scraping
  const createSource = useMutation({
    mutationFn: async (data: Partial<ScrapingSource>) => {
      const response = await repository.create(data);
      if (response.error) {
        throw new Error(response.error);
      }
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['scraper-sources'] });
      queryClient.invalidateQueries({ queryKey: ['scraper-active-sources'] });
    },
  });

  // Actualizar una fuente de scraping
  const updateSource = useMutation({
    mutationFn: async ({ id, data }: { id: string | number; data: Partial<ScrapingSource> }) => {
      const response = await repository.update(id, data);
      if (response.error) {
        throw new Error(response.error);
      }
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['scraper-sources'] });
      queryClient.invalidateQueries({ queryKey: ['scraper-active-sources'] });
    },
  });

  // Eliminar una fuente de scraping
  const deleteSource = useMutation({
    mutationFn: async (id: string | number) => {
      const response = await repository.delete(id);
      if (response.error) {
        throw new Error(response.error);
      }
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['scraper-sources'] });
      queryClient.invalidateQueries({ queryKey: ['scraper-active-sources'] });
    },
  });

  // Crear un registro de historial de scraping
  const createScrapingHistory = useMutation({
    mutationFn: async (data: Partial<ScrapingHistory>) => {
      const response = await repository.createScrapingHistory(data);
      if (response.error) {
        throw new Error(response.error);
      }
      return response.data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['scraping-history'] });
      if (variables.source_id) {
        queryClient.invalidateQueries({ 
          queryKey: ['source-scraping-history', variables.source_id] 
        });
      }
    },
  });

  // Actualizar un registro de historial de scraping
  const updateScrapingHistory = useMutation({
    mutationFn: async ({ id, data }: { id: string | number; data: Partial<ScrapingHistory> }) => {
      const response = await repository.updateScrapingHistory(id, data);
      if (response.error) {
        throw new Error(response.error);
      }
      return response.data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['scraping-history'] });
      if (variables.data.source_id) {
        queryClient.invalidateQueries({ 
          queryKey: ['source-scraping-history', variables.data.source_id] 
        });
      }
    },
  });

  return {
    getSources,
    getActiveSources,
    getScrapingHistory,
    getSourceScrapingHistory,
    getScrapingStats,
    createSource,
    updateSource,
    deleteSource,
    createScrapingHistory,
    updateScrapingHistory,
  };
}
