
/**
 * Hook personalizado para el manejo de datos de laptops
 * Proporciona funcionalidades de obtención y filtrado de laptops
 */

import { useQuery } from '@tanstack/react-query';
import { LaptopRepository } from '@/repositories/laptop.repository';
import { DatabaseLaptop, LaptopListing, LaptopScore } from '@/types';

const repository = new LaptopRepository();

export function useLaptops() {
  // Obtener todas las laptops
  const getLaptops = (limit: number = 10, offset: number = 0) => {
    return useQuery({
      queryKey: ['laptops', limit, offset],
      queryFn: async () => {
        const response = await repository.getAll({
          limit,
          offset,
          order: { column: 'model_name', ascending: true },
        });
        if (response.error) {
          throw new Error(response.error);
        }
        return response.data;
      },
    });
  };

  // Obtener laptop por ID con detalles completos
  const getLaptopById = (id: number) => {
    return useQuery({
      queryKey: ['laptop', id],
      queryFn: async () => {
        const response = await repository.getDetailedLaptop(id);
        if (response.error) {
          throw new Error(response.error);
        }
        return response.data;
      },
      enabled: !!id,
    });
  };

  // Buscar laptops por criterios
  const searchLaptops = (query: string, filters?: Record<string, any>) => {
    return useQuery({
      queryKey: ['laptops-search', query, filters],
      queryFn: async () => {
        const response = await repository.searchLaptops(query, filters);
        if (response.error) {
          throw new Error(response.error);
        }
        return response.data;
      },
      enabled: !!query,
    });
  };

  return {
    getLaptops,
    getLaptopById,
    searchLaptops,
  };
}
