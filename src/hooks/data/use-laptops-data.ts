
/**
 * Hook para gestionar datos de laptops
 *
 * Este hook proporciona funciones para interactuar con los datos de laptops
 * utilizando React Query para la gestión de estado y caché.
 */

import { useQuery } from '@tanstack/react-query';
import { LaptopRepository } from '@/repositories/laptop.repository';
import { Laptop } from '@/types/database';
import { useState, useCallback, useMemo } from 'react';

// Singleton del repositorio para evitar múltiples instancias
const repository = new LaptopRepository();

export function useLaptopsData(limit = 100, page = 0) {
  const [filters, setFilters] = useState({
    searchQuery: '',
    selectedBrand: 'all',
    priceRange: [0, 5000] as [number, number]
  });

  const fetchLaptops = useCallback(async () => {
    const response = await repository.getLaptopsWithBrands();
    if (response.error) {
      throw new Error(response.error);
    }
    return response.data;
  }, []);

  const { data: laptops, isLoading, error, refetch } = useQuery({
    queryKey: ['laptops', limit, page],
    queryFn: fetchLaptops,
  });

  const filteredLaptops = useMemo(() => {
    if (!laptops) return [];

    return laptops.filter((laptop: any) => {
      // Ensure laptop has required ID
      if (!laptop.id) return false;
      
      const { searchQuery, selectedBrand, priceRange } = filters;
      
      // Filtrar por búsqueda
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const modelName = (laptop.model_name || '').toLowerCase();
        const brandName = (laptop.brand_id?.toString() || '').toLowerCase();

        if (!modelName.includes(query) && !brandName.includes(query)) {
          return false;
        }
      }

      // Filtrar por marca
      if (selectedBrand !== 'all') {
        const brandId = laptop.brand_id?.toString() || '';
        if (brandId !== selectedBrand) {
          return false;
        }
      }

      // Filtrar por rango de precio
      const price = laptop.msrp || laptop.price || 0;
      if (price < priceRange[0] || price > priceRange[1]) {
        return false;
      }

      return true;
    });
  }, [laptops, filters]);

  const updateFilters = useCallback((newFilters: Partial<typeof filters>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters
    }));
  }, []);

  // Extract unique brands for filter options
  const uniqueBrands = useMemo(() => {
    if (!laptops) return [];
    
    const brands = laptops.map((laptop: any) => ({
      id: laptop.brand_id?.toString() || '',
      name: laptop.brands?.name || 'Unknown'
    }));
    
    // Remove duplicates
    const uniqueMap = new Map();
    brands.forEach(brand => {
      if (brand.id && !uniqueMap.has(brand.id)) {
        uniqueMap.set(brand.id, brand);
      }
    });
    
    return Array.from(uniqueMap.values());
  }, [laptops]);

  return {
    laptops: filteredLaptops,
    isLoading,
    error,
    refetch,
    filters,
    updateFilters,
    // Add legacy properties for backward compatibility
    filteredLaptops,
    searchQuery: filters.searchQuery,
    setSearchQuery: (query: string) => updateFilters({ searchQuery: query }),
    selectedBrand: filters.selectedBrand,
    setSelectedBrand: (brand: string) => updateFilters({ selectedBrand: brand }),
    priceRange: filters.priceRange,
    setPriceRange: (range: [number, number]) => updateFilters({ priceRange: range }),
    uniqueBrands
  };
}
