import { useState, useCallback, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { LaptopListing } from '@/types/database';
import { useScraperConfig } from '@/contexts/ScraperConfigContext';

// Tipos para las opciones de scraping
export interface ScraperOptions {
  sources?: string[];
  forceRefresh?: boolean;
}

// Tipos para los formatos de exportación
export type ExportFormat = 'json' | 'csv' | 'xlsx';

// Estados del proceso de scraping
export type ScrapingStatus = 'idle' | 'loading' | 'success' | 'error';

// Hook mejorado para el proceso de scraping
export function useEnhancedScraper() {
  // Estados
  const [status, setStatus] = useState<ScrapingStatus>('idle');
  const [progress, setProgress] = useState(0);
  const [exportProgress, setExportProgress] = useState(0);
  const [isExporting, setIsExporting] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [lastScrapingResult, setLastScrapingResult] = useState<any>(null);
  
  // Referencias para evitar condiciones de carrera
  const isMounted = useRef(true);
  const abortController = useRef<AbortController | null>(null);
  
  // Acceso al contexto de configuración
  const { configs, hasEnabledConfigs } = useScraperConfig();
  
  // Cliente de consulta para invalidar consultas
  const queryClient = useQueryClient();

  // Función para iniciar el proceso de scraping
  const startScraping = useCallback(async (options: ScraperOptions = {}) => {
    // Validar que hay configuraciones habilitadas
    if (!hasEnabledConfigs) {
      toast({
        variant: "destructive",
        title: "No Data Sources",
        description: "Please configure at least one active data source before starting the scraper.",
      });
      return null;
    }

    // Inicializar el estado
    setStatus('loading');
    setProgress(0);
    setError(null);
    
    // Crear un nuevo controlador de aborto
    abortController.current = new AbortController();
    const signal = abortController.current.signal;
    
    try {
      // Filtrar las configuraciones habilitadas
      const enabledConfigs = configs.filter(config => config.enabled);
      
      // Filtrar por fuentes específicas si se proporcionan
      const sourcesToScrape = options.sources
        ? enabledConfigs.filter(config => options.sources?.includes(config.id))
        : enabledConfigs;
      
      // Verificar que hay fuentes para hacer scraping
      if (sourcesToScrape.length === 0) {
        throw new Error('No enabled data sources found');
      }
      
      // Actualizar el progreso
      setProgress(10);
      
      // Simular el proceso de scraping
      await new Promise<void>((resolve, reject) => {
        // Verificar si el proceso ha sido abortado
        if (signal.aborted) {
          reject(new Error('Scraping process was aborted'));
          return;
        }
        
        // Simular un proceso que toma tiempo
        const timer = setTimeout(() => {
          if (isMounted.current) {
            setProgress(50);
          }
        }, 1000);
        
        const completeTimer = setTimeout(() => {
          clearTimeout(timer);
          if (isMounted.current) {
            setProgress(90);
            resolve();
          }
        }, 3000);
        
        // Limpiar temporizadores si se aborta
        signal.addEventListener('abort', () => {
          clearTimeout(timer);
          clearTimeout(completeTimer);
          reject(new Error('Scraping process was aborted'));
        });
      });
      
      // Generar datos de ejemplo
      const mockLaptops = generateMockLaptops(sourcesToScrape);
      
      // Guardar los datos en localStorage
      localStorage.setItem('mockLaptopData', JSON.stringify(mockLaptops));
      
      // Crear un resultado simulado
      const result = {
        results: sourcesToScrape.map(source => ({
          sourceId: source.id,
          count: mockLaptops.filter(laptop => 
            laptop.source_url.includes(source.url)
          ).length,
          success: true
        })),
        successCount: sourcesToScrape.length,
        failureCount: 0,
        totalItems: mockLaptops.length
      };
      
      // Actualizar el estado
      setProgress(100);
      setLastScrapingResult(result);
      
      // Invalidar consultas para forzar la actualización de los datos
      await queryClient.invalidateQueries({ queryKey: ['scraping-sources'] });
      await queryClient.invalidateQueries({ queryKey: ['scraping-history'] });
      await queryClient.invalidateQueries({ queryKey: ['laptop-listings'] });
      
      // Mostrar mensaje de éxito
      toast({
        title: "Scraping Completed",
        description: `Found ${result.totalItems} laptops from ${result.successCount} sources.`,
      });
      
      // Actualizar el estado final
      setStatus('success');
      
      return result;
    } catch (err) {
      // Manejar errores
      const error = err instanceof Error ? err : new Error('Unknown error during scraping');
      console.error('Scraping error:', error);
      
      setError(error);
      setStatus('error');
      
      // Mostrar mensaje de error
      toast({
        variant: "destructive",
        title: "Scraping Failed",
        description: error.message || 'An unknown error occurred.',
      });
      
      return null;
    } finally {
      // Limpiar el estado
      if (isMounted.current) {
        setTimeout(() => {
          if (isMounted.current && status === 'success') {
            setProgress(0);
          }
        }, 1000);
      }
    }
  }, [configs, hasEnabledConfigs, queryClient, status]);

  // Función para cancelar el proceso de scraping
  const cancelScraping = useCallback(() => {
    if (abortController.current) {
      abortController.current.abort();
      setStatus('idle');
      setProgress(0);
      toast({
        title: "Scraping Cancelled",
        description: "The scraping process was cancelled.",
      });
    }
  }, []);

  // Función para exportar datos
  const exportData = useCallback(async (format: ExportFormat = 'json', selectedFields?: string[], laptops?: LaptopListing[]) => {
    setIsExporting(true);
    setExportProgress(0);
    
    try {
      // Si no se proporcionan laptops, intentar obtenerlas de localStorage
      if (!laptops) {
        setExportProgress(10);
        const mockLaptopData = localStorage.getItem('mockLaptopData');
        
        if (mockLaptopData) {
          try {
            laptops = JSON.parse(mockLaptopData) as LaptopListing[];
          } catch (e) {
            console.error('Error parsing mock laptop data:', e);
          }
        }
        
        // Si aún no hay laptops, usar datos vacíos
        if (!laptops || laptops.length === 0) {
          laptops = [];
        }
      }
      
      setExportProgress(30);
      
      // Verificar que hay datos para exportar
      if (!laptops || laptops.length === 0) {
        toast({
          variant: "destructive",
          title: "No Data Available",
          description: "There are no results to export.",
        });
        return;
      }
      
      // Procesar los datos según los campos seleccionados
      let processedData = laptops.map(laptop => {
        const specs = laptop.specs || {};
        
        // Crear una representación plana de los datos para exportar
        const flatData = {
          id: laptop.id,
          name: laptop.name || '',
          price: laptop.price || 0,
          brand: typeof specs.brand === 'string' ? specs.brand : '',
          cpu: typeof specs.cpu === 'object' && specs.cpu ? specs.cpu.name : (specs.processor || ''),
          ram_size: typeof specs.ram === 'number' ? specs.ram : (typeof specs.ram === 'string' ? specs.ram : ''),
          ram_type: '',
          storage_size: typeof specs.storage === 'string' ? specs.storage : '',
          storage_type: '',
          gpu: typeof specs.gpu === 'object' && specs.gpu ? specs.gpu.name : (specs.graphics || ''),
          source_url: laptop.source_url || '',
          created_at: laptop.created_at,
          updated_at: laptop.updated_at
        };
        
        // Filtrar campos si se especifican
        if (selectedFields && selectedFields.length > 0) {
          return Object.fromEntries(
            Object.entries(flatData).filter(([key]) => 
              selectedFields.includes(key)
            )
          );
        }
        
        return flatData;
      });
      
      setExportProgress(60);
      
      // Formatear datos según el formato solicitado
      let exportData;
      let fileName;
      let mimeType;
      
      const timestamp = new Date().toISOString().replace(/:/g, '-').substring(0, 19);
      
      switch (format) {
        case 'json':
          exportData = JSON.stringify(processedData, null, 2);
          fileName = `laptop-data-${timestamp}.json`;
          mimeType = 'application/json';
          break;
          
        case 'csv':
          // Conversión simple a CSV
          const headers = Object.keys(processedData[0]).join(',');
          const rows = processedData.map(row => 
            Object.values(row).map(value => 
              typeof value === 'string' ? `"${value.replace(/"/g, '""')}"` : value
            ).join(',')
          );
          exportData = [headers, ...rows].join('\\n');
          fileName = `laptop-data-${timestamp}.csv`;
          mimeType = 'text/csv';
          break;
          
        case 'xlsx':
          // Para XLSX, usaríamos una biblioteca como SheetJS
          // Pero para esta implementación, devolvemos JSON como alternativa
          exportData = JSON.stringify(processedData, null, 2);
          fileName = `laptop-data-${timestamp}.json`;
          mimeType = 'application/json';
          toast({
            title: "Format Not Supported",
            description: "XLSX format requires additional libraries. Downloading as JSON instead.",
          });
          break;
      }
      
      setExportProgress(90);
      
      // Crear y descargar el archivo
      const blob = new Blob([exportData], { type: mimeType });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      setExportProgress(100);
      
      // Mostrar mensaje de éxito
      toast({
        title: "Export Successful",
        description: `Data has been exported to ${fileName}`,
      });
    } catch (err) {
      // Manejar errores
      const error = err instanceof Error ? err : new Error('Unknown error during export');
      console.error('Export error:', error);
      
      // Mostrar mensaje de error
      toast({
        variant: "destructive",
        title: "Export Failed",
        description: error.message || 'An unknown error occurred.',
      });
    } finally {
      // Limpiar el estado
      setTimeout(() => {
        setIsExporting(false);
        setExportProgress(0);
      }, 1000);
    }
  }, []);

  // Función para programar scraping automático
  const scheduleAutomaticScraping = useCallback(async (cronExpression: string = "0 0 * * *") => {
    try {
      // En una implementación real, esto configuraría un trabajo programado
      // Aquí, solo simulamos la configuración
      
      toast({
        title: "Automatic Scraping Scheduled",
        description: `Scraping jobs will run with schedule: ${cronExpression}`,
      });
      
      return true;
    } catch (err) {
      // Manejar errores
      const error = err instanceof Error ? err : new Error('Unknown error during scheduling');
      console.error('Scheduling error:', error);
      
      // Mostrar mensaje de error
      toast({
        variant: "destructive",
        title: "Scheduling Failed",
        description: error.message || 'An unknown error occurred.',
      });
      
      return false;
    }
  }, []);

  // Limpiar al desmontar
  useCallback(() => {
    return () => {
      isMounted.current = false;
      if (abortController.current) {
        abortController.current.abort();
      }
    };
  }, []);

  // Devolver los valores y funciones del hook
  return {
    status,
    isLoading: status === 'loading',
    progress,
    error,
    startScraping,
    cancelScraping,
    exportData,
    isExporting,
    exportProgress,
    scheduleAutomaticScraping,
    lastScrapingResult
  };
}

// Función auxiliar para generar datos de laptops de ejemplo
function generateMockLaptops(sources: any[]): LaptopListing[] {
  const mockLaptops: LaptopListing[] = [];
  const brands = ['Dell', 'HP', 'Lenovo', 'Asus', 'Acer', 'Apple', 'MSI'];
  const cpus = ['Intel i5', 'Intel i7', 'Intel i9', 'AMD Ryzen 5', 'AMD Ryzen 7', 'Apple M1', 'Apple M2'];
  const gpus = ['NVIDIA RTX 3050', 'NVIDIA RTX 3060', 'NVIDIA RTX 3070', 'AMD Radeon', 'Intel Iris Xe', 'Apple M1 GPU'];
  
  // Generar 3-5 laptops por fuente
  sources.forEach(source => {
    const count = Math.floor(Math.random() * 3) + 3; // 3-5 laptops
    
    for (let i = 0; i < count; i++) {
      const brand = brands[Math.floor(Math.random() * brands.length)];
      const cpu = cpus[Math.floor(Math.random() * cpus.length)];
      const gpu = gpus[Math.floor(Math.random() * gpus.length)];
      const ram = [8, 16, 32, 64][Math.floor(Math.random() * 4)];
      const storage = [256, 512, 1024, 2048][Math.floor(Math.random() * 4)];
      const price = Math.floor(Math.random() * 2000) + 500;
      
      mockLaptops.push({
        id: `laptop-${Date.now()}-${mockLaptops.length}`,
        source_id: source.id,
        scraping_id: `scrape-${Date.now()}`,
        name: `${brand} Laptop ${i + 1}`,
        price: price,
        specs: {
          brand: brand,
          cpu: { name: cpu },
          gpu: { name: gpu, type: Math.random() > 0.3 ? 'dedicated' : 'integrated' },
          ram: ram,
          storage: `${storage}GB`,
          processor: cpu,
          graphics: gpu
        },
        url: `https://example.com/laptop-${mockLaptops.length}`,
        image_url: null,
        source_url: source.url || 'https://example.com',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });
    }
  });
  
  return mockLaptops;
}
