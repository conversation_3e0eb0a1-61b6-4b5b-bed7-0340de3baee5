
import { useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { LaptopListing } from '@/types/database';

type ExportFormat = 'json' | 'csv' | 'xlsx';

interface ScraperOptions {
  sources?: string[];
  forceRefresh?: boolean;
}

export function useScraper() {
  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [exportProgress, setExportProgress] = useState(0);
  const [isExporting, setIsExporting] = useState(false);
  const queryClient = useQueryClient();

  const startScraping = async (options: ScraperOptions = {}) => {
    setIsLoading(true);
    setProgress(0);

    try {
      // First, try to get sources from localStorage
      let sources = [];
      const savedConfig = localStorage.getItem('scraperConfigs');

      if (savedConfig) {
        try {
          const config = JSON.parse(savedConfig);
          if (config.sites && Array.isArray(config.sites)) {
            sources = config.sites.filter((site: any) => site.enabled);
          }
        } catch (e) {
          console.error('Error parsing saved scraper config:', e);
        }
      }

      if (!sources || sources.length === 0) {
        toast({
          variant: "destructive",
          title: "No Data Sources",
          description: "Please configure at least one active data source before starting the scraper.",
        });
        return;
      }

      setProgress(10);

      // Filter sources if specified
      const sourcesToScrape = options.sources
        ? sources.filter(s => options.sources?.includes(s.id))
        : sources;

      // Simulate scraping process
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Create a mock response
      const mockData = {
        results: sourcesToScrape.map(source => ({
          sourceId: source.id,
          count: Math.floor(Math.random() * 10) + 1,
          success: true
        })),
        successCount: sourcesToScrape.length,
        failureCount: 0
      };

      setProgress(100);

      // Generate mock laptop data
      const mockLaptops = [];
      const brands = ['Dell', 'HP', 'Lenovo', 'Asus', 'Acer', 'Apple', 'MSI'];
      const cpus = ['Intel i5', 'Intel i7', 'Intel i9', 'AMD Ryzen 5', 'AMD Ryzen 7', 'Apple M1', 'Apple M2'];
      const gpus = ['NVIDIA RTX 3050', 'NVIDIA RTX 3060', 'NVIDIA RTX 3070', 'AMD Radeon', 'Intel Iris Xe', 'Apple M1 GPU'];

      for (let i = 0; i < 10; i++) {
        const brand = brands[Math.floor(Math.random() * brands.length)];
        const cpu = cpus[Math.floor(Math.random() * cpus.length)];
        const gpu = gpus[Math.floor(Math.random() * gpus.length)];
        const ram = [8, 16, 32, 64][Math.floor(Math.random() * 4)];
        const storage = [256, 512, 1024, 2048][Math.floor(Math.random() * 4)];
        const price = Math.floor(Math.random() * 2000) + 500;

        mockLaptops.push({
          id: `laptop-${Date.now()}-${i}`,
          source_id: sourcesToScrape[0]?.id || 'default',
          scraping_id: `scrape-${Date.now()}`,
          name: `${brand} Laptop ${i + 1}`,
          price: price,
          specs: {
            brand: brand,
            cpu: { name: cpu },
            gpu: { name: gpu, type: Math.random() > 0.3 ? 'dedicated' : 'integrated' },
            ram: ram,
            storage: `${storage}GB`
          },
          url: `https://example.com/laptop-${i}`,
          image_url: null,
          source_url: sourcesToScrape[0]?.url || 'https://example.com',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
      }

      // Store mock data in localStorage
      localStorage.setItem('mockLaptopData', JSON.stringify(mockLaptops));

      // Refresh the queries to get latest data
      await queryClient.invalidateQueries({ queryKey: ['scraping-sources'] });
      await queryClient.invalidateQueries({ queryKey: ['scraping-history'] });
      await queryClient.invalidateQueries({ queryKey: ['laptop-listings'] });

      // Show success message
      toast({
        title: "Scraping Completed",
        description: `Found ${mockData.results.reduce((sum: number, r: any) => sum + (r.count || 0), 0)} laptops from ${mockData.successCount} sources.`,
      });

      return mockData;
    } catch (error: any) {
      console.error('Scraping error:', error);
      toast({
        variant: "destructive",
        title: "Scraping Failed",
        description: error.message || 'An unknown error occurred.',
      });
      return null;
    } finally {
      setIsLoading(false);
      setTimeout(() => setProgress(0), 1000);
    }
  };

  const exportData = async (format: ExportFormat = 'json', selectedFields?: string[], laptops?: LaptopListing[]) => {
    setIsExporting(true);
    setExportProgress(0);

    try {
      // If laptops not provided, try to get from localStorage
      if (!laptops) {
        setExportProgress(10);
        const mockLaptopData = localStorage.getItem('mockLaptopData');
        
        if (mockLaptopData) {
          try {
            laptops = JSON.parse(mockLaptopData) as LaptopListing[];
          } catch (e) {
            console.error('Error parsing mock laptop data:', e);
            laptops = [];
          }
        } else {
          laptops = [];
        }
      }

      setExportProgress(30);

      if (!laptops || laptops.length === 0) {
        toast({
          variant: "destructive",
          title: "No Data Available",
          description: "There are no results to export.",
        });
        return;
      }

      // Process data based on selected fields
      let processedData = laptops.map(laptop => {
        const specs = laptop.specs || {};

        // Create a flat representation of the laptop data for export
        const flatData = {
          id: laptop.id,
          name: laptop.name || '',
          price: laptop.price || 0,
          brand: specs.brand || '',
          cpu: typeof specs.cpu === 'object' && specs.cpu ? specs.cpu.name : '',
          ram_size: specs.ram || 0,
          ram_type: '',
          storage_size: specs.storage || '',
          storage_type: '',
          gpu: typeof specs.gpu === 'object' && specs.gpu ? specs.gpu.name : '',
          source_url: laptop.source_url || '',
          created_at: laptop.created_at,
          updated_at: laptop.updated_at
        };

        // Filter fields if specified
        if (selectedFields && selectedFields.length > 0) {
          return Object.fromEntries(
            Object.entries(flatData).filter(([key]) =>
              selectedFields.includes(key)
            )
          );
        }

        return flatData;
      });

      setExportProgress(60);

      // Format data according to the requested format
      let exportData;
      let fileName;
      let mimeType;

      const timestamp = new Date().toISOString().replace(/:/g, '-').substring(0, 19);

      switch (format) {
        case 'json':
          exportData = JSON.stringify(processedData, null, 2);
          fileName = `laptop-data-${timestamp}.json`;
          mimeType = 'application/json';
          break;

        case 'csv':
          // Simple CSV conversion
          const headers = Object.keys(processedData[0]).join(',');
          const rows = processedData.map(row =>
            Object.values(row).map(value =>
              typeof value === 'string' ? `"${value.replace(/"/g, '""')}"` : value
            ).join(',')
          );
          exportData = [headers, ...rows].join('\n');
          fileName = `laptop-data-${timestamp}.csv`;
          mimeType = 'text/csv';
          break;

        case 'xlsx':
          // For XLSX, we would normally use a library like SheetJS
          // But for this implementation, we'll just return CSV as a fallback
          exportData = JSON.stringify(processedData, null, 2);
          fileName = `laptop-data-${timestamp}.json`;
          mimeType = 'application/json';
          toast({
            title: "Format Not Supported",
            description: "XLSX format requires additional libraries. Downloading as JSON instead.",
          });
          break;
      }

      setExportProgress(90);

      // Create and trigger download
      const blob = new Blob([exportData], { type: mimeType });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      setExportProgress(100);

      toast({
        title: "Export Successful",
        description: `Data has been exported to ${fileName}`,
      });
    } catch (error: any) {
      console.error('Export error:', error);
      toast({
        variant: "destructive",
        title: "Export Failed",
        description: error.message || 'An unknown error occurred.',
      });
    } finally {
      setIsExporting(false);
      setTimeout(() => setExportProgress(0), 1000);
    }
  };

  const scheduleAutomaticScraping = async (cronExpression: string = "0 0 * * *") => {
    try {
      // In a real implementation, this would set up a scheduled job
      // Here, we're just simulating the setup

      toast({
        title: "Automatic Scraping Scheduled",
        description: `Scraping jobs will run with schedule: ${cronExpression}`,
      });

      return true;
    } catch (error: any) {
      console.error('Scheduling error:', error);
      toast({
        variant: "destructive",
        title: "Scheduling Failed",
        description: error.message || 'An unknown error occurred.',
      });
      return false;
    }
  };

  return {
    isLoading,
    progress,
    startScraping,
    exportData,
    isExporting,
    exportProgress,
    scheduleAutomaticScraping
  };
}
