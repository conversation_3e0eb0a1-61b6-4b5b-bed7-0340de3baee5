
import { useQuery } from '@tanstack/react-query';
import { LaptopListing, ScrapingSource, ScrapingHistory } from '@/types/database';

export function useScrapingData() {
  const { data: sources, isLoading: sourcesLoading, error: sourcesError } = useQuery({
    queryKey: ['scraping-sources'],
    queryFn: async () => {
      // Get sources from localStorage
      const savedConfig = localStorage.getItem('scraperConfigs');
      if (savedConfig) {
        try {
          const config = JSON.parse(savedConfig);
          if (config.sites && Array.isArray(config.sites)) {
            // Convert SiteConfig to ScrapingSource format
            return config.sites.map((site: any) => ({
              id: site.id,
              name: site.name,
              base_url: site.url,
              is_active: site.enabled,
              selectors: site.selectors || {},
              created_at: site.created_at || new Date().toISOString(),
              updated_at: site.updated_at || new Date().toISOString()
            })) as ScrapingSource[];
          }
        } catch (e) {
          console.error('Error parsing saved scraper config:', e);
        }
      }

      // Return empty array if no localStorage data
      return [] as ScrapingSource[];
    }
  });

  const { data: history, isLoading: historyLoading, error: historyError } = useQuery({
    queryKey: ['scraping-history'],
    queryFn: async () => {
      // Create mock history data
      const mockHistory: ScrapingHistory[] = [
        {
          id: '1',
          source_id: '1',
          status: 'completed',
          started_at: new Date().toISOString(),
          completed_at: new Date().toISOString(),
          items_found: 10,
          error_message: null,
          metadata: null
        }
      ];

      return mockHistory;
    }
  });

  const { data: laptops, isLoading: laptopsLoading, error: laptopsError } = useQuery({
    queryKey: ['laptop-listings'],
    queryFn: async () => {
      // Get laptop data from localStorage
      const mockLaptopData = localStorage.getItem('mockLaptopData');
      if (mockLaptopData) {
        try {
          const laptops = JSON.parse(mockLaptopData);
          if (Array.isArray(laptops) && laptops.length > 0) {
            return laptops.map(laptop => ({
              id: laptop.id,
              source_id: laptop.source_id || '',
              scraping_id: laptop.scraping_id || '',
              name: laptop.name || 'Unknown Model',
              price: laptop.price || 0,
              specs: laptop.specs || {},
              url: laptop.url || '',
              image_url: laptop.image_url || null,
              source_url: laptop.source_url || '',
              created_at: laptop.created_at || new Date().toISOString(),
              updated_at: laptop.updated_at || new Date().toISOString()
            })) as LaptopListing[];
          }
        } catch (e) {
          console.error('Error parsing mock laptop data:', e);
        }
      }

      // Return empty array if no localStorage data
      return [] as LaptopListing[];
    }
  });

  // Calculate statistics for dashboard
  const stats = {
    totalItems: laptops?.length || 0,
    lastUpdated: laptops && laptops.length > 0 ? laptops[0].updated_at : null,
    averagePrice: laptops && laptops.length > 0
      ? laptops.reduce((sum, item) => sum + item.price, 0) / laptops.length
      : 0,
    successRate: history && history.length > 0
      ? (history.filter(item => item.status === 'completed').length / history.length) * 100
      : 0,
    brandDistribution: laptops && laptops.length > 0
      ? laptops.reduce((acc: Record<string, number>, item) => {
          const brand = ((item.specs?.brand as string) || 'Unknown').toString();
          acc[brand] = (acc[brand] || 0) + 1;
          return acc;
        }, {})
      : {}
  };

  return {
    sources,
    history,
    laptops,
    stats,
    isLoading: sourcesLoading || historyLoading || laptopsLoading,
    isError: !!(sourcesError || historyError || laptopsError)
  };
}
