/**
 * Página de configuración de MCP
 * 
 * Esta página permite configurar y monitorear el servidor MCP de Playwright
 * para web scraping.
 */

import { useState } from 'react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import ScraperLayout from '@/components/scraper/ScraperLayout';
import MCPConfigPanel from '@/components/scraper/MCPConfigPanel';
import MCPMonitorPanel from '@/components/scraper/MCPMonitorPanel';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, BookOpen } from 'lucide-react';
import { useScraperConfig, ScraperConfigProvider } from '@/contexts/ScraperConfigContext';
import ErrorBoundary from '@/components/ErrorBoundary';

const MCPConfigPageContent = () => {
  const [activeTab, setActiveTab] = useState('config');
  const { configs, isLoading, error } = useScraperConfig();

  // Manejar cambios en la configuración de MCP
  const handleMCPConfigChange = (config: any) => {
    console.log('MCP Config changed:', config);
    // Aquí se implementaría la lógica para guardar la configuración
  };

  // Manejar la actualización de los datos de monitoreo
  const handleRefreshMonitor = () => {
    console.log('Refreshing monitor data');
    // Aquí se implementaría la lógica para actualizar los datos de monitoreo
  };

  return (
    <div className="space-y-8">
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Información Importante</AlertTitle>
        <AlertDescription>
          El servidor MCP de Playwright permite realizar web scraping con capacidades avanzadas.
          Asegúrate de configurarlo correctamente y respetar las políticas de los sitios web.
        </AlertDescription>
      </Alert>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="config">Configuración</TabsTrigger>
          <TabsTrigger value="monitor">Monitoreo</TabsTrigger>
        </TabsList>
        
        <TabsContent value="config" className="mt-4">
          <MCPConfigPanel onConfigChange={handleMCPConfigChange} />
        </TabsContent>
        
        <TabsContent value="monitor" className="mt-4">
          <MCPMonitorPanel onRefresh={handleRefreshMonitor} />
        </TabsContent>
      </Tabs>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BookOpen className="mr-2 h-5 w-5" />
            Documentación de MCP
          </CardTitle>
          <CardDescription>
            Información sobre el servidor MCP de Playwright y sus capacidades
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium">¿Qué es MCP?</h3>
              <p className="text-muted-foreground">
                Model Context Protocol (MCP) es un protocolo abierto que proporciona una forma estandarizada
                de conectar modelos de IA a diferentes fuentes de datos y herramientas. El servidor MCP de
                Playwright permite realizar web scraping con capacidades avanzadas.
              </p>
            </div>
            
            <div>
              <h3 className="text-lg font-medium">Capacidades Principales</h3>
              <ul className="list-disc pl-5 text-muted-foreground">
                <li>Automatización de navegador con Playwright</li>
                <li>Captura de screenshots</li>
                <li>Ejecución de JavaScript</li>
                <li>Extracción de contenido</li>
                <li>Monitoreo de logs de consola</li>
                <li>Generación de código</li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-lg font-medium">Características Adicionales</h3>
              <ul className="list-disc pl-5 text-muted-foreground">
                <li>Limitación de velocidad específica del sitio</li>
                <li>Cumplimiento de robots.txt</li>
                <li>Validación de certificados SSL/TLS</li>
                <li>Manejo avanzado de errores</li>
                <li>Rotación de proxies</li>
                <li>Limpieza y normalización de datos</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

const MCPConfigPage = () => {
  return (
    <ScraperConfigProvider>
      <ScraperLayout 
        title="Configuración de MCP" 
        description="Configura y monitorea el servidor MCP de Playwright para web scraping"
      >
        <ErrorBoundary>
          <MCPConfigPageContent />
        </ErrorBoundary>
      </ScraperLayout>
    </ScraperConfigProvider>
  );
};

export default MCPConfigPage;
