
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/use-toast';
import SiteConfigForm from '@/components/scraper/SiteConfigForm';
import SiteConfigList from '@/components/scraper/SiteConfigList';
import { useScraperConfig } from '@/contexts/ScraperConfigContext';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Plus, Download, Settings } from 'lucide-react';
import { SiteConfig } from '@/types/database';

const EnhancedScraperConfigPage = () => {
  const {
    configs,
    isLoading,
    error,
    updateConfig,
    deleteConfig,
    createConfig,
    hasEnabledConfigs
  } = useScraperConfig();

  const [showForm, setShowForm] = useState(false);
  const [editingConfig, setEditingConfig] = useState<SiteConfig | null>(null);

  const handleCreateConfig = async (configData: Omit<SiteConfig, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      await createConfig(configData);
      setShowForm(false);
      
      toast({
        title: "Configuration Created",
        description: "New scraper configuration has been created successfully.",
      });
    } catch (error) {
      console.error('Error creating config:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to create configuration. Please try again.",
      });
    }
  };

  const handleEditConfig = (config: SiteConfig) => {
    setEditingConfig(config);
    setShowForm(true);
  };

  const handleUpdateConfig = async (configData: SiteConfig) => {
    if (!editingConfig) return;
    
    try {
      await updateConfig(editingConfig.id, {
        ...configData,
        updated_at: new Date().toISOString()
      });
      
      setEditingConfig(null);
      setShowForm(false);
      
      toast({
        title: "Configuration Updated",
        description: "Scraper configuration has been updated successfully.",
      });
    } catch (error) {
      console.error('Error updating config:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update configuration. Please try again.",
      });
    }
  };

  const handleDeleteConfig = async (id: string) => {
    try {
      await deleteConfig(id);
      
      toast({
        title: "Configuration Deleted",
        description: "Scraper configuration has been deleted successfully.",
      });
    } catch (error) {
      console.error('Error deleting config:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to delete configuration. Please try again.",
      });
    }
  };

  const handleToggleConfig = async (config: SiteConfig) => {
    try {
      await updateConfig(config.id, {
        ...config,
        enabled: !config.enabled,
        updated_at: new Date().toISOString()
      });
      
      toast({
        title: config.enabled ? "Configuration Disabled" : "Configuration Enabled",
        description: `${config.name} has been ${config.enabled ? 'disabled' : 'enabled'}.`,
      });
    } catch (error) {
      console.error('Error toggling config:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to toggle configuration. Please try again.",
      });
    }
  };

  const handleTestConfig = async (config: SiteConfig) => {
    try {
      // Simulate configuration testing
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast({
        title: "Configuration Test",
        description: `Test completed for ${config.name}. Connection successful.`,
      });
    } catch (error) {
      console.error('Error testing config:', error);
      toast({
        variant: "destructive",
        title: "Test Failed",
        description: "Configuration test failed. Please check your settings.",
      });
    }
  };

  const handleExportConfigs = () => {
    try {
      const exportData = {
        version: "1.0.0",
        exported_at: new Date().toISOString(),
        configurations: configs
      };
      
      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: 'application/json'
      });
      
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `scraper-configs-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      toast({
        title: "Export Successful",
        description: "Configurations have been exported successfully.",
      });
    } catch (error) {
      console.error('Error exporting configs:', error);
      toast({
        variant: "destructive",
        title: "Export Failed",
        description: "Failed to export configurations. Please try again.",
      });
    }
  };

  // Helper function to find config index by ID
  const findConfigIndex = (id: string) => configs.findIndex(config => config.id === id);

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading configurations...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="destructive">
          <AlertDescription>
            Error loading configurations: {error.message}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Scraper Configuration</h1>
          <p className="text-muted-foreground">
            Configure data sources for web scraping. Only scraped data will be displayed.
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button onClick={handleExportConfigs} variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button onClick={() => setShowForm(true)} size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Add Configuration
          </Button>
        </div>
      </div>

      {!hasEnabledConfigs && (
        <Alert>
          <Settings className="h-4 w-4" />
          <AlertDescription>
            No active configurations found. Add and enable at least one data source to start scraping.
          </AlertDescription>
        </Alert>
      )}

      {showForm && (
        <Card>
          <CardHeader>
            <CardTitle>
              {editingConfig ? 'Edit Configuration' : 'Add New Configuration'}
            </CardTitle>
            <CardDescription>
              Configure a data source for web scraping. Only valid, accessible URLs should be used.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <SiteConfigForm
              selectedConfig={editingConfig}
              onSubmit={editingConfig ? handleUpdateConfig : handleCreateConfig}
              onCancel={() => {
                setShowForm(false);
                setEditingConfig(null);
              }}
            />
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Active Configurations</CardTitle>
          <CardDescription>
            Manage your scraping configurations. Only enabled sources will be used for data collection.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {configs.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No configurations found. Add your first data source to get started.
            </div>
          ) : (
            <SiteConfigList
              siteConfigs={configs}
              onEditSite={(index: number) => handleEditConfig(configs[index])}
              onDeleteSite={(index: number) => handleDeleteConfig(configs[index].id)}
              onToggleEnable={(index: number) => handleToggleConfig(configs[index])}
              onTestScrape={(index: number) => handleTestConfig(configs[index])}
              onSaveAllConfigs={() => {}}
              isLoading={isLoading}
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default EnhancedScraperConfigPage;
