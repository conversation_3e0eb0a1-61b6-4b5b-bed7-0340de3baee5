
import React from 'react';
import ScrapingAnalyticsDashboard from '@/components/scraper/analytics/ScrapingAnalyticsDashboard';
import ScraperLayout from '@/components/scraper/ScraperLayout';

const ScrapingAnalyticsPage: React.FC = () => {
  return (
    <ScraperLayout 
      title="Análisis Técnico de Web Scraping" 
      description="Dashboard completo de monitoreo y análisis del proceso de extracción de datos"
    >
      <ScrapingAnalyticsDashboard />
    </ScraperLayout>
  );
};

export default ScrapingAnalyticsPage;
