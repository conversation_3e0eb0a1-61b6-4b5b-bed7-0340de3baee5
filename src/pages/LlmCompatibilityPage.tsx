
import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useLlmModels, useLaptops, useLaptopLlmCompatibility } from '@/hooks/use-database';
import { Laptop, LlmModel } from '@/types/entities';
import { Loader2, Cpu, HardDrive, MemoryStick, Zap } from 'lucide-react';

const LlmCompatibilityPage = () => {
  const [selectedLaptop, setSelectedLaptop] = useState<string>('');
  const [selectedLlm, setSelectedLlm] = useState<string>('');
  
  const { data: llmModels = [], isLoading: llmLoading, error: llmError } = useLlmModels();
  const { data: laptops = [], isLoading: laptopLoading, error: laptopError } = useLaptops();
  const { data: compatibility, isLoading: compatibilityLoading, error: compatibilityError } = useLaptopLlmCompatibility(
    selectedLaptop, 
    selectedLlm
  );

  const isLoading = llmLoading || laptopLoading || compatibilityLoading;
  const hasError = llmError || laptopError || compatibilityError;

  // Only show data if it comes from actual scraping
  const scrapedLaptops = laptops.filter(laptop => 
    laptop.created_at && 
    laptop.updated_at && 
    laptop.model_name &&
    laptop.model_name !== 'Unknown Model'
  );

  const handleAnalyzeCompatibility = () => {
    if (!selectedLaptop || !selectedLlm) {
      return;
    }
    // Compatibility analysis will be triggered by the hook
  };

  const getCompatibilityColor = (score: number) => {
    if (score >= 80) return 'bg-green-500';
    if (score >= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const getCompatibilityLabel = (score: number) => {
    if (score >= 80) return 'Excellent';
    if (score >= 60) return 'Good';
    if (score >= 40) return 'Fair';
    return 'Poor';
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading compatibility data...</span>
        </div>
      </div>
    );
  }

  if (hasError) {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="destructive">
          <AlertDescription>
            Error loading data. Please ensure scraping has been completed and try again.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">LLM Compatibility Analysis</h1>
        <p className="text-muted-foreground">
          Analyze compatibility between scraped laptop data and LLM models for optimal performance.
        </p>
      </div>

      {scrapedLaptops.length === 0 && (
        <Alert>
          <AlertDescription>
            No scraped laptop data available. Please run the scraper first to collect laptop data for analysis.
          </AlertDescription>
        </Alert>
      )}

      {scrapedLaptops.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Select Laptop</CardTitle>
            </CardHeader>
            <CardContent>
              <Select value={selectedLaptop} onValueChange={setSelectedLaptop}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a scraped laptop" />
                </SelectTrigger>
                <SelectContent>
                  {scrapedLaptops.map((laptop) => (
                    <SelectItem key={laptop.id} value={laptop.id.toString()}>
                      {laptop.model_name} - ${laptop.msrp?.toLocaleString() || 'N/A'}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Select LLM Model</CardTitle>
            </CardHeader>
            <CardContent>
              <Select value={selectedLlm} onValueChange={setSelectedLlm}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose an LLM model" />
                </SelectTrigger>
                <SelectContent>
                  {llmModels.map((model) => (
                    <SelectItem key={model.id} value={model.id.toString()}>
                      {model.name} ({model.parameters_billions}B parameters)
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </CardContent>
          </Card>
        </div>
      )}

      {selectedLaptop && selectedLlm && (
        <Card>
          <CardHeader>
            <CardTitle>Compatibility Analysis</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <Button onClick={handleAnalyzeCompatibility} className="w-full">
              <Zap className="h-4 w-4 mr-2" />
              Analyze Compatibility
            </Button>

            {compatibility && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Compatibility Score</span>
                  <Badge 
                    variant="outline" 
                    className={`${getCompatibilityColor(compatibility.score)} text-white`}
                  >
                    {getCompatibilityLabel(compatibility.score)}
                  </Badge>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Overall Score</span>
                    <span>{compatibility.score}%</span>
                  </div>
                  <Progress 
                    value={compatibility.score} 
                    className="h-2"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4">
                  <div className="space-y-3">
                    <h4 className="font-medium">Selected Laptop Specs</h4>
                    {(() => {
                      const laptop = scrapedLaptops.find(l => l.id.toString() === selectedLaptop);
                      if (!laptop) return <p>No laptop data available</p>;
                      
                      return (
                        <div className="space-y-2 text-sm">
                          <div className="flex items-center gap-2">
                            <Cpu className="h-4 w-4" />
                            <span>Model: {laptop.model_name}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <MemoryStick className="h-4 w-4" />
                            <span>RAM: Available in scraped data</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <HardDrive className="h-4 w-4" />
                            <span>Storage: Available in scraped data</span>
                          </div>
                        </div>
                      );
                    })()}
                  </div>

                  <div className="space-y-3">
                    <h4 className="font-medium">Selected LLM Model</h4>
                    {(() => {
                      const model = llmModels.find(m => m.id.toString() === selectedLlm);
                      if (!model) return <p>No model data available</p>;
                      
                      return (
                        <div className="space-y-2 text-sm">
                          <div>
                            <span className="font-medium">Name:</span> {model.name}
                          </div>
                          <div>
                            <span className="font-medium">Parameters:</span>{' '}
                            {model.parameters_billions}B
                          </div>
                        </div>
                      );
                    })()}
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default LlmCompatibilityPage;
