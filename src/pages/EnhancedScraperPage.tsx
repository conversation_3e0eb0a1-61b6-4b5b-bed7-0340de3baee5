
/**
 * Página mejorada del scraper
 *
 * Esta página muestra la interfaz mejorada del scraper, con funcionalidades adicionales
 * como cancelación de scraping, gestión de errores mejorada y más.
 */

import { useScrapingData } from '@/hooks/use-scraping-data';
import { useEnhancedScraper } from '@/hooks/use-enhanced-scraper';
import { toast } from '@/hooks/use-toast';
import { useScraperConfig, ScraperConfigProvider } from '@/contexts/ScraperConfigContext';
import ScraperPageBase from '@/components/scraper/ScraperPageBase';
import UnifiedScraperHeader from '@/components/scraper/UnifiedScraperHeader';

// Componente interno para usar el contexto
const EnhancedScraperPageContent = () => {
  const { sources, history, laptops, stats, isLoading: dataLoading, isError } = useScrapingData();
  const { isLoading, progress, startScraping, cancelScraping, exportData, status, error } = useEnhancedScraper();
  const { configs, isLoading: configsLoading, hasEnabledConfigs } = useScraperConfig();

  const handleDownloadResults = () => {
    if (!laptops || laptops.length === 0) {
      toast({
        variant: "destructive",
        title: "No Data Available",
        description: "There are no results to download.",
      });
      return;
    }

    exportData('json', undefined, laptops);
  };

  const header = (
    <UnifiedScraperHeader
      sources={configs}
      isLoading={isLoading}
      progress={progress}
      startScraping={startScraping}
      cancelScraping={cancelScraping}
      onDownload={handleDownloadResults}
      hasResults={!!(laptops && laptops.length)}
      error={error?.message}
      status={status}
      isEnhanced={true}
      configsLoading={configsLoading}
    />
  );

  return (
    <ScraperPageBase
      header={header}
      sources={sources}
      history={history}
      laptops={laptops}
      stats={stats}
      isLoading={dataLoading}
      isError={isError}
      withErrorBoundary={true}
    />
  );
};

// Componente principal que proporciona el contexto
const EnhancedScraperPage = () => {
  return (
    <ScraperConfigProvider>
      <EnhancedScraperPageContent />
    </ScraperConfigProvider>
  );
};

export default EnhancedScraperPage;
