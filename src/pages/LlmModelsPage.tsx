
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useLlmModels } from '@/hooks/use-database';
import { Loader2, Cpu, Database, Zap } from 'lucide-react';

const LlmModelsPage = () => {
  const { data: llmModels = [], isLoading, error } = useLlmModels();

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading LLM models...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="destructive">
          <AlertDescription>
            Error loading LLM models: {error.message}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">LLM Models</h1>
        <p className="text-muted-foreground">
          Available Large Language Models for compatibility analysis with scraped laptop data.
        </p>
      </div>

      {llmModels.length === 0 ? (
        <Alert>
          <AlertDescription>
            No LLM models available. Models are loaded from the system configuration.
          </AlertDescription>
        </Alert>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {llmModels.map((model) => (
            <Card key={model.id} className="h-full">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{model.name}</CardTitle>
                  <Badge variant="secondary">
                    {model.parameters_billions}B
                  </Badge>
                </div>
                <CardDescription>
                  Large Language Model for AI analysis
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <Cpu className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="font-medium">Parameters</p>
                      <p className="text-muted-foreground">
                        {model.parameters_billions}B
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Database className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="font-medium">Type</p>
                      <p className="text-muted-foreground">LLM</p>
                    </div>
                  </div>
                </div>

                <div className="pt-2 border-t">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Zap className="h-4 w-4" />
                    <span>Available for compatibility analysis</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default LlmModelsPage;
