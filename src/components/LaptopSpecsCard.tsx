/**
 * Componente para mostrar las especificaciones detalladas de una laptop
 */

import { useState, memo, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Cpu, MemoryStick, HardDrive, Monitor, Scale, Info, Tv } from 'lucide-react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Separator } from '@/components/ui/separator';
import { Link } from 'react-router-dom';
import { DetailedLaptop, LaptopLlmCompatibility } from '@/types/laptop';

interface LaptopSpecsCardProps {
  laptop: DetailedLaptop;
  showPrice?: boolean;
}

const LaptopSpecsCard = memo(({ laptop, showPrice = true }: LaptopSpecsCardProps) => {
  const [showDetails, setShowDetails] = useState(false);

  // Extraer datos de la laptop usando useMemo para evitar cálculos innecesarios en cada renderizado
  const laptopData = useMemo(() => {
    return {
      brandName: laptop.brands?.name || laptop.brand?.name || 'Unknown Brand',
      cpuInfo: laptop.laptop_cpus?.[0]?.cpus || { model: 'Unknown CPU' },
      gpuInfo: laptop.laptop_gpus?.[0]?.gpus || { model: 'Unknown GPU' },
      ramInfo: laptop.laptop_ram?.[0]?.ram_configurations || { size_gb: 'Unknown', speed_mhz: 'Unknown' },
      storageInfo: laptop.laptop_storage?.[0]?.storage_devices || { capacity_gb: 'Unknown' },
      displayInfo: laptop.displays?.[0] || { size_inches: 'Unknown' },
      physicalInfo: laptop.physical_specs || { weight_kg: 'Unknown' },
      sortedCompatibility: laptop.laptop_llm_compatibility
        ? [...laptop.laptop_llm_compatibility].sort((a, b) => b.score - a.score)
        : []
    };
  }, [laptop]);

  const { brandName, cpuInfo, gpuInfo, ramInfo, storageInfo, displayInfo, physicalInfo, sortedCompatibility } = laptopData;

  return (
    <Card className="overflow-hidden">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg">{laptop.model_name}</CardTitle>
            <CardDescription>{brandName}</CardDescription>
          </div>
          {showPrice && (
            <Badge variant="outline" className="text-lg font-semibold">
              ${typeof laptop.msrp === 'number' ? laptop.msrp.toLocaleString() : 'N/A'}
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="pb-2">
        <Tabs defaultValue="specs" className="w-full">
          <TabsList className="grid grid-cols-2 mb-2">
            <TabsTrigger value="specs">Especificaciones</TabsTrigger>
            <TabsTrigger value="llm">Compatibilidad LLM</TabsTrigger>
          </TabsList>

          <TabsContent value="specs" className="space-y-4">
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="flex items-center gap-1">
                <Cpu className="h-4 w-4 text-primary" />
                <span className="truncate" title={cpuInfo.model}>
                  {cpuInfo.model}
                </span>
              </div>

              <div className="flex items-center gap-1">
                <Tv className="h-4 w-4 text-primary" />
                <span className="truncate" title={gpuInfo.model}>
                  {gpuInfo.model}
                </span>
              </div>

              <div className="flex items-center gap-1">
                <MemoryStick className="h-4 w-4 text-primary" />
                <span>
                  {ramInfo.size_gb} GB
                  {ramInfo.speed_mhz && ` @ ${ramInfo.speed_mhz} MHz`}
                </span>
              </div>

              <div className="flex items-center gap-1">
                <HardDrive className="h-4 w-4 text-primary" />
                <span>
                  {storageInfo.capacity_gb} GB
                  {storageInfo.is_nvme && ' NVMe'}
                </span>
              </div>

              <div className="flex items-center gap-1">
                <Monitor className="h-4 w-4 text-primary" />
                <span>
                  {displayInfo.size_inches}"
                  {displayInfo.resolution_types?.name && ` ${displayInfo.resolution_types.name}`}
                </span>
              </div>

              <div className="flex items-center gap-1">
                <Scale className="h-4 w-4 text-primary" />
                <span>{physicalInfo.weight_kg} kg</span>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="llm" className="space-y-4">
            {sortedCompatibility.length > 0 ? (
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Modelos compatibles:</span>
                  <Link to={`/llm-compatibility?laptop=${laptop.id}`}>
                    <Button variant="link" size="sm" className="h-auto p-0">
                      Ver todos
                    </Button>
                  </Link>
                </div>

                <div className="flex flex-wrap gap-1">
                  {sortedCompatibility
                    .slice(0, 3)
                    .map((compat: LaptopLlmCompatibility, index: number) => (
                      <Badge
                        key={`${compat.llm_id}-${index}`}
                        variant={compat.score >= 80 ? "default" : compat.score >= 60 ? "secondary" : "outline"}
                      >
                        {compat.llm_models?.name || 'Unknown LLM'} ({compat.score}%)
                      </Badge>
                    ))
                  }
                  {sortedCompatibility.length > 3 && (
                    <Badge variant="outline">+{sortedCompatibility.length - 3} más</Badge>
                  )}
                </div>
              </div>
            ) : (
              <div className="text-sm text-muted-foreground text-center py-2">
                No hay datos de compatibilidad disponibles
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>

      <CardFooter className="pt-2">
        <Dialog open={showDetails} onOpenChange={setShowDetails}>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm" className="w-full">
              <Info className="h-4 w-4 mr-2" />
              Ver detalles
            </Button>
          </DialogTrigger>

          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>{laptop.model_name}</DialogTitle>
              <DialogDescription>{brandName}</DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Especificaciones</h3>
                {showPrice && (
                  <Badge variant="outline" className="text-lg font-semibold">
                    ${typeof laptop.msrp === 'number' ? laptop.msrp.toLocaleString() : 'N/A'}
                  </Badge>
                )}
              </div>

              <Separator />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium flex items-center mb-2">
                      <Cpu className="h-4 w-4 mr-2 text-primary" />
                      Procesador
                    </h4>
                    <Table>
                      <TableBody>
                        <TableRow>
                          <TableCell className="font-medium">Modelo</TableCell>
                          <TableCell>{cpuInfo.model}</TableCell>
                        </TableRow>
                        {cpuInfo.cores && (
                          <TableRow>
                            <TableCell className="font-medium">Núcleos</TableCell>
                            <TableCell>{cpuInfo.cores}</TableCell>
                          </TableRow>
                        )}
                        {cpuInfo.threads && (
                          <TableRow>
                            <TableCell className="font-medium">Hilos</TableCell>
                            <TableCell>{cpuInfo.threads}</TableCell>
                          </TableRow>
                        )}
                        {cpuInfo.base_clock_ghz && (
                          <TableRow>
                            <TableCell className="font-medium">Frecuencia base</TableCell>
                            <TableCell>{cpuInfo.base_clock_ghz} GHz</TableCell>
                          </TableRow>
                        )}
                        {cpuInfo.boost_clock_ghz && (
                          <TableRow>
                            <TableCell className="font-medium">Frecuencia turbo</TableCell>
                            <TableCell>{cpuInfo.boost_clock_ghz} GHz</TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium flex items-center mb-2">
                      <Tv className="h-4 w-4 mr-2 text-primary" />
                      Tarjeta gráfica
                    </h4>
                    <Table>
                      <TableBody>
                        <TableRow>
                          <TableCell className="font-medium">Modelo</TableCell>
                          <TableCell>{gpuInfo.model}</TableCell>
                        </TableRow>
                        {gpuInfo.vram_gb && (
                          <TableRow>
                            <TableCell className="font-medium">VRAM</TableCell>
                            <TableCell>{gpuInfo.vram_gb} GB</TableCell>
                          </TableRow>
                        )}
                        {laptop.laptop_gpus?.[0]?.is_discrete !== undefined && (
                          <TableRow>
                            <TableCell className="font-medium">Tipo</TableCell>
                            <TableCell>
                              {laptop.laptop_gpus[0].is_discrete ? 'Dedicada' : 'Integrada'}
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium flex items-center mb-2">
                      <MemoryStick className="h-4 w-4 mr-2 text-primary" />
                      Memoria RAM
                    </h4>
                    <Table>
                      <TableBody>
                        <TableRow>
                          <TableCell className="font-medium">Capacidad</TableCell>
                          <TableCell>{ramInfo.size_gb} GB</TableCell>
                        </TableRow>
                        {ramInfo.speed_mhz && (
                          <TableRow>
                            <TableCell className="font-medium">Velocidad</TableCell>
                            <TableCell>{ramInfo.speed_mhz} MHz</TableCell>
                          </TableRow>
                        )}
                        {ramInfo.memory_types?.name && (
                          <TableRow>
                            <TableCell className="font-medium">Tipo</TableCell>
                            <TableCell>{ramInfo.memory_types.name}</TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium flex items-center mb-2">
                      <HardDrive className="h-4 w-4 mr-2 text-primary" />
                      Almacenamiento
                    </h4>
                    <Table>
                      <TableBody>
                        <TableRow>
                          <TableCell className="font-medium">Capacidad</TableCell>
                          <TableCell>{storageInfo.capacity_gb} GB</TableCell>
                        </TableRow>
                        {storageInfo.storage_interfaces?.name && (
                          <TableRow>
                            <TableCell className="font-medium">Interfaz</TableCell>
                            <TableCell>{storageInfo.storage_interfaces.name}</TableCell>
                          </TableRow>
                        )}
                        {storageInfo.is_nvme !== undefined && (
                          <TableRow>
                            <TableCell className="font-medium">NVMe</TableCell>
                            <TableCell>{storageInfo.is_nvme ? 'Sí' : 'No'}</TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium flex items-center mb-2">
                      <Monitor className="h-4 w-4 mr-2 text-primary" />
                      Pantalla
                    </h4>
                    <Table>
                      <TableBody>
                        <TableRow>
                          <TableCell className="font-medium">Tamaño</TableCell>
                          <TableCell>{displayInfo.size_inches}"</TableCell>
                        </TableRow>
                        {displayInfo.resolution_types?.name && (
                          <TableRow>
                            <TableCell className="font-medium">Resolución</TableCell>
                            <TableCell>{displayInfo.resolution_types.name}</TableCell>
                          </TableRow>
                        )}
                        {displayInfo.refresh_rate && (
                          <TableRow>
                            <TableCell className="font-medium">Tasa de refresco</TableCell>
                            <TableCell>{displayInfo.refresh_rate} Hz</TableCell>
                          </TableRow>
                        )}
                        {displayInfo.panel_types?.name && (
                          <TableRow>
                            <TableCell className="font-medium">Tipo de panel</TableCell>
                            <TableCell>{displayInfo.panel_types.name}</TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium flex items-center mb-2">
                      <Scale className="h-4 w-4 mr-2 text-primary" />
                      Características físicas
                    </h4>
                    <Table>
                      <TableBody>
                        <TableRow>
                          <TableCell className="font-medium">Peso</TableCell>
                          <TableCell>{physicalInfo.weight_kg} kg</TableCell>
                        </TableRow>
                        {physicalInfo.height_mm && physicalInfo.width_mm && physicalInfo.depth_mm && (
                          <TableRow>
                            <TableCell className="font-medium">Dimensiones</TableCell>
                            <TableCell>
                              {physicalInfo.width_mm} × {physicalInfo.depth_mm} × {physicalInfo.height_mm} mm
                            </TableCell>
                          </TableRow>
                        )}
                        {physicalInfo.color && (
                          <TableRow>
                            <TableCell className="font-medium">Color</TableCell>
                            <TableCell>{physicalInfo.color}</TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              </div>

              {sortedCompatibility.length > 0 && (
                <>
                  <Separator />

                  <div>
                    <h3 className="text-lg font-semibold mb-2">Compatibilidad con LLMs</h3>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Modelo</TableHead>
                          <TableHead>Compatibilidad</TableHead>
                          <TableHead>Tokens/s</TableHead>
                          <TableHead>Offline</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {sortedCompatibility
                          .slice(0, 5)
                          .map((compat: LaptopLlmCompatibility, index: number) => (
                            <TableRow key={`${compat.llm_id}-table-${index}`}>
                              <TableCell className="font-medium">
                                {compat.llm_models?.name || 'Unknown LLM'}
                              </TableCell>
                              <TableCell>
                                <Badge
                                  variant={
                                    compat.score >= 80 ? "default" :
                                    compat.score >= 60 ? "secondary" :
                                    "outline"
                                  }
                                >
                                  {compat.score}%
                                </Badge>
                              </TableCell>
                              <TableCell>{compat.estimated_tokens_per_second || 'N/A'}</TableCell>
                              <TableCell>{compat.can_run_offline ? 'Sí' : 'No'}</TableCell>
                            </TableRow>
                          ))
                        }
                      </TableBody>
                    </Table>

                    {sortedCompatibility.length > 5 && (
                      <div className="mt-2 text-right">
                        <Link to={`/llm-compatibility?laptop=${laptop.id}`}>
                          <Button variant="link" size="sm">
                            Ver todos los modelos compatibles
                          </Button>
                        </Link>
                      </div>
                    )}
                  </div>
                </>
              )}
            </div>
          </DialogContent>
        </Dialog>
      </CardFooter>
    </Card>
  );
});

export default LaptopSpecsCard;
