
import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON><PERSON>, Pie, Cell } from 'recharts';
import { LaptopListing } from '@/types/database';

interface SpecsDistributionProps {
  laptops: LaptopListing[] | undefined;
}

const SpecsDistribution = ({ laptops }: SpecsDistributionProps) => {
  if (!laptops || laptops.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Specifications Distribution</CardTitle>
          <CardDescription>No data available</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  // Helper function to safely get RAM size
  const getRamSize = (specs: any) => {
    if (typeof specs?.ram === 'object' && specs.ram?.size) {
      return specs.ram.size;
    }
    if (typeof specs?.ram === 'number') {
      return specs.ram;
    }
    return 0;
  };

  // Helper function to safely get storage size
  const getStorageSize = (specs: any) => {
    if (typeof specs?.storage === 'object' && specs.storage?.size) {
      return specs.storage.size;
    }
    if (typeof specs?.storage === 'string') {
      return parseInt(specs.storage) || 0;
    }
    return 0;
  };

  // RAM distribution
  const ramDistribution = laptops.reduce((acc, laptop) => {
    const ram = getRamSize(laptop.specs);
    const ramKey = `${ram}GB`;
    acc[ramKey] = (acc[ramKey] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // Storage distribution
  const storageDistribution = laptops.reduce((acc, laptop) => {
    const storage = getStorageSize(laptop.specs);
    const storageKey = storage >= 1000 ? `${Math.floor(storage / 1000)}TB` : `${storage}GB`;
    acc[storageKey] = (acc[storageKey] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const ramData = Object.entries(ramDistribution).map(([name, value]) => ({ name, value }));
  const storageData = Object.entries(storageDistribution).map(([name, value]) => ({ name, value }));

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <Card>
        <CardHeader>
          <CardTitle className="text-sm font-medium">RAM Distribution</CardTitle>
        </CardHeader>
        <CardContent className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={ramData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} (${(percent * 100).toFixed(0)}%)`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {ramData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-sm font-medium">Storage Distribution</CardTitle>
        </CardHeader>
        <CardContent className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={storageData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="value" fill="#8884d8" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );
};

export default SpecsDistribution;
