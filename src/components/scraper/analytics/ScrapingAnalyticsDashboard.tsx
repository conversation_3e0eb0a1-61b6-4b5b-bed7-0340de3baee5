
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';
import { Activity, AlertTriangle, CheckCircle, Clock, Download, Eye, Globe, Shield, TrendingUp, Zap } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface ScrapingMetrics {
  totalRecords: number;
  recordsLast5Min: number;
  averageSpeed: number;
  currentSpeed: number;
  progressPercentage: number;
  activeProcesses: number;
  errorCount: number;
  successRate: number;
}

interface DataSource {
  id: string;
  name: string;
  url: string;
  status: 'active' | 'delayed' | 'error' | 'inactive';
  recordsExtracted: number;
  lastUpdate: string;
  estimatedCompletion: string;
  robotsTxtCompliant: boolean;
  rateLimit: number;
  sectionsScanned: string[];
  dataSchema: {
    fields: string[];
    types: Record<string, string>;
  };
}

interface ErrorLog {
  id: string;
  timestamp: string;
  type: 'network' | 'parsing' | 'rate_limit' | 'validation';
  description: string;
  sourceId: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

const ScrapingAnalyticsDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<ScrapingMetrics>({
    totalRecords: 0,
    recordsLast5Min: 0,
    averageSpeed: 0,
    currentSpeed: 0,
    progressPercentage: 0,
    activeProcesses: 0,
    errorCount: 0,
    successRate: 0
  });

  const [dataSources, setDataSources] = useState<DataSource[]>([
    {
      id: 'ola-click',
      name: 'laptop-ventas.ola.click',
      url: 'https://laptop-ventas.ola.click/products',
      status: 'active',
      recordsExtracted: 847,
      lastUpdate: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
      estimatedCompletion: new Date(Date.now() + 15 * 60 * 1000).toISOString(),
      robotsTxtCompliant: true,
      rateLimit: 2000,
      sectionsScanned: ['/products', '/api/categories', '/api/products'],
      dataSchema: {
        fields: ['id', 'name', 'description', 'price', 'category', 'images', 'visible'],
        types: {
          id: 'string',
          name: 'string',
          description: 'text',
          price: 'number',
          category: 'string',
          images: 'array',
          visible: 'boolean'
        }
      }
    },
    {
      id: 'revolico',
      name: 'revolico.com',
      url: 'https://revolico.com/computadoras/laptops',
      status: 'delayed',
      recordsExtracted: 1234,
      lastUpdate: new Date(Date.now() - 8 * 60 * 1000).toISOString(),
      estimatedCompletion: new Date(Date.now() + 45 * 60 * 1000).toISOString(),
      robotsTxtCompliant: true,
      rateLimit: 3000,
      sectionsScanned: ['/computadoras/laptops', '/api/search', '/graphql'],
      dataSchema: {
        fields: ['id', 'title', 'description', 'price', 'location', 'seller', 'images'],
        types: {
          id: 'string',
          title: 'string',
          description: 'text',
          price: 'string',
          location: 'string',
          seller: 'object',
          images: 'array'
        }
      }
    }
  ]);

  const [errorLogs, setErrorLogs] = useState<ErrorLog[]>([
    {
      id: '1',
      timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
      type: 'rate_limit',
      description: 'Rate limit exceeded for revolico.com - backing off for 30 seconds',
      sourceId: 'revolico',
      severity: 'medium'
    },
    {
      id: '2',
      timestamp: new Date(Date.now() - 12 * 60 * 1000).toISOString(),
      type: 'parsing',
      description: 'Failed to parse CPU specification from description field',
      sourceId: 'ola-click',
      severity: 'low'
    }
  ]);

  const [speedHistory, setSpeedHistory] = useState([
    { time: '10:00', speed: 45 },
    { time: '10:05', speed: 52 },
    { time: '10:10', speed: 38 },
    { time: '10:15', speed: 61 },
    { time: '10:20', speed: 47 },
    { time: '10:25', speed: 55 }
  ]);

  // Simulated real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      setMetrics(prev => ({
        ...prev,
        totalRecords: prev.totalRecords + Math.floor(Math.random() * 10),
        recordsLast5Min: Math.floor(Math.random() * 50) + 20,
        currentSpeed: Math.floor(Math.random() * 30) + 40,
        progressPercentage: Math.min(prev.progressPercentage + Math.random() * 2, 100),
        activeProcesses: dataSources.filter(ds => ds.status === 'active').length
      }));

      // Update speed history
      setSpeedHistory(prev => {
        const newEntry = {
          time: new Date().toLocaleTimeString('es-ES', { hour: '2-digit', minute: '2-digit' }),
          speed: Math.floor(Math.random() * 30) + 40
        };
        return [...prev.slice(-5), newEntry];
      });
    }, 60000); // Update every 60 seconds

    return () => clearInterval(interval);
  }, [dataSources]);

  const getStatusBadge = (status: DataSource['status']) => {
    const variants = {
      active: { variant: 'default' as const, color: 'bg-green-500', icon: CheckCircle },
      delayed: { variant: 'secondary' as const, color: 'bg-yellow-500', icon: Clock },
      error: { variant: 'destructive' as const, color: 'bg-red-500', icon: AlertTriangle },
      inactive: { variant: 'outline' as const, color: 'bg-gray-500', icon: Eye }
    };

    const config = variants[status];
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const exportReport = async (format: 'pdf' | 'excel') => {
    // Simulate export process
    toast({
      title: "Generando Reporte",
      description: `Exportando reporte técnico en formato ${format.toUpperCase()}...`,
    });

    setTimeout(() => {
      toast({
        title: "Reporte Generado",
        description: `El reporte técnico ha sido exportado exitosamente en formato ${format.toUpperCase()}.`,
      });
    }, 2000);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Análisis Técnico de Web Scraping</h1>
          <p className="text-muted-foreground">
            Dashboard de monitoreo en tiempo real - Actualización automática cada 60s
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => exportReport('pdf')} variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Exportar PDF
          </Button>
          <Button onClick={() => exportReport('excel')} variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Exportar Excel
          </Button>
        </div>
      </div>

      <Tabs defaultValue="metrics" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="metrics">Métricas en Tiempo Real</TabsTrigger>
          <TabsTrigger value="sources">Análisis por Fuente</TabsTrigger>
          <TabsTrigger value="compliance">Cumplimiento Técnico</TabsTrigger>
          <TabsTrigger value="logs">Logs y Errores</TabsTrigger>
        </TabsList>

        <TabsContent value="metrics" className="space-y-4">
          {/* Real-time Metrics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Registros Totales</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.totalRecords.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  +{metrics.recordsLast5Min} últimos 5 min
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Velocidad Actual</CardTitle>
                <Zap className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.currentSpeed}</div>
                <p className="text-xs text-muted-foreground">
                  registros/minuto (avg: {metrics.averageSpeed})
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Progreso Global</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.progressPercentage.toFixed(1)}%</div>
                <Progress value={metrics.progressPercentage} className="mt-2" />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Procesos Activos</CardTitle>
                <Globe className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.activeProcesses}</div>
                <p className="text-xs text-muted-foreground">
                  de {dataSources.length} fuentes configuradas
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Speed Trend Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Tendencia de Velocidad de Extracción</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={speedHistory}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="time" />
                  <YAxis />
                  <Tooltip />
                  <Line 
                    type="monotone" 
                    dataKey="speed" 
                    stroke="#8884d8" 
                    strokeWidth={2}
                    dot={{ r: 4 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sources" className="space-y-4">
          <div className="grid gap-4">
            {dataSources.map((source) => (
              <Card key={source.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        {source.name}
                        {getStatusBadge(source.status)}
                      </CardTitle>
                      <p className="text-sm text-muted-foreground">{source.url}</p>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold">{source.recordsExtracted.toLocaleString()}</div>
                      <p className="text-xs text-muted-foreground">registros extraídos</p>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <h4 className="font-semibold mb-2">Marcas Temporales</h4>
                      <div className="space-y-1 text-sm">
                        <div>
                          <span className="text-muted-foreground">Última actualización:</span>
                          <br />
                          {new Date(source.lastUpdate).toLocaleString('es-ES')}
                        </div>
                        <div>
                          <span className="text-muted-foreground">Finalización estimada:</span>
                          <br />
                          {new Date(source.estimatedCompletion).toLocaleString('es-ES')}
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-semibold mb-2">Secciones Escaneadas</h4>
                      <div className="flex flex-wrap gap-1">
                        {source.sectionsScanned.map((section, idx) => (
                          <Badge key={idx} variant="outline" className="text-xs">
                            {section}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h4 className="font-semibold mb-2">Configuración</h4>
                      <div className="space-y-1 text-sm">
                        <div className="flex items-center gap-2">
                          <Shield className="h-3 w-3" />
                          robots.txt: {source.robotsTxtCompliant ? '✅ Cumple' : '❌ No cumple'}
                        </div>
                        <div>
                          Rate limit: {source.rateLimit}ms
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold mb-2">Esquema de Datos Extraído</h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                      {source.dataSchema.fields.map((field) => (
                        <Badge key={field} variant="secondary" className="text-xs">
                          {field}: {source.dataSchema.types[field]}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="compliance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Reporte de Cumplimiento Técnico</CardTitle>
              <p className="text-sm text-muted-foreground">
                Documentación detallada del cumplimiento de estándares técnicos y éticos
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-3">Metodología de Extracción</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>API RESTful para laptop-ventas.ola.click (JSON estructurado)</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>GraphQL API para revolico.com</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Parsing de texto libre para especificaciones técnicas</span>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-3">Cumplimiento robots.txt</h3>
                <div className="space-y-2">
                  {dataSources.map((source) => (
                    <div key={source.id} className="flex items-center justify-between p-2 bg-muted rounded">
                      <span className="text-sm">{source.name}</span>
                      <div className="flex items-center gap-2">
                        {source.robotsTxtCompliant ? (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        ) : (
                          <AlertTriangle className="h-4 w-4 text-red-500" />
                        )}
                        <span className="text-sm">
                          {source.robotsTxtCompliant ? 'Cumple' : 'No cumple'}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-3">Rate Limiting y Delays</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {dataSources.map((source) => (
                    <div key={source.id} className="p-3 border rounded">
                      <h4 className="font-medium">{source.name}</h4>
                      <div className="text-sm text-muted-foreground mt-1">
                        Delay configurado: {source.rateLimit}ms
                        <br />
                        {source.rateLimit >= 2000 ? (
                          <span className="text-green-600">✅ Cumple mínimo 2s</span>
                        ) : (
                          <span className="text-red-600">❌ Menor a 2s requeridos</span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-3">Validación y Limpieza de Datos</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Validación de campos requeridos (id, name, price)</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Filtrado por productos visibles/activos</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Normalización de precios y especificaciones</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Detección y manejo de duplicados</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="logs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Log de Errores en Tiempo Real</CardTitle>
              <p className="text-sm text-muted-foreground">
                Registro cronológico de incidencias y resoluciones
              </p>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {errorLogs.map((log) => (
                  <div 
                    key={log.id} 
                    className={`p-3 rounded border-l-4 ${
                      log.severity === 'critical' 
                        ? 'border-red-500 bg-red-50' 
                        : log.severity === 'high'
                        ? 'border-orange-500 bg-orange-50'
                        : log.severity === 'medium'
                        ? 'border-yellow-500 bg-yellow-50'
                        : 'border-blue-500 bg-blue-50'
                    }`}
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <Badge 
                            variant={
                              log.severity === 'critical' || log.severity === 'high' 
                                ? 'destructive' 
                                : log.severity === 'medium' 
                                ? 'secondary' 
                                : 'default'
                            }
                          >
                            {log.type}
                          </Badge>
                          <span className="text-sm text-muted-foreground">
                            {dataSources.find(ds => ds.id === log.sourceId)?.name}
                          </span>
                        </div>
                        <p className="text-sm">{log.description}</p>
                      </div>
                      <span className="text-xs text-muted-foreground">
                        {new Date(log.timestamp).toLocaleString('es-ES')}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ScrapingAnalyticsDashboard;
