
/**
 * Componente base para las páginas de scraper
 *
 * Este componente proporciona la estructura común para las páginas de scraper,
 * permitiendo personalizar el encabezado y el contenido.
 */

import { useState, ReactNode, useMemo } from 'react';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertTriangle } from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import ScraperContent from '@/components/scraper/ScraperContent';
import DataManagementPanel from '@/components/scraper/DataManagementPanel';
import AdvancedVisualization from '@/components/scraper/AdvancedVisualization';
import LLMIntegration from '@/components/scraper/LLMIntegration';
import ScraperLayout from '@/components/scraper/ScraperLayout';
import ErrorBoundary from '@/components/ErrorBoundary';
import LoadingState from '@/components/LoadingState';
import { LaptopListing, ScrapingHistory, ScrapingSource, ScrapingStats } from '@/types/database';
import { DetailedLaptop } from '@/types/laptop';

interface ScraperPageBaseProps {
  header: ReactNode;
  sources?: ScrapingSource[];
  history?: ScrapingHistory[];
  laptops?: LaptopListing[];
  stats?: ScrapingStats;
  isLoading: boolean;
  isError: boolean;
  errorMessage?: string;
  withErrorBoundary?: boolean;
  withScraperConfigProvider?: boolean;
}

const ScraperPageBase = ({
  header,
  sources,
  history,
  laptops,
  stats,
  isLoading,
  isError,
  errorMessage = "There was a problem loading the scraper data. Please try again later.",
  withErrorBoundary = true,
  withScraperConfigProvider = false,
}: ScraperPageBaseProps) => {
  const [activeTab, setActiveTab] = useState('dashboard');

  // Convert LaptopListing[] to DetailedLaptop[] for LLMIntegration component
  const detailedLaptops = useMemo(() => {
    if (!laptops) return [];

    return laptops.map(laptop => {
      // Extract brand from specs or use a default
      const brand = laptop.specs?.brand?.toString() || 'Unknown';

      return {
        id: parseInt(laptop.id) || 0,
        model_name: laptop.name,
        msrp: laptop.price,
        url: laptop.url || undefined,
        image_url: laptop.image_url || undefined,
        source_url: laptop.source_url,
        created_at: laptop.created_at,
        updated_at: laptop.updated_at,
        data: {
          name: laptop.name,
          price: laptop.price,
          specs: {
            cpu: laptop.specs?.processor ? { model: laptop.specs.processor } : undefined,
            gpu: laptop.specs?.graphics ? { model: laptop.specs.graphics } : undefined,
            ram: typeof laptop.specs?.ram === 'number'
              ? { size_gb: laptop.specs.ram }
              : { size_gb: laptop.specs?.ram || '0' },
            storage: typeof laptop.specs?.storage === 'string'
              ? { capacity_gb: laptop.specs.storage }
              : { capacity_gb: '0' },
            brand
          }
        }
      };
    });
  }, [laptops]);

  // Render error state
  if (isError) {
    return (
      <ScraperLayout title="Laptop Scraper" description="Collect and analyze laptop data">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Error Loading Data</AlertTitle>
          <AlertDescription>{errorMessage}</AlertDescription>
        </Alert>
      </ScraperLayout>
    );
  }

  const content = (
    <>
      {header}

      {isLoading ? (
        <LoadingState message="Loading scraper data..." />
      ) : (
        <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid grid-cols-1 md:grid-cols-4 mb-4">
            <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
            <TabsTrigger value="visualizations">Advanced Visualizations</TabsTrigger>
            <TabsTrigger value="data-management">Data Management</TabsTrigger>
            <TabsTrigger value="analysis">LLM Analysis</TabsTrigger>
          </TabsList>

          <TabsContent value="dashboard">
            {withErrorBoundary ? (
              <ErrorBoundary>
                <ScraperContent
                  sources={sources}
                  history={history}
                  laptops={laptops}
                  stats={stats}
                  isLoading={isLoading}
                />
              </ErrorBoundary>
            ) : (
              <ScraperContent
                sources={sources}
                history={history}
                laptops={laptops}
                stats={stats}
                isLoading={isLoading}
              />
            )}
          </TabsContent>

          <TabsContent value="visualizations">
            {withErrorBoundary ? (
              <ErrorBoundary>
                <AdvancedVisualization
                  laptops={laptops}
                  isLoading={isLoading}
                />
              </ErrorBoundary>
            ) : (
              <AdvancedVisualization
                laptops={laptops}
                isLoading={isLoading}
              />
            )}
          </TabsContent>

          <TabsContent value="data-management">
            {withErrorBoundary ? (
              <ErrorBoundary>
                <DataManagementPanel
                  laptops={laptops}
                  isLoading={isLoading}
                />
              </ErrorBoundary>
            ) : (
              <DataManagementPanel
                laptops={laptops}
                isLoading={isLoading}
              />
            )}
          </TabsContent>

          <TabsContent value="analysis">
            {withErrorBoundary ? (
              <ErrorBoundary>
                <LLMIntegration
                  laptopData={detailedLaptops}
                />
              </ErrorBoundary>
            ) : (
              <LLMIntegration
                laptopData={detailedLaptops}
              />
            )}
          </TabsContent>
        </Tabs>
      )}
    </>
  );

  return (
    <ScraperLayout title="Laptop Scraper" description="Collect and analyze laptop data">
      {withErrorBoundary ? (
        <ErrorBoundary>{content}</ErrorBoundary>
      ) : (
        content
      )}
    </ScraperLayout>
  );
};

export default ScraperPageBase;
