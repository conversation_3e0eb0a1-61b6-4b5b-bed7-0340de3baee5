
import { useState } from "react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { LaptopListing } from "@/types/database";
import { ExternalLink } from "lucide-react";

interface ScraperResultsTableProps {
  results: LaptopListing[];
}

const ScraperResultsTable = ({ results }: ScraperResultsTableProps) => {
  const [selectedLaptop, setSelectedLaptop] = useState<LaptopListing | null>(null);
  
  if (results.length === 0) {
    return (
      <div className="text-center p-8 border rounded-md bg-muted/20">
        <p className="text-muted-foreground">No scraping results available.</p>
        <p className="text-sm text-muted-foreground mt-2">
          Configure and run the scraper to see results here.
        </p>
      </div>
    );
  }

  // Helper function to safely get CPU name
  const getCpuName = (specs: any) => {
    if (typeof specs?.cpu === 'object' && specs.cpu?.name) {
      return specs.cpu.name;
    }
    if (typeof specs?.processor === 'string') {
      return specs.processor;
    }
    return 'Unknown CPU';
  };

  // Helper function to safely get GPU name
  const getGpuName = (specs: any) => {
    if (typeof specs?.gpu === 'object' && specs.gpu?.name) {
      return specs.gpu.name;
    }
    if (typeof specs?.graphics === 'string') {
      return specs.graphics;
    }
    return 'Unknown GPU';
  };

  // Helper function to safely get RAM size
  const getRamSize = (specs: any) => {
    if (typeof specs?.ram === 'object' && specs.ram?.size) {
      return specs.ram.size;
    }
    if (typeof specs?.ram === 'number') {
      return specs.ram;
    }
    return 0;
  };

  // Helper function to safely get storage size
  const getStorageSize = (specs: any) => {
    if (typeof specs?.storage === 'object' && specs.storage?.size) {
      return specs.storage.size;
    }
    if (typeof specs?.storage === 'string') {
      return specs.storage;
    }
    return 'Unknown';
  };

  return (
    <div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Price</TableHead>
              <TableHead>Specs</TableHead>
              <TableHead>Source</TableHead>
              <TableHead>Last Updated</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {results.map((laptop) => (
              <TableRow key={laptop.id}>
                <TableCell className="font-medium">{laptop.name}</TableCell>
                <TableCell>${laptop.price?.toLocaleString()}</TableCell>
                <TableCell>
                  <div className="flex flex-wrap gap-1 max-w-xs">
                    {getCpuName(laptop.specs) !== 'Unknown CPU' && (
                      <Badge variant="outline" className="bg-muted/50">
                        {getCpuName(laptop.specs).substring(0, 15)}{getCpuName(laptop.specs).length > 15 ? '...' : ''}
                      </Badge>
                    )}
                    {getRamSize(laptop.specs) > 0 && (
                      <Badge variant="outline" className="bg-muted/50">
                        {getRamSize(laptop.specs)}GB RAM
                      </Badge>
                    )}
                    {getStorageSize(laptop.specs) !== 'Unknown' && (
                      <Badge variant="outline" className="bg-muted/50">
                        {getStorageSize(laptop.specs)}
                      </Badge>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <a 
                    href={laptop.source_url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-blue-500 hover:underline flex items-center"
                  >
                    {laptop.source_url ? new URL(laptop.source_url).hostname : 'Unknown'}
                    <ExternalLink className="h-3 w-3 ml-1" />
                  </a>
                </TableCell>
                <TableCell>
                  <Badge variant="outline" className="bg-muted/50">
                    {new Date(laptop.updated_at).toLocaleDateString()}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => setSelectedLaptop(laptop)}
                  >
                    Details
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <Dialog open={!!selectedLaptop} onOpenChange={(open) => !open && setSelectedLaptop(null)}>
        {selectedLaptop && (
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle>{selectedLaptop.name}</DialogTitle>
              <DialogDescription>
                Listing from {new URL(selectedLaptop.source_url).hostname} - Updated {new Date(selectedLaptop.updated_at).toLocaleString()}
              </DialogDescription>
            </DialogHeader>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium mb-2">Specifications</h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">CPU:</span>
                    <span>{getCpuName(selectedLaptop.specs)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">GPU:</span>
                    <span>{getGpuName(selectedLaptop.specs)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">RAM:</span>
                    <span>{getRamSize(selectedLaptop.specs)}GB</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Storage:</span>
                    <span>{getStorageSize(selectedLaptop.specs)}</span>
                  </div>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Listing Info</h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Price:</span>
                    <span className="font-medium">${selectedLaptop.price.toLocaleString()}</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Source URL:</span>
                    <a 
                      href={selectedLaptop.source_url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-blue-500 hover:underline"
                    >
                      View listing
                    </a>
                  </div>
                  
                  {selectedLaptop.url && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Product URL:</span>
                      <a 
                        href={selectedLaptop.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-500 hover:underline"
                      >
                        View product
                      </a>
                    </div>
                  )}
                  
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Created:</span>
                    <span>{new Date(selectedLaptop.created_at).toLocaleString()}</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Updated:</span>
                    <span>{new Date(selectedLaptop.updated_at).toLocaleString()}</span>
                  </div>
                </div>
              </div>
            </div>
            
            {selectedLaptop.image_url && (
              <div className="mt-4">
                <h4 className="font-medium mb-2">Product Image</h4>
                <div className="aspect-video bg-muted/20 rounded-md flex items-center justify-center">
                  <img 
                    src={selectedLaptop.image_url} 
                    alt={selectedLaptop.name} 
                    className="max-h-full max-w-full object-contain rounded-md"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = "/placeholder.svg";
                      target.onerror = null;
                    }}
                  />
                </div>
              </div>
            )}

            <div className="flex justify-end mt-4">
              <Button asChild variant="outline">
                <a href={selectedLaptop.source_url} target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="mr-2 h-4 w-4" />
                  View Original Listing
                </a>
              </Button>
            </div>
          </DialogContent>
        )}
      </Dialog>
    </div>
  );
};

export default ScraperResultsTable;
