import { useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, PieChart, Pie, Cell, ResponsiveContainer } from 'recharts';
import { Loader2 } from 'lucide-react';
import { useVisualization } from '@/hooks/ui/use-visualization';
import { LaptopListing } from '@/types/database';

interface AdvancedVisualizationProps {
  laptops?: LaptopListing[];
  isLoading: boolean;
}

const AdvancedVisualization = ({ laptops = [], isLoading }: AdvancedVisualizationProps) => {
  const {
    priceDistribution,
    brandDistribution,
    ramDistribution,
    storageDistribution,
    avgPriceByBrand,
    totalLaptops,
    avgPrice
  } = useVisualization(laptops);

  const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#8dd1e1', '#a4de6c', '#d084d0'];

  const renderCustomTooltip = (props: any) => {
    const { active, payload } = props;
    if (active && payload && payload.length) {
      return (
        <Card className="bg-popover text-popover-foreground p-4">
          <CardContent>
            <p className="font-bold">{payload[0].payload.name}</p>
            <p className="text-sm">{`Count: ${payload[0].value}`}</p>
          </CardContent>
        </Card>
      );
    }
    return null;
  };

  if (isLoading) {
    return (
      <Card className="w-full p-8 text-center">
        <Loader2 className="h-8 w-8 mx-auto animate-spin mb-4" />
        <p className="text-muted-foreground">Loading data...</p>
      </Card>
    );
  }

  return (
    <Tabs defaultValue="price" className="space-y-4">
      <TabsList className="grid grid-cols-1 md:grid-cols-4">
        <TabsTrigger value="price">Price Distribution</TabsTrigger>
        <TabsTrigger value="brand">Brand Distribution</TabsTrigger>
        <TabsTrigger value="ram">RAM Distribution</TabsTrigger>
        <TabsTrigger value="storage">Storage Distribution</TabsTrigger>
      </TabsList>

      <TabsContent value="price" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Price Distribution</CardTitle>
            <CardDescription>Distribution of laptops across different price ranges.</CardDescription>
          </CardHeader>
          <CardContent>
            {priceDistribution && priceDistribution.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={priceDistribution}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="range" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="count" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                No price data available.
              </div>
            )}
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="brand" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Brand Distribution</CardTitle>
            <CardDescription>Distribution of laptops by brand.</CardDescription>
          </CardHeader>
          <CardContent>
            {brandDistribution && brandDistribution.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    dataKey="value"
                    data={brandDistribution}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    label
                  >
                    {brandDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip content={renderCustomTooltip} />
                </PieChart>
              </ResponsiveContainer>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                No brand data available.
              </div>
            )}
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="ram" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>RAM Distribution</CardTitle>
            <CardDescription>Distribution of laptops by RAM size.</CardDescription>
          </CardHeader>
          <CardContent>
            {ramDistribution && ramDistribution.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={ramDistribution}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="value" fill="#82ca9d" />
                </BarChart>
              </ResponsiveContainer>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                No RAM data available.
              </div>
            )}
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="storage" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Storage Distribution</CardTitle>
            <CardDescription>Distribution of laptops by storage type.</CardDescription>
          </CardHeader>
          <CardContent>
            {storageDistribution && storageDistribution.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    dataKey="value"
                    data={storageDistribution}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#ffc658"
                    label
                  >
                    {storageDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip content={renderCustomTooltip} />
                </PieChart>
              </ResponsiveContainer>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                No storage data available.
              </div>
            )}
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  );
};

export default AdvancedVisualization;
