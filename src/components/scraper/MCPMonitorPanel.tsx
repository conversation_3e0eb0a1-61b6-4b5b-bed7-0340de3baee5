import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { AlertCircle, CheckCircle, Clock, XCircle } from 'lucide-react';

// Tipos
interface ScrapingJob {
  id: string;
  source: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  startTime: string;
  endTime?: string;
  itemsScraped: number;
  pagesScraped: number;
  errors: string[];
}

interface ScrapingLog {
  id: string;
  timestamp: string;
  level: 'info' | 'warning' | 'error';
  message: string;
  source: string;
  details?: string;
}

interface MCPMonitorPanelProps {
  onRefresh?: () => void;
}

const MCPMonitorPanel: React.FC<MCPMonitorPanelProps> = ({ onRefresh }) => {
  const [activeTab, setActiveTab] = useState('jobs');
  const [jobs, setJobs] = useState<ScrapingJob[]>([]);
  const [logs, setLogs] = useState<ScrapingLog[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Datos de ejemplo para la demostración
  useEffect(() => {
    // Simular carga de datos
    setIsLoading(true);
    
    // Datos de ejemplo para trabajos de scraping
    const exampleJobs: ScrapingJob[] = [
      {
        id: '1',
        source: 'laptop-ventas.ola.click',
        status: 'completed',
        progress: 100,
        startTime: '2023-05-15T10:30:00Z',
        endTime: '2023-05-15T10:35:00Z',
        itemsScraped: 45,
        pagesScraped: 3,
        errors: []
      },
      {
        id: '2',
        source: 'gaminglaptop.deals',
        status: 'running',
        progress: 65,
        startTime: '2023-05-15T11:00:00Z',
        itemsScraped: 28,
        pagesScraped: 2,
        errors: []
      },
      {
        id: '3',
        source: 'revolico.com',
        status: 'failed',
        progress: 30,
        startTime: '2023-05-15T09:45:00Z',
        endTime: '2023-05-15T09:48:00Z',
        itemsScraped: 12,
        pagesScraped: 1,
        errors: ['Error de conexión: Tiempo de espera agotado', 'No se pudo acceder a la página 2']
      },
      {
        id: '4',
        source: 'laptop-ventas.ola.click',
        status: 'pending',
        progress: 0,
        startTime: '2023-05-15T12:00:00Z',
        itemsScraped: 0,
        pagesScraped: 0,
        errors: []
      }
    ];
    
    // Datos de ejemplo para logs
    const exampleLogs: ScrapingLog[] = [
      {
        id: '1',
        timestamp: '2023-05-15T10:30:00Z',
        level: 'info',
        message: 'Iniciando scraping de laptop-ventas.ola.click',
        source: 'laptop-ventas.ola.click'
      },
      {
        id: '2',
        timestamp: '2023-05-15T10:32:00Z',
        level: 'info',
        message: 'Página 1 procesada correctamente',
        source: 'laptop-ventas.ola.click'
      },
      {
        id: '3',
        timestamp: '2023-05-15T10:33:00Z',
        level: 'info',
        message: 'Página 2 procesada correctamente',
        source: 'laptop-ventas.ola.click'
      },
      {
        id: '4',
        timestamp: '2023-05-15T10:34:00Z',
        level: 'info',
        message: 'Página 3 procesada correctamente',
        source: 'laptop-ventas.ola.click'
      },
      {
        id: '5',
        timestamp: '2023-05-15T10:35:00Z',
        level: 'info',
        message: 'Scraping completado con éxito',
        source: 'laptop-ventas.ola.click'
      },
      {
        id: '6',
        timestamp: '2023-05-15T11:00:00Z',
        level: 'info',
        message: 'Iniciando scraping de gaminglaptop.deals',
        source: 'gaminglaptop.deals'
      },
      {
        id: '7',
        timestamp: '2023-05-15T11:02:00Z',
        level: 'info',
        message: 'Página 1 procesada correctamente',
        source: 'gaminglaptop.deals'
      },
      {
        id: '8',
        timestamp: '2023-05-15T11:05:00Z',
        level: 'info',
        message: 'Página 2 procesada correctamente',
        source: 'gaminglaptop.deals'
      },
      {
        id: '9',
        timestamp: '2023-05-15T09:45:00Z',
        level: 'info',
        message: 'Iniciando scraping de revolico.com',
        source: 'revolico.com'
      },
      {
        id: '10',
        timestamp: '2023-05-15T09:47:00Z',
        level: 'warning',
        message: 'Tiempo de respuesta lento en revolico.com',
        source: 'revolico.com'
      },
      {
        id: '11',
        timestamp: '2023-05-15T09:48:00Z',
        level: 'error',
        message: 'Error de conexión: Tiempo de espera agotado',
        source: 'revolico.com',
        details: 'Error: ETIMEDOUT'
      }
    ];
    
    setJobs(exampleJobs);
    setLogs(exampleLogs);
    setIsLoading(false);
  }, []);

  // Función para formatear la fecha
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Función para calcular la duración
  const calculateDuration = (startTime: string, endTime?: string) => {
    const start = new Date(startTime).getTime();
    const end = endTime ? new Date(endTime).getTime() : Date.now();
    const durationMs = end - start;
    
    const seconds = Math.floor(durationMs / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    return `${minutes}m ${remainingSeconds}s`;
  };

  // Renderizar el estado del trabajo
  const renderJobStatus = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-500"><CheckCircle className="h-3 w-3 mr-1" /> Completado</Badge>;
      case 'running':
        return <Badge className="bg-blue-500"><Clock className="h-3 w-3 mr-1" /> En Progreso</Badge>;
      case 'failed':
        return <Badge className="bg-red-500"><XCircle className="h-3 w-3 mr-1" /> Fallido</Badge>;
      case 'pending':
        return <Badge className="bg-gray-500"><Clock className="h-3 w-3 mr-1" /> Pendiente</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // Renderizar el nivel de log
  const renderLogLevel = (level: string) => {
    switch (level) {
      case 'info':
        return <Badge className="bg-blue-500">Info</Badge>;
      case 'warning':
        return <Badge className="bg-yellow-500">Advertencia</Badge>;
      case 'error':
        return <Badge className="bg-red-500">Error</Badge>;
      default:
        return <Badge>{level}</Badge>;
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Monitor de Scraping MCP</CardTitle>
        <CardDescription>
          Monitorea las operaciones de web scraping realizadas con el servidor MCP
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="jobs">Trabajos</TabsTrigger>
            <TabsTrigger value="logs">Logs</TabsTrigger>
          </TabsList>
          
          <TabsContent value="jobs" className="mt-4">
            <ScrollArea className="h-[400px]">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Fuente</TableHead>
                    <TableHead>Estado</TableHead>
                    <TableHead>Progreso</TableHead>
                    <TableHead>Inicio</TableHead>
                    <TableHead>Duración</TableHead>
                    <TableHead>Items</TableHead>
                    <TableHead>Páginas</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {jobs.map((job) => (
                    <TableRow key={job.id}>
                      <TableCell>{job.source}</TableCell>
                      <TableCell>{renderJobStatus(job.status)}</TableCell>
                      <TableCell>
                        <div className="w-[100px]">
                          <Progress value={job.progress} className="h-2" />
                          <span className="text-xs text-gray-500">{job.progress}%</span>
                        </div>
                      </TableCell>
                      <TableCell>{formatDate(job.startTime)}</TableCell>
                      <TableCell>{calculateDuration(job.startTime, job.endTime)}</TableCell>
                      <TableCell>{job.itemsScraped}</TableCell>
                      <TableCell>{job.pagesScraped}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </ScrollArea>
          </TabsContent>
          
          <TabsContent value="logs" className="mt-4">
            <ScrollArea className="h-[400px]">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Timestamp</TableHead>
                    <TableHead>Nivel</TableHead>
                    <TableHead>Fuente</TableHead>
                    <TableHead>Mensaje</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {logs.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell>{formatDate(log.timestamp)}</TableCell>
                      <TableCell>{renderLogLevel(log.level)}</TableCell>
                      <TableCell>{log.source}</TableCell>
                      <TableCell>
                        <div>
                          {log.message}
                          {log.details && (
                            <div className="text-xs text-gray-500 mt-1">
                              {log.details}
                            </div>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default MCPMonitorPanel;
