import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, CheckCircle2 } from 'lucide-react';
import { checkMCPServerAvailability } from '@/integrations/mcp';

interface MCPConfigPanelProps {
  onConfigChange?: (config: any) => void;
}

const MCPConfigPanel: React.FC<MCPConfigPanelProps> = ({ onConfigChange }) => {
  const [isServerAvailable, setIsServerAvailable] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('general');
  const [config, setConfig] = useState({
    useCustomServer: true,
    respectRobotsTxt: true,
    validateCertificates: true,
    rotateUserAgents: true,
    useProxies: false,
    advancedErrorHandling: true,
    dataNormalization: true
  });

  // Verificar disponibilidad del servidor MCP al cargar el componente
  useEffect(() => {
    const checkServerAvailability = async () => {
      setIsLoading(true);
      try {
        const isAvailable = await checkMCPServerAvailability();
        setIsServerAvailable(isAvailable);
      } catch (error) {
        console.error('Error al verificar disponibilidad del servidor MCP:', error);
        setIsServerAvailable(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkServerAvailability();
  }, []);

  // Manejar cambios en la configuración
  const handleConfigChange = (key: string, value: boolean) => {
    const newConfig = { ...config, [key]: value };
    setConfig(newConfig);
    if (onConfigChange) {
      onConfigChange(newConfig);
    }
  };

  // Verificar disponibilidad del servidor MCP manualmente
  const handleCheckAvailability = async () => {
    setIsLoading(true);
    try {
      const isAvailable = await checkMCPServerAvailability();
      setIsServerAvailable(isAvailable);
    } catch (error) {
      console.error('Error al verificar disponibilidad del servidor MCP:', error);
      setIsServerAvailable(false);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Configuración del Servidor MCP</CardTitle>
        <CardDescription>
          Configura el servidor Model Context Protocol (MCP) para web scraping con Playwright
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Estado del servidor */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-lg font-medium">Estado del Servidor</h3>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleCheckAvailability}
              disabled={isLoading}
            >
              {isLoading ? 'Verificando...' : 'Verificar'}
            </Button>
          </div>
          
          {isServerAvailable === null ? (
            <Alert className="bg-gray-100">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Desconocido</AlertTitle>
              <AlertDescription>
                No se ha verificado el estado del servidor MCP.
              </AlertDescription>
            </Alert>
          ) : isServerAvailable ? (
            <Alert className="bg-green-50">
              <CheckCircle2 className="h-4 w-4 text-green-600" />
              <AlertTitle className="text-green-600">Disponible</AlertTitle>
              <AlertDescription>
                El servidor MCP está disponible y listo para usar.
              </AlertDescription>
            </Alert>
          ) : (
            <Alert className="bg-red-50">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <AlertTitle className="text-red-600">No Disponible</AlertTitle>
              <AlertDescription>
                El servidor MCP no está disponible. Asegúrate de que esté instalado y configurado correctamente.
              </AlertDescription>
            </Alert>
          )}
        </div>

        {/* Configuración */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="security">Seguridad</TabsTrigger>
            <TabsTrigger value="advanced">Avanzado</TabsTrigger>
          </TabsList>
          
          <TabsContent value="general" className="space-y-4 mt-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="useCustomServer" className="font-medium">Usar Servidor Personalizado</Label>
                <p className="text-sm text-gray-500">Utiliza el servidor MCP personalizado con características adicionales</p>
              </div>
              <Switch 
                id="useCustomServer" 
                checked={config.useCustomServer}
                onCheckedChange={(checked) => handleConfigChange('useCustomServer', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="dataNormalization" className="font-medium">Normalización de Datos</Label>
                <p className="text-sm text-gray-500">Normaliza y limpia los datos extraídos automáticamente</p>
              </div>
              <Switch 
                id="dataNormalization" 
                checked={config.dataNormalization}
                onCheckedChange={(checked) => handleConfigChange('dataNormalization', checked)}
              />
            </div>
          </TabsContent>
          
          <TabsContent value="security" className="space-y-4 mt-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="respectRobotsTxt" className="font-medium">Respetar robots.txt</Label>
                <p className="text-sm text-gray-500">Cumple con las directivas de robots.txt de los sitios web</p>
              </div>
              <Switch 
                id="respectRobotsTxt" 
                checked={config.respectRobotsTxt}
                onCheckedChange={(checked) => handleConfigChange('respectRobotsTxt', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="validateCertificates" className="font-medium">Validar Certificados SSL/TLS</Label>
                <p className="text-sm text-gray-500">Verifica la validez de los certificados SSL/TLS de los sitios web</p>
              </div>
              <Switch 
                id="validateCertificates" 
                checked={config.validateCertificates}
                onCheckedChange={(checked) => handleConfigChange('validateCertificates', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="rotateUserAgents" className="font-medium">Rotar User Agents</Label>
                <p className="text-sm text-gray-500">Alterna entre diferentes user agents para evitar bloqueos</p>
              </div>
              <Switch 
                id="rotateUserAgents" 
                checked={config.rotateUserAgents}
                onCheckedChange={(checked) => handleConfigChange('rotateUserAgents', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="useProxies" className="font-medium">Usar Proxies</Label>
                <p className="text-sm text-gray-500">Utiliza proxies para evitar limitaciones de velocidad y bloqueos</p>
              </div>
              <Switch 
                id="useProxies" 
                checked={config.useProxies}
                onCheckedChange={(checked) => handleConfigChange('useProxies', checked)}
              />
            </div>
          </TabsContent>
          
          <TabsContent value="advanced" className="space-y-4 mt-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="advancedErrorHandling" className="font-medium">Manejo Avanzado de Errores</Label>
                <p className="text-sm text-gray-500">Utiliza estrategias avanzadas de manejo de errores y reintentos</p>
              </div>
              <Switch 
                id="advancedErrorHandling" 
                checked={config.advancedErrorHandling}
                onCheckedChange={(checked) => handleConfigChange('advancedErrorHandling', checked)}
              />
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline">Restaurar Valores Predeterminados</Button>
        <Button>Guardar Configuración</Button>
      </CardFooter>
    </Card>
  );
};

export default MCPConfigPanel;
