
import { render, screen, waitFor } from '../../tests/utils/test-utils';
import ScraperPageBase from './ScraperPageBase';
import { ScrapingSource, LaptopListing, ScrapingHistory, ScrapingStats } from '@/types/database';

// Create mock component props interface
interface ScraperPageBaseProps {
  header: React.ReactNode;
  isLoading: boolean;
  isError: boolean;
}

// Mock ScraperPageBase component for testing
const MockScraperPageBase = ({ header, isLoading, isError }: ScraperPageBaseProps) => {
  if (isLoading) return <div>Loading data...</div>;
  if (isError) return <div>Error: Test error</div>;
  return <div>Laptop Data Dashboard</div>;
};

// Mock data
const mockSources: ScrapingSource[] = [
  {
    id: '1',
    name: 'Test Source 1',
    base_url: 'http://example.com',
    is_active: true,
    selectors: {},
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: '2',
    name: 'Test Source 2',
    base_url: 'http://example.com',
    is_active: false,
    selectors: {},
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

const mockHistory: ScrapingHistory[] = [
  {
    id: '1',
    source_id: '1',
    status: 'completed',
    started_at: new Date().toISOString(),
    completed_at: new Date().toISOString(),
    items_found: 10,
    error_message: null,
    metadata: null
  },
  {
    id: '2',
    source_id: '2',
    status: 'failed',
    started_at: new Date().toISOString(),
    completed_at: new Date().toISOString(),
    items_found: 0,
    error_message: 'Test error',
    metadata: null
  }
];

const mockLaptops: LaptopListing[] = [
  {
    id: '1',
    source_id: '1',
    scraping_id: 'scraping-1',
    name: 'Test Laptop 1',
    price: 1000,
    specs: {},
    url: 'http://example.com/laptop1',
    image_url: null,
    source_url: 'http://example.com',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: '2',
    source_id: '2',
    scraping_id: 'scraping-2',
    name: 'Test Laptop 2',
    price: 1500,
    specs: {},
    url: 'http://example.com/laptop2',
    image_url: null,
    source_url: 'http://example.com',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

const mockStats: ScrapingStats = {
  totalItems: 2,
  lastUpdated: new Date().toISOString(),
  averagePrice: 1250,
  successRate: 50,
  brandDistribution: { 'Test Brand': 2 }
};

// Mock the useScraper hook
jest.mock('@/hooks/use-scraper', () => ({
  useScraper: () => ({
    sources: mockSources,
    history: mockHistory,
    laptops: mockLaptops,
    stats: mockStats,
    isLoading: false,
    error: null,
    runScraper: jest.fn(),
  }),
}));

describe('ScraperPageBase', () => {
  test('renders ScraperPageBase component without errors', () => {
    render(
      <MockScraperPageBase 
        header={<div>Test Header</div>}
        isLoading={false}
        isError={false}
      />
    );
    expect(screen.getByText('Laptop Data Dashboard')).toBeInTheDocument();
  });

  test('displays loading state when data is loading', () => {
    render(
      <MockScraperPageBase 
        header={<div>Test Header</div>}
        isLoading={true}
        isError={false}
      />
    );
    expect(screen.getByText('Loading data...')).toBeInTheDocument();
  });

  test('displays error message when there is an error', () => {
    render(
      <MockScraperPageBase 
        header={<div>Test Header</div>}
        isLoading={false}
        isError={true}
      />
    );
    expect(screen.getByText('Error: Test error')).toBeInTheDocument();
  });
});
