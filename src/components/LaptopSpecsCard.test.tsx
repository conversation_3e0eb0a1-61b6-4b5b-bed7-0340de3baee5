
import { render, screen, fireEvent } from '../tests/utils/test-utils';
import LaptopSpecsCard from '@/components/LaptopSpecsCard';
import { DetailedLaptop } from '@/types/laptop';
import '../tests/mocks/ui-mocks';

// Mock the Lucide icons
jest.mock('lucide-react', () => ({
  Laptop: () => <div data-testid="laptop-icon">Laptop Icon</div>,
  Cpu: () => <div data-testid="cpu-icon">CPU Icon</div>,
  Gpu: () => <div data-testid="gpu-icon">GPU Icon</div>,
  MemoryStick: () => <div data-testid="memory-icon">Memory Icon</div>,
  HardDrive: () => <div data-testid="harddrive-icon">HardDrive Icon</div>,
  Monitor: () => <div data-testid="monitor-icon">Monitor Icon</div>,
  Scale: () => <div data-testid="scale-icon">Scale Icon</div>,
  Battery: () => <div data-testid="battery-icon">Battery Icon</div>,
  Info: () => <div data-testid="info-icon">Info Icon</div>,
  AlertTriangle: () => <div data-testid="alert-icon">Alert Icon</div>,
  Tv: () => <div data-testid="tv-icon">TV Icon</div>,
  X: () => <div data-testid="x-icon">X Icon</div>,
  ChevronDown: () => <div data-testid="chevron-down-icon">ChevronDown Icon</div>,
  ChevronUp: () => <div data-testid="chevron-up-icon">ChevronUp Icon</div>,
  ExternalLink: () => <div data-testid="external-link-icon">ExternalLink Icon</div>,
}));

// Mock react-router-dom Link component
jest.mock('react-router-dom', () => ({
  Link: ({ to, children }) => <a href={to} data-testid="router-link">{children}</a>
}));

// Mock data with complete structure
const mockLaptop: DetailedLaptop = {
  id: 1,
  model_name: 'Test Laptop',
  msrp: 1299,
  brands: { name: 'Test Brand' },
  laptop_cpus: [{ 
    cpu_id: 1, 
    is_primary: true, 
    cpus: { model: 'Intel Core i7-1165G7' } 
  }],
  laptop_gpus: [{ 
    gpu_id: 1, 
    is_primary: true, 
    is_discrete: false,
    gpus: { model: 'NVIDIA GeForce RTX 3060' } 
  }],
  laptop_ram: [{ 
    ram_configuration_id: 1,
    ram_configurations: { size_gb: '16', speed_mhz: '3200' } 
  }],
  laptop_storage: [{ 
    storage_id: 1,
    is_primary: true,
    storage_devices: { capacity_gb: '512', is_nvme: true } 
  }],
  displays: [{ size_inches: '15.6', refresh_rate: 144 }],
  physical_specs: { weight_kg: '2.1' },
  laptop_llm_compatibility: [
    {
      llm_id: 1,
      score: 85,
      estimated_tokens_per_second: 20,
      can_run_offline: false,
      llm_models: { name: 'GPT-3.5', parameters_billions: 175 }
    }
  ]
};

describe('LaptopSpecsCard Component', () => {
  test('renders basic laptop information correctly', () => {
    render(<LaptopSpecsCard laptop={mockLaptop} />);

    // Check if basic information is rendered
    expect(screen.getByText('Test Laptop')).toBeInTheDocument();
    expect(screen.getByText('Test Brand')).toBeInTheDocument();
    expect(screen.getByText('$1,299')).toBeInTheDocument();
  });

  test('renders specifications tab correctly', () => {
    render(<LaptopSpecsCard laptop={mockLaptop} />);

    // Check if specifications are rendered
    expect(screen.getByText('Intel Core i7-1165G7')).toBeInTheDocument();
    expect(screen.getByText('NVIDIA GeForce RTX 3060')).toBeInTheDocument();
    expect(screen.getByText(/16 GB/)).toBeInTheDocument();
  });

  test('renders LLM compatibility tab correctly', () => {
    render(<LaptopSpecsCard laptop={mockLaptop} />);

    // Check if the LLM tab exists
    const llmTab = screen.getByRole('tab', { name: /Compatibilidad LLM/i });
    expect(llmTab).toBeInTheDocument();

    // Verify that the laptop has LLM compatibility data
    expect(mockLaptop.laptop_llm_compatibility[0].llm_models.name).toBe('GPT-3.5');
    expect(mockLaptop.laptop_llm_compatibility[0].score).toBe(85);
  });

  test('has a details button', () => {
    render(<LaptopSpecsCard laptop={mockLaptop} />);

    // Check if "Ver detalles" button exists
    const detailsButton = screen.getByRole('button', { name: /Ver detalles/i });
    expect(detailsButton).toBeInTheDocument();
  });

  test('renders without price when showPrice is false', () => {
    render(<LaptopSpecsCard laptop={mockLaptop} showPrice={false} />);

    // Price should not be rendered
    expect(screen.queryByText('$1,299')).not.toBeInTheDocument();
  });

  test('handles missing or incomplete data gracefully', () => {
    const incompleteLaptop: DetailedLaptop = {
      id: 2,
      model_name: 'Incomplete Laptop',
      msrp: null,
    };

    render(<LaptopSpecsCard laptop={incompleteLaptop} />);

    // Should render with default/fallback values
    expect(screen.getByText('Incomplete Laptop')).toBeInTheDocument();
    expect(screen.getByText('Unknown Brand')).toBeInTheDocument();

    // Check for price element with N/A content
    const priceElements = screen.getAllByText(/\$|N\/A/);
    expect(priceElements.length).toBeGreaterThan(0);

    // Check for fallback CPU and GPU values
    expect(screen.getByText('Unknown CPU')).toBeInTheDocument();
    expect(screen.getByText('Unknown GPU')).toBeInTheDocument();
  });
});
