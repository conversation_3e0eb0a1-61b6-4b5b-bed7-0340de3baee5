
/**
 * Contexto para gestionar configuraciones de scraper
 */

import React, { createContext, useContext, useState, ReactNode } from 'react';
import { SiteConfig } from '@/types/database';

interface ScraperConfigContextType {
  configs: SiteConfig[];
  isLoading: boolean;
  error: Error | null;
  hasEnabledConfigs: boolean;
  updateConfig: (id: string, config: Partial<SiteConfig>) => Promise<void>;
  deleteConfig: (id: string) => Promise<void>;
  createConfig: (config: Omit<SiteConfig, 'id' | 'created_at' | 'updated_at'>) => Promise<void>;
}

const ScraperConfigContext = createContext<ScraperConfigContextType | undefined>(undefined);

export const useScraperConfig = () => {
  const context = useContext(ScraperConfigContext);
  if (context === undefined) {
    throw new Error('useScraperConfig must be used within a ScraperConfigProvider');
  }
  return context;
};

interface ScraperConfigProviderProps {
  children: ReactNode;
}

export const ScraperConfigProvider: React.FC<ScraperConfigProviderProps> = ({ children }) => {
  const [configs, setConfigs] = useState<SiteConfig[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // Load initial configs from localStorage
  React.useEffect(() => {
    try {
      const stored = localStorage.getItem('scraperConfigs');
      if (stored) {
        setConfigs(JSON.parse(stored));
      }
    } catch (err) {
      console.error('Error loading scraper configs:', err);
      setError(err as Error);
    }
  }, []);

  const hasEnabledConfigs = configs.some(config => config.enabled);

  const updateConfig = async (id: string, updatedConfig: Partial<SiteConfig>) => {
    try {
      setIsLoading(true);
      setConfigs(prev => {
        const updated = prev.map(config => 
          config.id === id 
            ? { ...config, ...updatedConfig, updated_at: new Date().toISOString() }
            : config
        );
        localStorage.setItem('scraperConfigs', JSON.stringify(updated));
        return updated;
      });
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteConfig = async (id: string) => {
    try {
      setIsLoading(true);
      setConfigs(prev => {
        const filtered = prev.filter(config => config.id !== id);
        localStorage.setItem('scraperConfigs', JSON.stringify(filtered));
        return filtered;
      });
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const createConfig = async (configData: Omit<SiteConfig, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      setIsLoading(true);
      const newConfig: SiteConfig = {
        ...configData,
        id: `config-${Date.now()}`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      setConfigs(prev => {
        const updated = [...prev, newConfig];
        localStorage.setItem('scraperConfigs', JSON.stringify(updated));
        return updated;
      });
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const value: ScraperConfigContextType = {
    configs,
    isLoading,
    error,
    hasEnabledConfigs,
    updateConfig,
    deleteConfig,
    createConfig
  };

  return (
    <ScraperConfigContext.Provider value={value}>
      {children}
    </ScraperConfigContext.Provider>
  );
};
