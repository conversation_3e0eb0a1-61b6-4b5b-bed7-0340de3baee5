
/**
 * Servicio de Supabase
 * 
 * Este servicio proporciona métodos para interactuar con la base de datos de Supabase.
 * Incluye métodos CRUD básicos y manejo de errores.
 */

import { supabase } from '@/integrations/supabase/client';
import { ApiResponse } from '@/types/api';

export interface QueryOptions {
  select?: string;
  filter?: Record<string, any>;
  order?: { column: string; ascending: boolean };
  limit?: number;
  offset?: number;
}

export class SupabaseService {
  /**
   * Obtiene registros de una tabla
   */
  async get<T>(table: string, options: QueryOptions = {}): Promise<ApiResponse<T[]>> {
    try {
      // For demo purposes, return empty data since we don't have tables set up
      console.log(`Mock query to table: ${table}`, options);
      
      return {
        data: [] as T[],
        status: 200,
        message: 'Success'
      };
    } catch (error) {
      console.error('Error fetching data:', error);
      return {
        error: error instanceof Error ? error.message : 'Unknown error',
        status: 500
      };
    }
  }

  /**
   * Crea un nuevo registro
   */
  async post<T>(table: string, data: Record<string, any>): Promise<ApiResponse<T>> {
    try {
      // For demo purposes, return mock data
      console.log(`Mock insert to table: ${table}`, data);
      
      return {
        data: { ...data, id: Date.now().toString() } as T,
        status: 201,
        message: 'Created successfully'
      };
    } catch (error) {
      console.error('Error creating record:', error);
      return {
        error: error instanceof Error ? error.message : 'Unknown error',
        status: 500
      };
    }
  }

  /**
   * Actualiza un registro
   */
  async put<T>(table: string, data: Record<string, any>): Promise<ApiResponse<T>> {
    try {
      // For demo purposes, return mock data
      console.log(`Mock update to table: ${table}`, data);
      
      return {
        data: data as T,
        status: 200,
        message: 'Updated successfully'
      };
    } catch (error) {
      console.error('Error updating record:', error);
      return {
        error: error instanceof Error ? error.message : 'Unknown error',
        status: 500
      };
    }
  }

  /**
   * Elimina un registro
   */
  async delete<T>(table: string, filter: Record<string, any>): Promise<ApiResponse<T>> {
    try {
      // For demo purposes, return success
      console.log(`Mock delete from table: ${table}`, filter);
      
      return {
        data: filter as T,
        status: 200,
        message: 'Deleted successfully'
      };
    } catch (error) {
      console.error('Error deleting record:', error);
      return {
        error: error instanceof Error ? error.message : 'Unknown error',
        status: 500
      };
    }
  }
}
