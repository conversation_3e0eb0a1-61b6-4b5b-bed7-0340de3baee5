
/**
 * Servicio simplificado de base de datos
 * 
 * Este servicio maneja las operaciones de datos utilizando localStorage
 * en lugar de Supabase para evitar problemas de configuración.
 */

import { 
  ScrapingSource, 
  LaptopListing, 
  ScrapingHistory,
  Laptop 
} from '@/types/database';

// Claves para localStorage
const STORAGE_KEYS = {
  SOURCES: 'scraper_sources',
  LAPTOPS: 'laptop_listings',
  HISTORY: 'scraping_history',
  SCRAPER_CONFIGS: 'scraperConfigs'
};

class DatabaseService {
  // Operaciones para fuentes de scraping
  sources = {
    getAll: async (): Promise<ScrapingSource[]> => {
      try {
        const saved = localStorage.getItem(STORAGE_KEYS.SOURCES);
        return saved ? JSON.parse(saved) : [];
      } catch (error) {
        console.error('Error getting sources:', error);
        return [];
      }
    },

    getActive: async (): Promise<ScrapingSource[]> => {
      const sources = await this.sources.getAll();
      return sources.filter(source => source.is_active);
    },

    create: async (source: Omit<ScrapingSource, 'id' | 'created_at' | 'updated_at'>): Promise<ScrapingSource> => {
      const sources = await this.sources.getAll();
      const newSource: ScrapingSource = {
        ...source,
        id: Date.now().toString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      sources.push(newSource);
      localStorage.setItem(STORAGE_KEYS.SOURCES, JSON.stringify(sources));
      return newSource;
    },

    update: async (id: string, updates: Partial<ScrapingSource>): Promise<ScrapingSource> => {
      const sources = await this.sources.getAll();
      const index = sources.findIndex(s => s.id === id);
      
      if (index === -1) {
        throw new Error('Source not found');
      }
      
      sources[index] = {
        ...sources[index],
        ...updates,
        updated_at: new Date().toISOString()
      };
      
      localStorage.setItem(STORAGE_KEYS.SOURCES, JSON.stringify(sources));
      return sources[index];
    },

    delete: async (id: string): Promise<void> => {
      const sources = await this.sources.getAll();
      const filtered = sources.filter(s => s.id !== id);
      localStorage.setItem(STORAGE_KEYS.SOURCES, JSON.stringify(filtered));
    }
  };

  // Operaciones para laptops
  laptops = {
    getAll: async (limit = 100, page = 0): Promise<Laptop[]> => {
      try {
        const mockData = localStorage.getItem('mockLaptopData');
        if (mockData) {
          const laptops = JSON.parse(mockData);
          // Convert to Laptop format
          return laptops.map((laptop: any) => ({
            id: parseInt(laptop.id.replace('laptop-', '')) || Math.floor(Math.random() * 1000),
            model_name: laptop.name || 'Unknown Model',
            brand_id: 1,
            description: 'Imported from localStorage',
            image_url: laptop.image_url || null,
            is_available: true,
            msrp: laptop.price || 0,
            created_at: laptop.created_at || new Date().toISOString(),
            updated_at: laptop.updated_at || new Date().toISOString()
          }));
        }
        return [];
      } catch (error) {
        console.error('Error getting laptops:', error);
        return [];
      }
    },

    getById: async (id: number): Promise<Laptop | null> => {
      const laptops = await this.laptops.getAll();
      return laptops.find(l => l.id === id) || null;
    },

    search: async (query: string): Promise<Laptop[]> => {
      const laptops = await this.laptops.getAll();
      return laptops.filter(laptop => 
        laptop.model_name.toLowerCase().includes(query.toLowerCase())
      );
    },

    create: async (laptop: Omit<Laptop, 'id' | 'created_at' | 'updated_at'>): Promise<Laptop> => {
      const laptops = await this.laptops.getAll();
      const newLaptop: Laptop = {
        ...laptop,
        id: Math.floor(Math.random() * 10000),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      laptops.push(newLaptop);
      // Save back to mockLaptopData for consistency
      const mockData = laptops.map(l => ({
        id: `laptop-${l.id}`,
        name: l.model_name,
        price: l.msrp || 0,
        specs: { brand: 'Unknown' },
        created_at: l.created_at,
        updated_at: l.updated_at
      }));
      localStorage.setItem('mockLaptopData', JSON.stringify(mockData));
      
      return newLaptop;
    },

    update: async (id: number, updates: Partial<Laptop>): Promise<Laptop> => {
      const laptops = await this.laptops.getAll();
      const index = laptops.findIndex(l => l.id === id);
      
      if (index === -1) {
        throw new Error('Laptop not found');
      }
      
      laptops[index] = {
        ...laptops[index],
        ...updates,
        updated_at: new Date().toISOString()
      };
      
      return laptops[index];
    },

    delete: async (id: number): Promise<void> => {
      // Implementation for deleting laptop
      console.log(`Delete laptop ${id}`);
    }
  };

  // Operaciones para historial de scraping
  history = {
    getAll: async (): Promise<ScrapingHistory[]> => {
      try {
        const saved = localStorage.getItem(STORAGE_KEYS.HISTORY);
        return saved ? JSON.parse(saved) : [
          {
            id: '1',
            source_id: '1',
            status: 'completed' as const,
            started_at: new Date().toISOString(),
            completed_at: new Date().toISOString(),
            items_found: 10,
            error_message: null,
            metadata: null
          }
        ];
      } catch (error) {
        console.error('Error getting history:', error);
        return [];
      }
    },

    create: async (history: Omit<ScrapingHistory, 'id'>): Promise<ScrapingHistory> => {
      const histories = await this.history.getAll();
      const newHistory: ScrapingHistory = {
        ...history,
        id: Date.now().toString()
      };
      
      histories.push(newHistory);
      localStorage.setItem(STORAGE_KEYS.HISTORY, JSON.stringify(histories));
      return newHistory;
    }
  };

  // Stub implementations for compatibility
  manufacturers = {
    getAll: async () => [],
    create: async (data: any) => data,
    update: async (id: number, data: any) => data,
    delete: async (id: number) => {}
  };

  brands = {
    getAll: async () => [],
    create: async (data: any) => data,
    update: async (id: number, data: any) => data,
    delete: async (id: number) => {}
  };

  llmModels = {
    getAll: async () => [],
    getById: async (id: number) => null,
    create: async (data: any) => data,
    update: async (id: number, data: any) => data,
    delete: async (id: number) => {}
  };

  laptopLlmCompatibility = {
    getAll: async () => [],
    getByLaptopId: async (laptopId: number) => [],
    getByLlmId: async (llmId: number) => [],
    create: async (data: any) => data,
    update: async (laptopId: number, llmId: number, data: any) => data,
    delete: async (laptopId: number, llmId: number) => {}
  };
}

export const databaseService = new DatabaseService();
