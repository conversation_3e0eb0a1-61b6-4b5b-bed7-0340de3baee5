
# LLM Laptop Lens - Servicio de Scraping

Este servicio es responsable de la recopilación de datos de laptops desde diversas fuentes, como Revolico.com, para ser utilizados en la aplicación LLM Laptop Lens.

## Características

- Scraping configurable de Revolico.com mediante API GraphQL
- Filtrado por rango de precios configurable
- Filtrado por fecha de publicación configurable
- Extracción y normalización de especificaciones técnicas
- Manejo robusto de errores y reintentos
- Sistema de logs detallados

## Estructura del Proyecto

```
scraper-service/
├── src/
│   ├── index.ts                 # Punto de entrada
│   ├── scrapers/                # Implementaciones de scrapers
│   │   ├── scraper-base.ts      # Clase base para todos los scrapers
│   │   ├── revolico-scraper.ts  # Implementación específica para Revolico.com
│   │   └── __tests__/           # Tests unitarios para los scrapers
│   ├── types/                   # Definiciones de tipos
│   │   └── scraping.types.ts    # Tipos para el sistema de scraping
│   └── utils/                   # Utilidades comunes
│       └── logger.ts            # Sistema de logs
├── dist/                        # Código compilado (generado)
├── package.json                 # Dependencias y scripts
└── tsconfig.json                # Configuración de TypeScript
```

## Configuración de Scraping

La configuración de las fuentes de scraping se almacena en la base de datos en la tabla `scraping_sources`. El campo `selectors` contiene la configuración específica para cada fuente, como por ejemplo:

### Revolico.com GraphQL API

```json
{
  "graphql": {
    "query": "...",
    "variables": {
      "categories": [860],
      "phrase": "laptop",
      "priceGte": 800,
      "priceLte": 2000,
      "updatedOnToOrder": true,
      "daysAgo": 7,
      "page": 1
    },
    "pageParam": "page",
    "maxPages": 5,
    "filters": {
      "priceRange": {
        "enabled": true,
        "min": 800,
        "max": 2000,
        "minParam": "priceGte",
        "maxParam": "priceLte"
      },
      "dateFilter": {
        "enabled": true,
        "daysAgo": 7,
        "param": "daysAgo"
      }
    },
    "dataMapping": {
      "items": "data.search.ads",
      "name": "title",
      "description": "description",
      "price": "price",
      "currency": "currency",
      "imageUrl": "images[0].medium",
      "publishDate": "updated_on",
      "specs": {
        "mapping": "attributes",
        "transforms": {
          "ram": "attributes.ram",
          "cpu": "attributes.processor",
          "gpu": "attributes.graphic_card",
          "storage": "attributes.storage"
        }
      },
      "contactInfo": {
        "phone": "phones[0].number",
        "email": "emails[0].email"
      }
    }
  }
}
```

## Uso

Para ejecutar el servicio:

```bash
# Desarrollo
npm run dev

# Producción
npm run build
npm start
```

Para ejecutar los tests:

```bash
npm test
```

## Extensión

Para añadir soporte para una nueva fuente de datos:

1. Crear una nueva clase que extienda `ScraperBase`
2. Implementar el método `scrape()`
3. Añadir la nueva fuente a la tabla `scraping_sources` en la base de datos
4. Actualizar el factory method en `ScraperBase` para reconocer la nueva fuente

## Documentación Adicional

Para más detalles sobre la API de Revolico.com, consultar el documento técnico en `docs/Reporte Técnico Interacción con la API de Revolico.com.md`.
