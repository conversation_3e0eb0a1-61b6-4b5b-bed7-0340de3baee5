
/**
 * Logger.ts
 * Utilidad para registrar logs con diferentes niveles
 */

export class Logger {
  private context: string;
  
  constructor(context: string = 'App') {
    this.context = context;
  }
  
  /**
   * Registra un mensaje informativo
   * @param message Mensaje a registrar
   * @param data Datos adicionales (opcional)
   */
  info(message: string, data?: any): void {
    this.log('INFO', message, data);
  }
  
  /**
   * Registra un mensaje de advertencia
   * @param message Mensaje a registrar
   * @param data Datos adicionales (opcional)
   */
  warn(message: string, data?: any): void {
    this.log('WARN', message, data);
  }
  
  /**
   * Registra un mensaje de error
   * @param message Mensaje a registrar
   * @param error Objeto de error o mensaje
   */
  error(message: string, error?: any): void {
    let errorDetails = '';
    
    if (error) {
      if (error instanceof Error) {
        errorDetails = `${error.message}\n${error.stack}`;
      } else if (typeof error === 'object') {
        try {
          errorDetails = JSON.stringify(error, null, 2);
        } catch {
          errorDetails = String(error);
        }
      } else {
        errorDetails = String(error);
      }
    }
    
    this.log('ERROR', message, errorDetails);
  }
  
  /**
   * Registra un mensaje de depuración
   * @param message Mensaje a registrar
   * @param data Datos adicionales (opcional)
   */
  debug(message: string, data?: any): void {
    if (process.env.DEBUG === 'true') {
      this.log('DEBUG', message, data);
    }
  }
  
  /**
   * Método interno para formatear y registrar mensajes
   * @param level Nivel del log
   * @param message Mensaje a registrar
   * @param data Datos adicionales (opcional)
   */
  private log(level: string, message: string, data?: any): void {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${level}] [${this.context}] ${message}`;
    
    // Determinar el método de console según el nivel
    let consoleMethod: 'log' | 'info' | 'warn' | 'error' = 'log';
    
    switch (level) {
      case 'INFO':
        consoleMethod = 'info';
        break;
      case 'WARN':
        consoleMethod = 'warn';
        break;
      case 'ERROR':
        consoleMethod = 'error';
        break;
      default:
        consoleMethod = 'log';
        break;
    }
    
    // Registrar el mensaje
    if (data !== undefined) {
      console[consoleMethod](logMessage, data);
    } else {
      console[consoleMethod](logMessage);
    }
    
    // TODO: En una implementación más avanzada, podríamos añadir aquí
    // el almacenamiento del log en un archivo o en un servicio externo
  }
}
