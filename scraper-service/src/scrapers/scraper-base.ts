
/**
 * ScraperBase.ts
 * Clase base para implementaciones de scrapers
 */

import { ScrapingResult, ScrapingSource } from '../types/scraping.types';

export abstract class ScraperBase {
  /**
   * Método abstracto que debe implementar cada scraper específico
   */
  abstract scrape(): Promise<ScrapingResult>;
  
  /**
   * Factory method para crear el scraper adecuado según la fuente
   * @param source Configuración de la fuente de datos
   * @returns Una instancia del scraper apropiado
   */
  static create(source: ScrapingSource): ScraperBase {
    switch (source.name.toLowerCase()) {
      case 'revolico.com':
        // Importación dinámica para evitar problemas de dependencia circular
        const { RevolicoScraper } = require('./revolico-scraper');
        return new RevolicoScraper(source);
      default:
        throw new Error(`No hay un scraper implementado para la fuente: ${source.name}`);
    }
  }
}
