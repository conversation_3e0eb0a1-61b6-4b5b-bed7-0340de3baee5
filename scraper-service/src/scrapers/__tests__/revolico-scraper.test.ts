
/**
 * Tests para RevolicoScraper
 */

import { RevolicoScraper } from '../revolico-scraper';
import { ScrapingSource } from '../../types/scraping.types';
import axios from 'axios';

// Mock de axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('RevolicoScraper', () => {
  // Configuración de prueba para el scraper
  const mockSource: ScrapingSource = {
    id: 'mock-id',
    name: 'Revolico.com',
    base_url: 'https://api.revolico.com/graphql',
    enabled: true,
    selectors: {
      graphql: {
        query: 'query SearchAds($categories: [Int!], $provinces: [String!], $phrase: String, $priceGte: Float, $priceLte: Float, $updatedOnToOrder: Boolean, $daysAgo: Int, $page: Int!) { search(categories: $categories, provinces: $provinces, phrase: $phrase, price_gte: $priceGte, price_lte: $priceLte, updated_on_to_order: $updatedOnToOrder, days_ago: $daysAgo, page: $page) { ads { id title description price updated_on province { name } images { medium } currency phone_visible emails { email } phones { number } attributes } pagination { current_page total_pages total_items } } }',
        variables: {
          categories: [860],
          phrase: 'laptop',
          priceGte: 800,
          priceLte: 2000,
          updatedOnToOrder: true,
          daysAgo: 7,
          page: 1
        },
        pageParam: 'page',
        maxPages: 2,
        dataMapping: {
          items: 'data.search.ads',
          name: 'title',
          description: 'description',
          price: 'price',
          currency: 'currency',
          imageUrl: 'images[0].medium',
          publishDate: 'updated_on',
          specs: {
            mapping: 'attributes',
            transforms: {
              ram: 'attributes.ram',
              cpu: 'attributes.processor',
              gpu: 'attributes.graphic_card',
              storage: 'attributes.storage'
            }
          },
          contactInfo: {
            phone: 'phones[0].number',
            email: 'emails[0].email'
          }
        }
      }
    }
  };

  // Mock de respuesta de la API
  const mockApiResponse = {
    data: {
      data: {
        search: {
          ads: [
            {
              id: '12345',
              title: 'Laptop Dell Inspiron 15 i7 16GB RAM',
              description: 'Excelente laptop Dell con procesador Intel i7, 16GB RAM DDR4, 512GB SSD',
              price: 1200,
              updated_on: '2023-05-10T12:00:00Z',
              province: { name: 'La Habana' },
              images: [{ medium: 'https://example.com/image1.jpg' }],
              currency: 'USD',
              phone_visible: true,
              emails: [{ email: '<EMAIL>' }],
              phones: [{ number: '+53 12345678' }],
              attributes: {
                ram: '16GB DDR4',
                processor: 'Intel i7-10750H 6 cores 2.6GHz',
                graphic_card: 'NVIDIA GTX 1650',
                storage: '512GB SSD'
              }
            },
            {
              id: '67890',
              title: 'MacBook Pro M1 8GB',
              description: 'MacBook Pro con chip M1, 8GB RAM, 256GB SSD',
              price: 1800,
              updated_on: '2023-05-09T10:00:00Z',
              province: { name: 'La Habana' },
              images: [{ medium: 'https://example.com/image2.jpg' }],
              currency: 'USD',
              phone_visible: true,
              emails: [{ email: '<EMAIL>' }],
              phones: [{ number: '+53 98765432' }],
              attributes: {
                ram: '8GB',
                processor: 'Apple M1',
                storage: '256GB SSD'
              }
            }
          ],
          pagination: {
            current_page: 1,
            total_pages: 1,
            total_items: 2
          }
        }
      }
    }
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Configurar el mock de axios para devolver la respuesta simulada
    mockedAxios.post.mockResolvedValue(mockApiResponse);
  });

  test('debe inicializarse correctamente con una fuente', () => {
    const scraper = new RevolicoScraper(mockSource);
    expect(scraper).toBeDefined();
  });

  test('debe ejecutar scraping y devolver resultados', async () => {
    const scraper = new RevolicoScraper(mockSource);
    const result = await scraper.scrape();

    // Verificar que se llamó a axios con los parámetros correctos
    expect(mockedAxios.post).toHaveBeenCalledWith(
      mockSource.base_url,
      {
        query: expect.any(String),
        variables: expect.objectContaining({
          categories: [860],
          phrase: 'laptop',
          priceGte: 800,
          priceLte: 2000,
          updatedOnToOrder: true,
          daysAgo: 7,
          page: 1
        })
      },
      expect.any(Object)
    );

    // Verificar los resultados
    expect(result.laptops).toHaveLength(2);
    expect(result.laptops[0].name).toBe('Laptop Dell Inspiron 15 i7 16GB RAM');
    expect(result.laptops[0].brand).toBe('Dell');
    expect(result.laptops[0].price).toBe(1200);
    
    // Verificar que el filtro de fecha está aplicado correctamente
    expect(mockedAxios.post.mock.calls[0][1].variables.daysAgo).toBe(7);
    
    // Verificar que el rango de precios se aplica correctamente
    expect(mockedAxios.post.mock.calls[0][1].variables.priceGte).toBe(800);
    expect(mockedAxios.post.mock.calls[0][1].variables.priceLte).toBe(2000);
  });

  test('debe manejar correctamente errores de la API', async () => {
    // Simular un error en la API
    mockedAxios.post.mockRejectedValueOnce(new Error('Error de conexión'));
    
    const scraper = new RevolicoScraper(mockSource);
    const result = await scraper.scrape();
    
    // Verificar que no hay laptops pero hay errores registrados
    expect(result.laptops).toHaveLength(0);
    expect(result.session.errors.length).toBeGreaterThan(0);
    expect(result.session.errors[0].message).toContain('Error general en el scraper');
  });

  test('debe extraer correctamente las especificaciones', async () => {
    const scraper = new RevolicoScraper(mockSource);
    const result = await scraper.scrape();
    
    // Verificar la extracción de especificaciones
    const laptop = result.laptops[0];
    
    expect(laptop.specifications.cpu.name).toBe('Intel i7-10750H 6 cores 2.6GHz');
    expect(laptop.specifications.cpu.cores).toBe(6);
    expect(laptop.specifications.ram.size).toBe(16);
    expect(laptop.specifications.ram.type).toBe('DDR4');
    expect(laptop.specifications.storage.size).toBe(512);
    expect(laptop.specifications.storage.type).toBe('SSD');
    expect(laptop.specifications.gpu.name).toBe('NVIDIA GTX 1650');
    expect(laptop.specifications.gpu.type).toBe('dedicated');
  });
  
  test('debe ser capaz de extraer información desde la descripción si los atributos no están disponibles', async () => {
    // Modificar la respuesta para quitar los atributos estructurados
    const modifiedResponse = { ...mockApiResponse };
    modifiedResponse.data.data.search.ads[0].attributes = {};
    modifiedResponse.data.data.search.ads[0].description = 'Laptop con Intel Core i5 1035G1, 8GB RAM DDR4, 256GB SSD, NVIDIA MX330';
    
    mockedAxios.post.mockResolvedValueOnce(modifiedResponse);
    
    const scraper = new RevolicoScraper(mockSource);
    const result = await scraper.scrape();
    
    // Verificar la extracción desde la descripción
    const laptop = result.laptops[0];
    expect(laptop.specifications.cpu.name).toContain('Intel');
    expect(laptop.specifications.ram.size).toBe(8);
    expect(laptop.specifications.storage.type).toBe('SSD');
    expect(laptop.specifications.gpu.name).toContain('NVIDIA');
    expect(laptop.specifications.gpu.type).toBe('dedicated');
  });
});
