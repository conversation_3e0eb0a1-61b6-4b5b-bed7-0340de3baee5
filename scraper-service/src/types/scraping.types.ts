
/**
 * Types para el sistema de scraping
 */

export interface ScrapingSource {
  id: string;
  name: string;
  base_url: string;
  enabled: boolean;
  last_scraped_at?: string;
  created_at?: string;
  updated_at?: string;
  selectors?: any;
}

export interface LaptopData {
  id: string;
  name: string;
  brand: string;
  price: number;
  specifications: {
    cpu: {
      name: string;
      cores?: number;
      threads?: number;
      baseClockSpeed?: number;
      boostClockSpeed?: number;
      tdp?: number;
    };
    gpu: {
      name: string;
      memory?: number;
      cudaCores?: number;
      type: "integrated" | "dedicated";
    };
    ram: {
      size: number;
      type: string;
      speed?: number;
    };
    storage: {
      type: string;
      size: number;
      readSpeed?: number;
      writeSpeed?: number;
    };
    display?: {
      size?: number;
      resolution?: string;
      refreshRate?: number;
      type?: string;
    };
    battery?: {
      capacity?: number;
      lifeHours?: number;
    };
    ports?: string[];
    dimensions?: {
      width?: number;
      depth?: number;
      height?: number;
      weightKg?: number;
    };
  };
  inStock: boolean;
  imageUrl: string;
  description: string;
  promoOffer?: string;
  source?: {
    name: string;
    url: string;
    publishDate?: string;
    contactInfo?: {
      phone?: string;
      email?: string;
    }
  };
  currency?: string;
}

export interface ScrapingSession {
  timestamp: string;
  totalProducts: number;
  errors: ScrapingError[];
}

export interface ScrapingError {
  url: string;
  message: string;
  stack?: string;
}

export interface ScrapingResult {
  laptops: LaptopData[];
  session: ScrapingSession;
}
