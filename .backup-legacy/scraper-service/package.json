{"name": "laptop-scraper-service", "version": "1.0.0", "description": "Servicio de scraping para tiendas de laptops", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev src/index.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@supabase/supabase-js": "^2.38.4", "axios": "^1.9.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "lru-cache": "^10.2.0", "puppeteer": "^22.8.2", "winston": "^3.11.0"}, "devDependencies": {"@types/cors": "^2.8.15", "@types/express": "^4.17.21", "@types/jest": "^29.5.14", "@types/node": "^20.9.0", "@types/winston": "^2.4.4", "glob": "^10.3.10", "jest": "^29.7.0", "rimraf": "^5.0.5", "ts-jest": "^29.3.2", "ts-node-dev": "^2.0.0", "typescript": "^5.2.2"}}