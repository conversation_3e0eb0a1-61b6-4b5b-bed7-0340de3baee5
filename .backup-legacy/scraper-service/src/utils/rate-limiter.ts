/**
 * Implementación de un limitador de velocidad para controlar
 * la frecuencia de las solicitudes a los sitios web.
 */
export class RateLimiter {
  private queue: (() => Promise<void>)[] = [];
  private processing = false;
  private requestsPerPeriod: number;
  private periodMs: number;
  private requestTimes: number[] = [];

  constructor(requestsPerPeriod: number, periodMs: number) {
    this.requestsPerPeriod = requestsPerPeriod;
    this.periodMs = periodMs;
  }

  /**
   * Programa una función para ser ejecutada respetando los límites de velocidad.
   */
  async schedule<T>(fn: () => Promise<T>): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      this.queue.push(async () => {
        try {
          await this.waitForSlot();
          const result = await fn();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });

      if (!this.processing) {
        this.processQueue();
      }
    });
  }

  private async processQueue(): Promise<void> {
    if (this.queue.length === 0) {
      this.processing = false;
      return;
    }

    this.processing = true;
    const task = this.queue.shift();
    
    if (task) {
      await task();
    }

    // Continuar procesando la cola
    this.processQueue();
  }

  private async waitForSlot(): Promise<void> {
    const now = Date.now();
    
    // Eliminar tiempos de solicitud antiguos
    this.requestTimes = this.requestTimes.filter(
      time => now - time < this.periodMs
    );

    if (this.requestTimes.length >= this.requestsPerPeriod) {
      // Calcular cuánto tiempo esperar
      const oldestRequest = this.requestTimes[0];
      const waitTime = this.periodMs - (now - oldestRequest);
      
      if (waitTime > 0) {
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
      
      // Actualizar el tiempo actual después de esperar
      this.requestTimes.shift();
    }

    // Registrar esta solicitud
    this.requestTimes.push(Date.now());
  }
}