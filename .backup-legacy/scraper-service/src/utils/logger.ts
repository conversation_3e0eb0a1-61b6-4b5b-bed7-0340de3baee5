import winston from 'winston';
import path from 'path';
import fs from 'fs';

// Asegurar que el directorio de logs exista
const logDir = 'logs';
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir);
}

// Configuración del logger
export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp({
      format: 'YYYY-MM-DD HH:mm:ss'
    }),
    winston.format.errors({ stack: true }),
    winston.format.splat(),
    winston.format.json()
  ),
  defaultMeta: { service: 'scraper-service' },
  transports: [
    // Escribir logs de error en error.log
    new winston.transports.File({
      filename: path.join(logDir, 'scraper-error.log'),
      level: 'error'
    }),
    // Escribir todos los logs en combined.log
    new winston.transports.File({
      filename: path.join(logDir, 'scraper.log')
    }),
  ],
});

// Si no estamos en producción, también mostrar logs en la consola con formato legible
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.timestamp({
        format: 'HH:mm:ss'
      }),
      winston.format.printf(({ timestamp, level, message, ...rest }) => {
        const restString = Object.keys(rest).length ?
          ` ${JSON.stringify(rest, null, 2)}` : '';
        return `${timestamp} ${level}: ${message}${restString}`;
      })
    )
  }));
}