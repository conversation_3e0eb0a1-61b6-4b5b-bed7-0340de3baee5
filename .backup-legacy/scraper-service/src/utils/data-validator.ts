/**
 * Utilidades para validar y normalizar los datos extraídos.
 */
export class DataValidator {
  /**
   * Extrae el precio de un texto y lo convierte a número.
   */
  static parsePrice(text: string): { price: number; currency: string } {
    // Eliminar espacios y caracteres no deseados
    const cleanText = text.trim().replace(/\s+/g, ' ');
    
    // Buscar patrones de moneda
    const usdMatch = cleanText.match(/\$\s*([0-9,]+(\.[0-9]+)?)/);
    const eurMatch = cleanText.match(/€\s*([0-9,]+(\.[0-9]+)?)/);
    const cupMatch = cleanText.match(/([0-9,]+(\.[0-9]+)?)\s*CUP/i);
    
    if (usdMatch && usdMatch[1]) {
      return {
        price: parseFloat(usdMatch[1].replace(/,/g, '')),
        currency: 'USD'
      };
    } else if (eurMatch && eurMatch[1]) {
      return {
        price: parseFloat(eurMatch[1].replace(/,/g, '')),
        currency: 'EUR'
      };
    } else if (cupMatch && cupMatch[1]) {
      return {
        price: parseFloat(cupMatch[1].replace(/,/g, '')),
        currency: 'CUP'
      };
    }
    
    // Si no se encuentra un patrón específico, intentar extraer cualquier número
    const numberMatch = cleanText.match(/([0-9,]+(\.[0-9]+)?)/);
    if (numberMatch && numberMatch[1]) {
      return {
        price: parseFloat(numberMatch[1].replace(/,/g, '')),
        currency: 'USD' // Moneda por defecto
      };
    }
    
    return { price: 0, currency: 'USD' };
  }

  /**
   * Parsea información de CPU de un texto.
   */
  static parseCpu(text: string): { name: string; cores?: number; threads?: number } {
    const cleanText = text.toLowerCase();
    
    // Detectar marca y modelo
    let name = 'Unknown CPU';
    
    if (cleanText.includes('intel')) {
      if (cleanText.includes('i9')) name = 'Intel Core i9';
      else if (cleanText.includes('i7')) name = 'Intel Core i7';
      else if (cleanText.includes('i5')) name = 'Intel Core i5';
      else if (cleanText.includes('i3')) name = 'Intel Core i3';
      else name = 'Intel CPU';
    } else if (cleanText.includes('amd')) {
      if (cleanText.includes('ryzen 9')) name = 'AMD Ryzen 9';
      else if (cleanText.includes('ryzen 7')) name = 'AMD Ryzen 7';
      else if (cleanText.includes('ryzen 5')) name = 'AMD Ryzen 5';
      else if (cleanText.includes('ryzen 3')) name = 'AMD Ryzen 3';
      else name = 'AMD CPU';
    } else if (cleanText.includes('apple')) {
      if (cleanText.includes('m3')) name = 'Apple M3';
      else if (cleanText.includes('m2')) name = 'Apple M2';
      else if (cleanText.includes('m1')) name = 'Apple M1';
      else name = 'Apple Silicon';
    }
    
    // Intentar extraer número de núcleos
    const coresMatch = cleanText.match(/(\d+)\s*(?:core|núcleo)/i);
    const cores = coresMatch ? parseInt(coresMatch[1], 10) : undefined;
    
    // Intentar extraer número de hilos
    const threadsMatch = cleanText.match(/(\d+)\s*(?:thread|hilo)/i);
    const threads = threadsMatch ? parseInt(threadsMatch[1], 10) : undefined;
    
    return { name, cores, threads };
  }

  /**
   * Parsea información de RAM de un texto.
   */
  static parseRam(text: string): { size: number; type: string } {
    const cleanText = text.toLowerCase();
    
    // Extraer tamaño de RAM
    const sizeMatch = cleanText.match(/(\d+)\s*(?:gb|g\b)/i);
    const size = sizeMatch ? parseInt(sizeMatch[1], 10) : 0;
    
    // Determinar tipo de RAM
    let type = 'Unknown';
    if (cleanText.includes('ddr5')) type = 'DDR5';
    else if (cleanText.includes('ddr4')) type = 'DDR4';
    else if (cleanText.includes('ddr3')) type = 'DDR3';
    else if (cleanText.includes('lpddr5')) type = 'LPDDR5';
    else if (cleanText.includes('lpddr4')) type = 'LPDDR4';
    
    return { size, type };
  }

  /**
   * Parsea información de almacenamiento de un texto.
   */
  static parseStorage(text: string): { size: number; type: string } {
    const cleanText = text.toLowerCase();
    
    // Extraer tamaño de almacenamiento
    const tbMatch = cleanText.match(/(\d+(?:\.\d+)?)\s*(?:tb|t\b)/i);
    const gbMatch = cleanText.match(/(\d+(?:\.\d+)?)\s*(?:gb|g\b)/i);
    
    let size = 0;
    if (tbMatch && tbMatch[1]) {
      size = parseFloat(tbMatch[1]) * 1024; // Convertir TB a GB
    } else if (gbMatch && gbMatch[1]) {
      size = parseFloat(gbMatch[1]);
    }
    
    // Determinar tipo de almacenamiento
    let type = 'HDD';
    if (cleanText.includes('ssd')) type = 'SSD';
    else if (cleanText.includes('nvme')) type = 'NVMe SSD';
    else if (cleanText.includes('m.2')) type = 'M.2 SSD';
    else if (cleanText.includes('emmc')) type = 'eMMC';
    
    return { size, type };
  }

  /**
   * Parsea información de GPU de un texto.
   */
  static parseGpu(text: string): { name: string; type: string; vram?: number } {
    const cleanText = text.toLowerCase();
    
    // Detectar marca y modelo
    let name = 'Integrated Graphics';
    let type = 'Integrated';
    
    if (cleanText.includes('nvidia') || cleanText.includes('geforce') || cleanText.includes('rtx') || cleanText.includes('gtx')) {
      type = 'Dedicated';
      if (cleanText.includes('rtx 40')) name = 'NVIDIA RTX 40 Series';
      else if (cleanText.includes('rtx 30')) name = 'NVIDIA RTX 30 Series';
      else if (cleanText.includes('rtx 20')) name = 'NVIDIA RTX 20 Series';
      else if (cleanText.includes('rtx')) name = 'NVIDIA RTX';
      else if (cleanText.includes('gtx 16')) name = 'NVIDIA GTX 16 Series';
      else if (cleanText.includes('gtx 10')) name = 'NVIDIA GTX 10 Series';
      else if (cleanText.includes('gtx')) name = 'NVIDIA GTX';
      else name = 'NVIDIA GPU';
    } else if (cleanText.includes('amd') || cleanText.includes('radeon')) {
      if (cleanText.includes('radeon')) {
        type = 'Dedicated';
        name = 'AMD Radeon';
      } else {
        name = 'AMD Integrated Graphics';
      }
    } else if (cleanText.includes('intel')) {
      if (cleanText.includes('arc')) {
        type = 'Dedicated';
        name = 'Intel Arc';
      } else if (cleanText.includes('iris')) {
        name = 'Intel Iris';
      } else if (cleanText.includes('uhd')) {
        name = 'Intel UHD Graphics';
      } else {
        name = 'Intel Integrated Graphics';
      }
    } else if (cleanText.includes('apple')) {
      name = 'Apple Integrated Graphics';
    }
    
    // Intentar extraer VRAM
    const vramMatch = cleanText.match(/(\d+)\s*(?:gb|g\b)\s*(?:vram|video|graphics)/i);
    const vram = vramMatch ? parseInt(vramMatch[1], 10) : undefined;
    
    return { name, type, vram };
  }

  /**
   * Parsea información de pantalla de un texto.
   */
  static parseDisplay(text: string): { size?: number; resolution?: string; type?: string } {
    const cleanText = text.toLowerCase();
    
    // Extraer tamaño de pantalla
    const sizeMatch = cleanText.match(/(\d+(?:\.\d+)?)\s*(?:"|inch|pulgada)/i);
    const size = sizeMatch ? parseFloat(sizeMatch[1]) : undefined;
    
    // Extraer resolución
    let resolution: string | undefined;
    if (cleanText.includes('4k') || cleanText.includes('3840x2160')) {
      resolution = '3840x2160';
    } else if (cleanText.includes('2k') || cleanText.includes('2560x1440')) {
      resolution = '2560x1440';
    } else if (cleanText.includes('1920x1080') || cleanText.includes('1080p') || cleanText.includes('full hd')) {
      resolution = '1920x1080';
    } else if (cleanText.includes('1366x768') || cleanText.includes('hd')) {
      resolution = '1366x768';
    }
    
    // Determinar tipo de pantalla
    let type: string | undefined;
    if (cleanText.includes('oled')) type = 'OLED';
    else if (cleanText.includes('ips')) type = 'IPS';
    else if (cleanText.includes('tn')) type = 'TN';
    else if (cleanText.includes('va')) type = 'VA';
    else if (cleanText.includes('lcd')) type = 'LCD';
    
    return { size, resolution, type };
  }

  /**
   * Extrae la marca de un nombre de laptop.
   */
  static extractBrand(name: string): string {
    const cleanName = name.toLowerCase();
    const brands = [
      'dell', 'hp', 'lenovo', 'asus', 'acer', 'apple', 'msi', 
      'samsung', 'lg', 'huawei', 'microsoft', 'razer', 'gigabyte', 
      'alienware', 'toshiba', 'fujitsu', 'sony', 'gateway'
    ];
    
    for (const brand of brands) {
      if (cleanName.includes(brand)) {
        return brand.charAt(0).toUpperCase() + brand.slice(1);
      }
    }
    
    return 'Unknown';
  }

  /**
   * Genera un ID único para una laptop basado en su nombre y características.
   */
  static generateId(laptop: { name: string; brand: string; price: number }): string {
    const nameSlug = laptop.name
      .toLowerCase()
      .replace(/[^\w\s]/g, '')
      .replace(/\s+/g, '-');
    
    const brandSlug = laptop.brand.toLowerCase();
    const priceStr = Math.round(laptop.price).toString();
    
    return `${brandSlug}-${nameSlug}-${priceStr}`;
  }
}
