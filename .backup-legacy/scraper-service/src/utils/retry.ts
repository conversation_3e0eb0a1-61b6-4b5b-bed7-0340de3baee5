import { RetryConfig } from '../types';

/**
 * Ejecuta una función con reintentos en caso de error.
 * 
 * @param fn Función a ejecutar
 * @param config Configuración de reintentos
 * @returns Resultado de la función
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  config: RetryConfig,
  onRetry?: (error: Error, attempt: number) => void
): Promise<T> {
  let lastError: Error | null = null;
  let delay = config.initialDelay;

  for (let attempt = 1; attempt <= config.maxRetries + 1; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      // Si es el último intento, lanzar el error
      if (attempt > config.maxRetries) {
        throw lastError;
      }

      // Notificar del reintento si hay callback
      if (onRetry) {
        onRetry(lastError, attempt);
      }

      // Esperar antes del siguiente intento
      await new Promise(resolve => setTimeout(resolve, delay));
      
      // Aumentar el delay para el próximo intento (backoff exponencial)
      delay = Math.min(delay * config.factor, config.maxDelay);
    }
  }

  // Este punto nunca debería alcanzarse, pero TypeScript necesita un return
  throw lastError || new Error('Unknown error during retry');
}