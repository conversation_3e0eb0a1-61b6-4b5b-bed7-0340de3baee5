/**
 * Utilidades para transformar los datos extraídos al formato de la base de datos.
 */
import { LaptopData, DbLaptopListing } from '../types';

/**
 * Transforma los datos de una laptop al formato de la base de datos.
 */
export function transformLaptopToDbFormat(
  laptop: LaptopData,
  sourceId: string,
  scrapingId: number | null = null
): DbLaptopListing {
  return {
    url: laptop.url,
    name: laptop.name,
    brand: laptop.brand,
    price: laptop.price,
    source_url: laptop.source_url,
    // Fix: imageUrl is stored in the database as part of specs
    in_stock: laptop.inStock,
    specs: {
      cpu: {
        name: laptop.specifications.cpu.name,
        cores: laptop.specifications.cpu.cores,
        threads: laptop.specifications.cpu.threads,
        speed: `${laptop.specifications.cpu.baseClockSpeed || ''} GHz`
      },
      ram: {
        size: `${laptop.specifications.ram.size} GB`,
        type: laptop.specifications.ram.type,
        speed: laptop.specifications.ram.speed ? `${laptop.specifications.ram.speed} MHz` : undefined
      },
      storage: {
        size: `${laptop.specifications.storage.size} GB`,
        type: laptop.specifications.storage.type
      },
      display: {
        size: laptop.specifications.display.size ? `${laptop.specifications.display.size}"` : '',
        resolution: laptop.specifications.display.resolution,
        type: laptop.specifications.display.type
      },
      gpu: {
        name: laptop.specifications.gpu.name,
        memory: laptop.specifications.gpu.vram ? `${laptop.specifications.gpu.vram} GB` : undefined
      }
    },
    source_id: sourceId,
    scraping_id: scrapingId !== null ? scrapingId : undefined,
    listing_date: laptop.posted_date
  };
}

/**
 * Transforma una lista de laptops al formato de la base de datos.
 */
export function transformLaptopsToDbFormat(
  laptops: LaptopData[],
  sourceId: string,
  scrapingId: number | null = null
): DbLaptopListing[] {
  return laptops.map(laptop => transformLaptopToDbFormat(laptop, sourceId, scrapingId));
}

/**
 * Normaliza los datos de una laptop para asegurar que todos los campos requeridos estén presentes.
 */
export function normalizeLaptopData(laptop: Partial<LaptopData>): LaptopData {
  // Asegurar que todos los campos requeridos estén presentes
  return {
    id: laptop.id || generateId(laptop),
    name: laptop.name || 'Unknown Model',
    brand: laptop.brand || 'Unknown Brand',
    model_name: laptop.model_name || laptop.name || 'Unknown Model',
    price: laptop.price || 0,
    currency: laptop.currency || 'USD',
    specifications: normalizeSpecifications(laptop.specifications),
    inStock: laptop.inStock !== undefined ? laptop.inStock : true,
    imageUrl: laptop.imageUrl || '',
    description: laptop.description || '',
    url: laptop.url || '',
    source_url: laptop.source_url || '',
    promoOffer: laptop.promoOffer,
    posted_date: laptop.posted_date,
    seller: laptop.seller,
    release_date: laptop.release_date,
    is_available: laptop.is_available !== undefined ? laptop.is_available : true,
    msrp: laptop.msrp
  };
}

/**
 * Normaliza las especificaciones de una laptop.
 */
function normalizeSpecifications(specs: any = {}): any {
  return {
    cpu: {
      name: specs?.cpu?.name || 'Unknown CPU',
      cores: specs?.cpu?.cores || null,
      threads: specs?.cpu?.threads || null,
      baseClockSpeed: specs?.cpu?.baseClockSpeed || null,
      boostClockSpeed: specs?.cpu?.boostClockSpeed || null,
      tdp: specs?.cpu?.tdp || null,
      architecture: specs?.cpu?.architecture || null,
      manufacturer: specs?.cpu?.manufacturer || null
    },
    ram: {
      size: specs?.ram?.size || 0,
      type: specs?.ram?.type || 'Unknown',
      speed: specs?.ram?.speed || null,
      slots_used: specs?.ram?.slots_used || null,
      max_slots: specs?.ram?.max_slots || null,
      expandable: specs?.ram?.expandable || null,
      max_supported_gb: specs?.ram?.max_supported_gb || null
    },
    storage: {
      size: specs?.storage?.size || 0,
      type: specs?.storage?.type || 'Unknown',
      interface: specs?.storage?.interface || null,
      readSpeed: specs?.storage?.readSpeed || null,
      writeSpeed: specs?.storage?.writeSpeed || null,
      is_primary: specs?.storage?.is_primary !== undefined ? specs?.storage?.is_primary : true
    },
    display: {
      size: specs?.display?.size || null,
      resolution: specs?.display?.resolution || null,
      type: specs?.display?.type || null,
      refresh_rate: specs?.display?.refresh_rate || null,
      brightness_nits: specs?.display?.brightness_nits || null,
      color_gamut: specs?.display?.color_gamut || null,
      hdr_support: specs?.display?.hdr_support || null,
      is_touchscreen: specs?.display?.is_touchscreen || null,
      response_time_ms: specs?.display?.response_time_ms || null
    },
    gpu: {
      name: specs?.gpu?.name || 'Integrated Graphics',
      type: specs?.gpu?.type || 'Integrated',
      vram: specs?.gpu?.vram || null,
      is_discrete: specs?.gpu?.is_discrete !== undefined ? specs?.gpu?.is_discrete : (specs?.gpu?.type === 'Dedicada'),
      performance_score: specs?.gpu?.performance_score || null,
      manufacturer: specs?.gpu?.manufacturer || null
    },
    physical: specs?.physical || null,
    battery: specs?.battery || null,
    ports: specs?.ports || null,
    features: specs?.features || null
  };
}

/**
 * Genera un ID único para una laptop basado en su nombre y características.
 */
function generateId(laptop: Partial<LaptopData>): string {
  const nameSlug = (laptop.name || 'unknown')
    .toLowerCase()
    .replace(/[^\w\s]/g, '')
    .replace(/\s+/g, '-');

  const brandSlug = (laptop.brand || 'unknown').toLowerCase();
  const priceStr = Math.round(laptop.price || 0).toString();

  return `${brandSlug}-${nameSlug}-${priceStr}`;
}
