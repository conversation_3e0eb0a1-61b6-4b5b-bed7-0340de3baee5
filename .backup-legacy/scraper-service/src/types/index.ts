// Tipos para la configuración del scraper
export interface RateLimit {
  requests: number;
  period: number; // en milisegundos
}

export interface RetryConfig {
  maxRetries: number;
  initialDelay: number;
  maxDelay: number;
  factor: number;
}

export interface Selectors {
  // Selectores HTML para scrapers basados en DOM
  productCard?: string;
  productName?: string;
  productPrice?: string;
  productImage?: string;
  specifications?: string;
  brand?: string;
  availability?: string;

  // Configuración específica para API GraphQL (Revolico)
  apiUrl?: string;
  subcategorySlug?: string;
  price?: {
    currency: string;
    gte: number;
    lte: number;
    configurable?: boolean;
    defaultRange?: {
      min: number;
      max: number;
    };
  };
  dateFilter?: {
    enabled: boolean;
    field: string;
    daysBack: number;
    configurable: boolean;
  };
  sort?: Array<{
    order: string;
    field: string;
  }>;
  hasImage?: boolean;
  provinceSlug?: string;
  pageLength?: number;
  requiredFields?: string[];

  // Permitir propiedades adicionales
  [key: string]: string | number | boolean | object | undefined;
}

export interface ScraperConfig {
  id: string;
  name: string;
  baseUrl: string;
  queryParams?: string;
  selectors: Selectors;
  rateLimit: RateLimit;
  timeout: number;
  retryConfig: RetryConfig;
}

// Tipos para los datos de laptops
export interface CpuSpecification {
  name: string;
  cores?: number;
  threads?: number;
  baseClockSpeed?: number;
  boostClockSpeed?: number;
  tdp?: number;
  architecture?: string;
  manufacturer?: string;
}

export interface RamSpecification {
  size: number;
  type: string;
  speed?: number;
  slots_used?: number;
  max_slots?: number;
  expandable?: boolean;
  max_supported_gb?: number;
}

export interface StorageSpecification {
  size: number;
  type: string;
  interface?: string;
  readSpeed?: number;
  writeSpeed?: number;
  is_primary?: boolean;
}

export interface DisplaySpecification {
  size?: number;
  resolution?: string;
  type?: string;
  refresh_rate?: number;
  brightness_nits?: number;
  color_gamut?: string;
  hdr_support?: boolean;
  is_touchscreen?: boolean;
  response_time_ms?: number;
}

export interface GpuSpecification {
  name: string;
  type: string;
  vram?: number;
  is_discrete?: boolean;
  performance_score?: number;
  manufacturer?: string;
}

export interface PhysicalSpecification {
  weight_kg?: number;
  height_mm?: number;
  width_mm?: number;
  depth_mm?: number;
  material?: string;
  color?: string;
  has_fingerprint_reader?: boolean;
  has_webcam?: boolean;
  webcam_resolution?: string;
  has_backlit_keyboard?: boolean;
}

export interface BatterySpecification {
  capacity_wh?: number;
  life_hours?: number;
  type?: string;
  is_removable?: boolean;
  fast_charging?: boolean;
}

export interface LaptopSpecifications {
  cpu: CpuSpecification;
  ram: RamSpecification;
  storage: StorageSpecification;
  display: DisplaySpecification;
  gpu: GpuSpecification;
  physical?: PhysicalSpecification;
  battery?: BatterySpecification;
  ports?: string[];
  features?: string[];
}

export interface LaptopData {
  id: string;
  name: string;
  brand: string;
  model_name?: string;
  price: number;
  currency: string;
  specifications: LaptopSpecifications;
  inStock: boolean;
  imageUrl: string;
  description: string;
  promoOffer?: string;
  url: string;
  source_url: string;
  posted_date?: string;
  seller?: string;
  release_date?: string;
  is_available?: boolean;
  msrp?: number;
}

export interface ScrapingResult {
  sourceId: string;
  count: number;
  success: boolean;
  error?: string;
}

export interface ScrapingResponse {
  results: ScrapingResult[];
  successCount: number;
  failureCount: number;
  totalItems: number;
}

// Tipos para la base de datos
export interface DbLaptopListing {
  id?: string;
  laptop_id?: number;
  source_id: string;
  price: number;
  url: string;
  in_stock: boolean;
  shipping_cost?: number;
  rating?: number;
  reviews_count?: number;
  listing_date?: string;
  free_shipping?: boolean;
  estimated_delivery_days?: number;
  has_warranty?: boolean;
  warranty_months?: number;
  name: string;
  brand: string;
  source_url: string;
  specs: {
    cpu: {
      name: string;
      cores?: number;
      threads?: number;
      speed?: string;
    };
    ram: {
      size: string;
      type: string;
      speed?: string;
    };
    storage: {
      size: string;
      type: string;
    };
    display: {
      size: string;
      resolution?: string;
      type?: string;
    };
    gpu: {
      name: string;
      memory?: string;
    };
  };
  scraping_id?: number;
  created_at?: string;
  updated_at?: string;
}

export interface DbScrapingHistory {
  id?: number;
  source_id: number;
  start_time: string;
  end_time?: string;
  status: 'running' | 'completed' | 'failed';
  items_found: number;
  error_message?: string;
}

export interface DbSource {
  id?: number;
  name: string;
  url: string;
  api_endpoint?: string;
  is_active: boolean;
  last_updated?: string;
  update_frequency?: string;
  selectors: Record<string, unknown>;
  last_scrape_attempt?: string;
  scrape_success_count?: number;
  scrape_failure_count?: number;
}
