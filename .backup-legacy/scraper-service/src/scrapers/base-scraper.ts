import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import { ScraperConfig, LaptopData } from '../types';
import { RateLimiter } from '../utils/rate-limiter';
import { withRetry } from '../utils/retry';
import { getRandomUserAgent } from '../utils/user-agent';
import { logger } from '../utils/logger';
import { DataValidator } from '../utils/data-validator';

export abstract class BaseScraper {
  protected browser: Browser | null = null;
  protected rateLimiter: RateLimiter;
  protected config: ScraperConfig;

  constructor(config: ScraperConfig) {
    this.config = config;
    this.rateLimiter = new RateLimiter(
      config.rateLimit.requests,
      config.rateLimit.period
    );
  }

  /**
   * Inicializa el navegador Puppeteer.
   */
  async initialize(): Promise<void> {
    logger.info(`Inicializando scraper para ${this.config.name}`);

    this.browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--disable-gpu',
        '--window-size=1920x1080',
      ],
      protocolTimeout: this.config.timeout || 30000
    });
  }

  /**
   * Cierra el navegador Puppeteer.
   */
  async close(): Promise<void> {
    if (this.browser) {
      logger.info(`Cerrando scraper para ${this.config.name}`);
      await this.browser.close();
      this.browser = null;
    }
  }

  /**
   * Crea una nueva página con la configuración adecuada.
   */
  protected async createPage(): Promise<Page> {
    if (!this.browser) {
      throw new Error('Browser not initialized');
    }

    const page = await this.browser.newPage();

    // Configurar la página
    await page.setUserAgent(getRandomUserAgent());
    await page.setViewport({ width: 1920, height: 1080 });
    await page.setDefaultNavigationTimeout(this.config.timeout || 30000);

    // Interceptar y bloquear recursos innecesarios para mejorar el rendimiento
    await page.setRequestInterception(true);
    page.on('request', (request) => {
      const resourceType = request.resourceType();
      const url = request.url();

      // Bloquear recursos estáticos y multimedia
      if (
        resourceType === 'image' ||
        resourceType === 'stylesheet' ||
        resourceType === 'font' ||
        resourceType === 'media' ||
        url.match(/\.(png|jpg|jpeg|gif|svg|css|woff|woff2|ttf|otf|eot|mp4|webm|ogg|mp3|wav)$/i)
      ) {
        request.abort();
      } else {
        request.continue();
      }
    });

    // Configurar manejo de errores
    page.on('error', error => {
      logger.error(`Error en la página: ${error.message}`);
    });

    page.on('pageerror', error => {
      logger.warn(`Error de JavaScript en la página: ${error.message}`);
    });

    return page;
  }

  /**
   * Navega a una URL con reintentos y límites de velocidad.
   */
  protected async navigateTo(page: Page, url: string): Promise<void> {
    await this.rateLimiter.schedule(async () => {
      await withRetry(
        async () => {
          logger.info(`Navegando a ${url}`);
          await page.goto(url, { waitUntil: 'domcontentloaded' });
        },
        this.config.retryConfig,
        (error, attempt) => {
          logger.warn(`Error al navegar a ${url} (intento ${attempt}): ${error.message}`);
        }
      );
    });
  }

  /**
   * Extrae el texto de un elemento con reintentos.
   */
  protected async extractText(page: Page, selector: string): Promise<string> {
    return withRetry(
      async () => {
        const element = await page.$(selector);
        if (!element) {
          return '';
        }

        const text = await page.evaluate(el => el.textContent, element);
        return (text || '').trim();
      },
      this.config.retryConfig,
      (error, attempt) => {
        logger.warn(`Error al extraer texto de ${selector} (intento ${attempt}): ${error.message}`);
      }
    );
  }

  /**
   * Extrae el atributo de un elemento con reintentos.
   */
  protected async extractAttribute(page: Page, selector: string, attribute: string): Promise<string> {
    return withRetry(
      async () => {
        const element = await page.$(selector);
        if (!element) {
          return '';
        }

        const value = await page.evaluate(
          (el, attr) => el.getAttribute(attr),
          element,
          attribute
        );

        return (value || '').trim();
      },
      this.config.retryConfig,
      (error, attempt) => {
        logger.warn(`Error al extraer atributo ${attribute} de ${selector} (intento ${attempt}): ${error.message}`);
      }
    );
  }

  /**
   * Método principal para scrapear todas las páginas.
   * Debe ser implementado por las clases hijas.
   */
  abstract scrapeAllPages(): Promise<LaptopData[]>;
}