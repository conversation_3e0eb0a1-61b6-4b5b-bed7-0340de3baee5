import { createClient } from '@supabase/supabase-js';
import { ScraperConfig, Selectors, RateLimit, RetryConfig } from '../types';
import { logger } from '../utils/logger';

// Configuración de Supabase
export const SUPABASE_CONFIG = {
  url: process.env.SUPABASE_URL || 'http://localhost:8000',
  key: process.env.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyAgCiAgICAicm9sZSI6ICJhbm9uIiwKICAgICJpc3MiOiAic3VwYWJhc2UtZGVtbyIsCiAgICAiaWF0IjogMTY0MTc2OTIwMCwKICAgICJleHAiOiAxNzk5NTM1NjAwCn0.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE',
  tables: {
    laptopListings: 'laptop_listings',
    scrapingSources: 'sources',
    scrapingHistory: 'scraping_history'
  },
};

// Lista de User-Agents para rotación
export const USER_AGENTS = [
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) Chrome/********* Safari/537.36',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/117.0',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Safari/605.1.15',
  'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
];

// Configuraciones por defecto para cada sitio (fallback)
export const DEFAULT_SCRAPER_CONFIGS: ScraperConfig[] = [
  {
    id: 'laptop-ventas',
    name: 'Laptop Ventas',
    baseUrl: 'https://laptop-ventas.ola.click/products',
    selectors: {
      productPrice: '.product__price',
      productImage: '.v-image__image v-image__image--cover',
      productDescription: '.product-card__description mt-2 text-truncate-2-line',
      stockStatus: '.out-of-stock product-card__out-of-stock',
    },
    rateLimit: {
      requests: 10,
      period: 60000, // 1 minuto
    },
    timeout: 30000, // 30 segundos
    retryConfig: {
      maxRetries: 3,
      initialDelay: 1000,
      maxDelay: 30000,
      factor: 2,
    },
  },
  {
    id: 'gaminglaptop',
    name: 'Gaming Laptop Deals',
    baseUrl: 'https://gaminglaptop.deals',
    selectors: {
      productCard: '.laptop-item',
      productName: '.laptop-name',
      productPrice: '.laptop-price',
      productImage: '.laptop-image img',
      specifications: '.laptop-specs',
      brand: '.laptop-brand',
      availability: '.in-stock-label',
    },
    rateLimit: {
      requests: 5,
      period: 60000, // 1 minuto
    },
    timeout: 30000, // 30 segundos
    retryConfig: {
      maxRetries: 3,
      initialDelay: 1000,
      maxDelay: 30000,
      factor: 2,
    },
  },
  {
    id: 'revolico',
    name: 'Revolico',
    baseUrl: 'https://www.revolico.com/search',
    queryParams: '?min_price=800&max_price=2000&currency=USD&category=computadoras&subcategory=laptop&province=la-habana',
    selectors: {
      productCard: '.classified-item',
      productName: '.classified-title',
      productPrice: '.classified-price',
      productImage: '.classified-image img',
      specifications: '.classified-description',
      brand: '.classified-brand',
      availability: '.availability-status',
    },
    rateLimit: {
      requests: 2,
      period: 60000, // 1 minuto
    },
    timeout: 30000, // 30 segundos
    retryConfig: {
      maxRetries: 3,
      initialDelay: 1000,
      maxDelay: 30000,
      factor: 2,
    },
  },
];

// Cliente de Supabase
const supabase = createClient(SUPABASE_CONFIG.url, SUPABASE_CONFIG.key);

/**
 * Carga las configuraciones de scraping desde la base de datos.
 * Si no hay configuraciones en la base de datos, utiliza las configuraciones por defecto.
 */
export async function loadScraperConfigs(): Promise<ScraperConfig[]> {
  try {
    // Intentar cargar las configuraciones desde la base de datos
    const { data, error } = await supabase
      .from(SUPABASE_CONFIG.tables.scrapingSources)
      .select('*')
      .eq('is_active', true);

    if (error) {
      logger.error(`Error al cargar configuraciones desde Supabase: ${error.message}`);
      return DEFAULT_SCRAPER_CONFIGS;
    }

    if (!data || data.length === 0) {
      logger.warn('No se encontraron configuraciones en la base de datos, usando configuraciones por defecto');
      return DEFAULT_SCRAPER_CONFIGS;
    }

    // Convertir los datos de la base de datos al formato ScraperConfig
    return data.map(source => {
      // Extraer los selectores del campo JSONB
      const selectors: Selectors = source.selectors || {
        productCard: '',
        productName: '',
        productPrice: '',
        productImage: '',
        specifications: ''
      };

      // Configuración de rate limit por defecto si no está definida
      const rateLimit: RateLimit = {
        requests: 5,
        period: 60000
      };

      // Configuración de reintentos por defecto si no está definida
      const retryConfig: RetryConfig = {
        maxRetries: 3,
        initialDelay: 1000,
        maxDelay: 30000,
        factor: 2
      };

      // Crear y devolver la configuración del scraper
      return {
        id: source.id.toString(),
        name: source.name,
        baseUrl: source.url,
        queryParams: source.api_endpoint, // Usar api_endpoint como queryParams
        selectors,
        rateLimit,
        timeout: 30000, // Valor por defecto
        retryConfig
      };
    });
  } catch (error) {
    logger.error(`Error inesperado al cargar configuraciones: ${error instanceof Error ? error.message : String(error)}`);
    return DEFAULT_SCRAPER_CONFIGS;
  }
}

/**
 * Registra el inicio de una sesión de scraping en la base de datos.
 */
export async function registerScrapingStart(sourceId: string): Promise<number | null> {
  try {
    const { data, error } = await supabase
      .from(SUPABASE_CONFIG.tables.scrapingHistory)
      .insert({
        source_id: sourceId,
        start_time: new Date().toISOString(),
        status: 'running'
      })
      .select('id');

    if (error) {
      logger.error(`Error al registrar inicio de scraping: ${error.message}`);
      return null;
    }

    return data && data.length > 0 ? data[0].id : null;
  } catch (error) {
    logger.error(`Error inesperado al registrar inicio de scraping: ${error instanceof Error ? error.message : String(error)}`);
    return null;
  }
}

/**
 * Actualiza el estado de una sesión de scraping en la base de datos.
 */
export async function updateScrapingStatus(
  id: number,
  status: 'completed' | 'failed',
  itemsFound: number = 0,
  errorMessage?: string
): Promise<boolean> {
  try {
    const { error } = await supabase
      .from(SUPABASE_CONFIG.tables.scrapingHistory)
      .update({
        end_time: new Date().toISOString(),
        status,
        items_found: itemsFound,
        error_message: errorMessage
      })
      .eq('id', id);

    if (error) {
      logger.error(`Error al actualizar estado de scraping: ${error.message}`);
      return false;
    }

    return true;
  } catch (error) {
    logger.error(`Error inesperado al actualizar estado de scraping: ${error instanceof Error ? error.message : String(error)}`);
    return false;
  }
}

// Exportar una función para obtener las configuraciones
export let SCRAPER_CONFIGS: ScraperConfig[] = DEFAULT_SCRAPER_CONFIGS;

// Función para inicializar las configuraciones
export async function initializeConfigs(): Promise<void> {
  SCRAPER_CONFIGS = await loadScraperConfigs();
  logger.info(`Configuraciones cargadas: ${SCRAPER_CONFIGS.length} fuentes`);
}