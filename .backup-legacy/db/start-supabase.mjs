#!/usr/bin/env node

/**
 * Script para iniciar Supabase local
 * 
 * Este script verifica si Supabase local está en ejecución y lo inicia si es necesario.
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import path from 'path';
import fs from 'fs';

// Obtener __dirname en ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Ruta al directorio de Supabase local
const SUPABASE_DIR = '/home/<USER>/GithubProjects/supabase';

// Verificar que el directorio de Supabase existe
if (!fs.existsSync(SUPABASE_DIR)) {
  console.error('\x1b[31m%s\x1b[0m', `❌ Error: El directorio de Supabase no existe en ${SUPABASE_DIR}`);
  console.error('\x1b[33m%s\x1b[0m', 'Por favor, instala Supabase CLI y crea un proyecto local:');
  console.error('1. npm install -g supabase');
  console.error('2. mkdir -p /home/<USER>/GithubProjects/supabase');
  console.error('3. cd /home/<USER>/GithubProjects/supabase');
  console.error('4. supabase init');
  process.exit(1);
}

// Verificar si Supabase está en ejecución
console.log('Verificando si Supabase local está en ejecución...');

// Función para verificar si Supabase está en ejecución
async function isSupabaseRunning() {
  return new Promise((resolve) => {
    const curl = spawn('curl', ['-s', 'http://localhost:8000/health']);
    
    curl.on('close', (code) => {
      resolve(code === 0);
    });
  });
}

// Función para iniciar Supabase
async function startSupabase() {
  return new Promise((resolve, reject) => {
    console.log('Iniciando Supabase local...');
    
    const supabase = spawn('supabase', ['start'], {
      cwd: SUPABASE_DIR,
      stdio: 'inherit'
    });
    
    supabase.on('close', (code) => {
      if (code === 0) {
        console.log('\x1b[32m%s\x1b[0m', '✅ Supabase local iniciado correctamente');
        resolve();
      } else {
        console.error('\x1b[31m%s\x1b[0m', `❌ Error al iniciar Supabase local (código ${code})`);
        reject(new Error(`Error al iniciar Supabase local (código ${code})`));
      }
    });
  });
}

// Función principal
async function main() {
  try {
    const running = await isSupabaseRunning();
    
    if (running) {
      console.log('\x1b[32m%s\x1b[0m', '✅ Supabase local ya está en ejecución');
    } else {
      await startSupabase();
    }
  } catch (err) {
    console.error('\x1b[31m%s\x1b[0m', `❌ Error: ${err.message}`);
    process.exit(1);
  }
}

// Ejecutar función principal
main();
