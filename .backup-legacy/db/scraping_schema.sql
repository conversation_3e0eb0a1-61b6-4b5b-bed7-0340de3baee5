-- Esquema para el sistema de scraping refactorizado

-- Tabla para almacenar las fuentes de scraping
CREATE TABLE IF NOT EXISTS sources (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  url VARCHAR(255) NOT NULL,
  api_endpoint VARCHAR(255),
  is_active BOOLEAN DEFAULT true,
  selectors JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabla para almacenar el historial de scraping
CREATE TABLE IF NOT EXISTS scraping_history (
  id SERIAL PRIMARY KEY,
  source_id INTEGER REFERENCES sources(id),
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  end_time TIMESTAMP WITH TIME ZONE,
  status VARCHAR(50) NOT NULL, -- 'running', 'completed', 'failed'
  items_found INTEGER DEFAULT 0,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Actualizar la tabla de laptop_listings para incluir referencias a las fuentes y al historial
ALTER TABLE laptop_listings ADD COLUMN IF NOT EXISTS source_id INTEGER REFERENCES sources(id);
ALTER TABLE laptop_listings ADD COLUMN IF NOT EXISTS scraping_id INTEGER REFERENCES scraping_history(id);

-- Función para actualizar el timestamp de updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers para actualizar updated_at automáticamente
CREATE TRIGGER update_sources_updated_at
BEFORE UPDATE ON sources
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_scraping_history_updated_at
BEFORE UPDATE ON scraping_history
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Insertar fuentes de ejemplo
INSERT INTO sources (name, url, api_endpoint, is_active, selectors)
VALUES
  ('Laptop Ventas', 'https://laptop-ventas.ola.click/products', NULL, true, '{
    "productCard": ".products_grid .product_card",
    "productName": ".product_card_name",
    "productPrice": ".product_card_price",
    "productImage": ".product_card_image img",
    "specifications": ".product_card_specifications",
    "brand": ".product_card_brand",
    "availability": ".product_card_stock"
  }'),
  ('Gaming Laptop Deals', 'https://gaminglaptop.deals', NULL, true, '{
    "productCard": ".laptop-item",
    "productName": ".laptop-name",
    "productPrice": ".laptop-price",
    "productImage": ".laptop-image img",
    "specifications": ".laptop-specs",
    "brand": ".laptop-brand",
    "availability": ".in-stock-label"
  }'),
  ('Revolico', 'https://www.revolico.com', 'search', true, '{
    "apiUrl": "https://graphql-api.revolico.app/",
    "subcategorySlug": "computadoras_laptop",
    "price": {
      "currency": "USD",
      "gte": 800,
      "lte": 2000
    },
    "hasImage": true,
    "provinceSlug": "la-habana",
    "pageLength": 20
  }')
ON CONFLICT (id) DO NOTHING;

-- Índices para mejorar el rendimiento
CREATE INDEX IF NOT EXISTS idx_laptop_listings_source_id ON laptop_listings(source_id);
CREATE INDEX IF NOT EXISTS idx_laptop_listings_scraping_id ON laptop_listings(scraping_id);
CREATE INDEX IF NOT EXISTS idx_scraping_history_source_id ON scraping_history(source_id);
CREATE INDEX IF NOT EXISTS idx_scraping_history_status ON scraping_history(status);
