/**
 * Script para ejecutar la migración de la base de datos
 *
 * Este script ejecuta los archivos SQL de migración en el orden correcto.
 * Utiliza la API de Supabase para ejecutar las consultas SQL.
 */

import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';

// Obtener __dirname en ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configurar dotenv para cargar el archivo .env de la raíz del proyecto
const rootEnvFile = path.join(__dirname, '../../.env');
dotenv.config({ path: rootEnvFile });

// Configuración de Supabase local
const supabaseUrl = process.env.SUPABASE_URL || 'http://localhost:8000';
const supabaseKey = process.env.SUPABASE_SERVICE_KEY; // Necesitamos la clave de servicio para ejecutar migraciones

if (!supabaseKey) {
  console.error('\x1b[31m%s\x1b[0m', 'Error: SUPABASE_SERVICE_KEY no está definida en el archivo .env');
  console.error('\x1b[33m%s\x1b[0m', 'Para desarrollo local, la clave de servicio debe ser similar a la clave anónima pero con el rol "service_role"');
  console.error('Actualiza el archivo .env con la clave de servicio correcta');
  process.exit(1);
}

// Verificar que Supabase local esté en ejecución
console.log(`Conectando a Supabase local en ${supabaseUrl}...`);

// Ya no necesitamos crear un cliente de Supabase, ya que usaremos psql directamente
console.log(`Configuración de Supabase: ${supabaseUrl}`);
console.log(`Usando clave de servicio: ${supabaseKey.substring(0, 10)}...`);
console.log('Usando psql directamente para ejecutar las consultas SQL');

// Archivos de migración en orden
let migrationFiles = [
  '01_initial_schema.sql',
  '02_llm_compatibility_schema.sql',
  '03_migration_script.sql',
  '04_schema_updates.sql'
];

// Filtrar los archivos según las fases especificadas en MIGRATION_PHASES
if (process.env.MIGRATION_PHASES) {
  const phases = process.env.MIGRATION_PHASES.split(',');
  migrationFiles = migrationFiles.filter(file => phases.includes(file));
}

// Función para ejecutar una consulta SQL usando psql directamente
async function executeQuery(query) {
  try {
    // Guardar la consulta SQL en un archivo temporal
    const tempFile = path.join(__dirname, 'temp_query.sql');
    fs.writeFileSync(tempFile, query);

    // Ejecutar la consulta usando psql
    console.log('Ejecutando consulta SQL usando psql directamente...');

    // Usar child_process.execSync para ejecutar el comando
    const { execSync } = await import('child_process');

    try {
      // Copiar el archivo al contenedor y ejecutarlo
      execSync(`docker cp ${tempFile} supabase-db:/tmp/temp_query.sql && docker exec supabase-db psql -U postgres -d postgres -f /tmp/temp_query.sql`, {
        stdio: 'inherit'
      });
      console.log('Consulta ejecutada correctamente');
      return { success: true };
    } catch (execError) {
      console.error('Error al ejecutar la consulta:', execError.message);
      throw new Error(`Error executing query with psql: ${execError.message}`);
    } finally {
      // Eliminar el archivo temporal
      if (fs.existsSync(tempFile)) {
        fs.unlinkSync(tempFile);
      }
    }
  } catch (err) {
    console.error('Error:', err.message);
    throw err;
  }
}

// Función para ejecutar un archivo SQL
async function executeSqlFile(filePath) {
  try {
    console.log(`Executing ${filePath}...`);

    // Leer el archivo SQL
    const sql = fs.readFileSync(filePath, 'utf8');

    // Dividir el archivo en consultas individuales
    // Nota: Esta es una implementación mejorada que maneja mejor los casos complejos
    const queries = [];
    let currentQuery = '';
    let inFunction = false;

    // Dividir por líneas para un mejor procesamiento
    const lines = sql.split('\n');

    for (const line of lines) {
      // Ignorar comentarios
      if (line.trim().startsWith('--')) {
        continue;
      }

      // Detectar inicio/fin de funciones o bloques
      if (line.includes('CREATE OR REPLACE FUNCTION') || line.includes('CREATE FUNCTION')) {
        inFunction = true;
      }

      // Añadir la línea a la consulta actual
      currentQuery += line + '\n';

      // Si estamos en una función, solo terminamos cuando encontramos el final con $$ LANGUAGE
      if (inFunction && line.includes('$$ LANGUAGE')) {
        inFunction = false;
        queries.push(currentQuery.trim());
        currentQuery = '';
        continue;
      }

      // Para consultas normales, terminamos con punto y coma
      if (!inFunction && line.trim().endsWith(';')) {
        queries.push(currentQuery.trim());
        currentQuery = '';
      }
    }

    // Añadir cualquier consulta restante
    if (currentQuery.trim()) {
      queries.push(currentQuery.trim());
    }

    // Filtrar consultas vacías
    const validQueries = queries.filter(q => q.trim().length > 0);

    console.log(`Found ${validQueries.length} queries in ${filePath}`);

    // Ejecutar cada consulta
    for (const query of validQueries) {
      try {
        await executeQuery(query.endsWith(';') ? query : query + ';');
      } catch (queryErr) {
        console.error(`Error in query: ${query.substring(0, 100)}...`);
        throw queryErr;
      }
    }

    console.log(`Successfully executed ${filePath}`);
  } catch (err) {
    console.error(`Error executing ${filePath}:`, err.message);
    throw err;
  }
}

// Función principal
async function runMigration() {
  try {
    console.log('Starting database migration...');

    // Crear una transacción
    await executeQuery('BEGIN;');

    // Ejecutar cada archivo de migración
    for (const file of migrationFiles) {
      const filePath = path.join(__dirname, file);
      await executeSqlFile(filePath);
    }

    // Confirmar la transacción
    await executeQuery('COMMIT;');

    console.log('Migration completed successfully!');
  } catch (err) {
    // Revertir la transacción en caso de error
    await executeQuery('ROLLBACK;');
    console.error('Migration failed:', err.message);
    process.exit(1);
  }
}

// Ejecutar la migración
runMigration();
