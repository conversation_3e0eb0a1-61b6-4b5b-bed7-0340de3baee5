/**
 * Script para insertar datos de ejemplo de laptops
 *
 * <PERSON>ste script inserta datos de ejemplo de laptops en la base de datos de Supabase.
 */

import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';
import { execSync } from 'child_process';

// Obtener __dirname en ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configurar dotenv para cargar el archivo .env de la raíz del proyecto
const rootEnvFile = path.join(__dirname, '../../.env');
dotenv.config({ path: rootEnvFile });

// Configuración de Supabase local
const supabaseUrl = process.env.SUPABASE_URL || 'http://localhost:8000';
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseKey) {
  console.error('\x1b[31m%s\x1b[0m', 'Error: SUPABASE_SERVICE_KEY no está definida en el archivo .env');
  console.error('\x1b[33m%s\x1b[0m', 'Para desarrollo local, la clave de servicio debe ser similar a la clave anónima pero con el rol "service_role"');
  console.error('Actualiza el archivo .env con la clave de servicio correcta');
  process.exit(1);
}

// Verificar que Supabase local esté en ejecución
console.log(`Conectando a Supabase local en ${supabaseUrl}...`);

// Ya no necesitamos crear un cliente de Supabase, ya que usaremos psql directamente
console.log(`Configuración de Supabase: ${supabaseUrl}`);
console.log(`Usando clave de servicio: ${supabaseKey.substring(0, 10)}...`);
console.log('Usando psql directamente para ejecutar las consultas SQL');

// Función para insertar laptops de ejemplo usando psql directamente
async function insertLaptops() {
  console.log('Insertando laptops de ejemplo usando psql directamente...');
  
  // Crear una consulta SQL para insertar laptops de ejemplo
  const sqlQuery = `
    -- Insertar laptops de ejemplo
    INSERT INTO laptops (model_name, brand_id, release_date, description, url, image_url, created_at, updated_at)
    VALUES
      ('ThinkPad X1 Carbon Gen 10', 1, '2022-01-01', 'Laptop empresarial ultraligera con excelente teclado y batería de larga duración', 'https://www.lenovo.com/es/es/laptops/thinkpad/thinkpad-x1/X1-Carbon-Gen-10/p/22TP2X1X1C10', 'https://example.com/thinkpad-x1-carbon.jpg', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('MacBook Pro 16" M2 Pro', 2, '2023-01-01', 'Potente laptop para profesionales creativos con chip M2 Pro', 'https://www.apple.com/es/macbook-pro/', 'https://example.com/macbook-pro-16.jpg', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('XPS 13 Plus', 3, '2022-06-01', 'Laptop ultradelgada con diseño minimalista y pantalla InfinityEdge', 'https://www.dell.com/es-es/shop/dell-laptops/xps-13-plus/spd/xps-13-9320-laptop', 'https://example.com/xps-13-plus.jpg', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('ROG Zephyrus G14', 4, '2022-03-01', 'Laptop gaming compacta con gran rendimiento y portabilidad', 'https://rog.asus.com/es/laptops/rog-zephyrus/rog-zephyrus-g14-series/', 'https://example.com/rog-zephyrus-g14.jpg', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('Surface Laptop 5', 5, '2022-10-01', 'Laptop elegante con pantalla táctil y Windows 11', 'https://www.microsoft.com/es-es/surface/devices/surface-laptop-5', 'https://example.com/surface-laptop-5.jpg', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('Gram 17', 6, '2022-02-01', 'Laptop ultraligera con pantalla grande y excelente batería', 'https://www.lg.com/es/portatiles/lg-gram-17', 'https://example.com/lg-gram-17.jpg', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('Spectre x360 14', 7, '2022-01-01', 'Convertible premium con pantalla OLED y diseño elegante', 'https://www.hp.com/es-es/shop/product.aspx?id=5R1V7EA&opt=ABE&sel=NTB', 'https://example.com/spectre-x360-14.jpg', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('Yoga 9i', 8, '2022-05-01', 'Convertible premium con bisagra soundbar y pantalla OLED', 'https://www.lenovo.com/es/es/laptops/yoga/yoga-9-series/Yoga-9i-Gen-7-14-inch-Intel/p/LEN101Y0009', 'https://example.com/yoga-9i.jpg', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('Framework Laptop', 9, '2022-07-01', 'Laptop modular y reparable con componentes intercambiables', 'https://frame.work/', 'https://example.com/framework-laptop.jpg', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('Razer Blade 14', 10, '2022-02-01', 'Laptop gaming compacta con potente GPU y pantalla de alta tasa de refresco', 'https://www.razer.com/es-es/gaming-laptops/razer-blade-14', 'https://example.com/razer-blade-14.jpg', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
    
    -- Insertar displays para las laptops
    INSERT INTO displays (laptop_id, size_inches, resolution_id, panel_type_id, refresh_rate, brightness_nits, color_gamut, hdr_support, touch_screen)
    VALUES
      (1, 14.0, 3, 2, 60, 400, 'sRGB 100%', false, false),
      (2, 16.0, 4, 3, 120, 1000, 'P3 100%', true, false),
      (3, 13.4, 3, 2, 60, 500, 'sRGB 100%', false, false),
      (4, 14.0, 3, 1, 144, 300, 'sRGB 100%', false, false),
      (5, 13.5, 3, 2, 60, 400, 'sRGB 100%', false, true),
      (6, 17.0, 3, 2, 60, 350, 'sRGB 99%', false, false),
      (7, 14.0, 3, 3, 60, 400, 'P3 100%', true, true),
      (8, 14.0, 3, 3, 60, 400, 'P3 100%', true, true),
      (9, 13.5, 3, 2, 60, 400, 'sRGB 100%', false, false),
      (10, 14.0, 3, 1, 144, 500, 'sRGB 100%', false, false);
    
    -- Insertar especificaciones físicas para las laptops
    INSERT INTO physical_specs (laptop_id, weight_kg, thickness_mm, width_mm, depth_mm, material, color, backlit_keyboard, webcam_resolution, fingerprint_reader, face_recognition)
    VALUES
      (1, 1.12, 15.36, 315.6, 222.5, 'Carbon Fiber', 'Black', true, '1080p', true, false),
      (2, 2.15, 16.8, 355.7, 248.1, 'Aluminum', 'Silver', true, '1080p', true, true),
      (3, 1.23, 15.28, 295.3, 199.04, 'Aluminum', 'Platinum', true, '720p', true, false),
      (4, 1.65, 19.5, 324, 222, 'Aluminum', 'White', true, '720p', false, false),
      (5, 1.27, 14.5, 308, 223, 'Aluminum', 'Platinum', true, '720p', true, true),
      (6, 1.35, 17.8, 380.2, 260.1, 'Magnesium Alloy', 'White', true, '1080p', true, false),
      (7, 1.36, 17.0, 319.5, 217.0, 'Aluminum', 'Silver', true, '720p', true, false),
      (8, 1.4, 15.25, 318, 230, 'Aluminum', 'Oatmeal', true, '1080p', true, false),
      (9, 1.3, 15.85, 296, 229, 'Aluminum', 'Silver', true, '1080p', true, false),
      (10, 1.78, 16.8, 319.7, 220, 'Aluminum', 'Black', true, '1080p', false, false);
    
    -- Insertar CPUs para las laptops
    INSERT INTO cpus (model, manufacturer, architecture_id, cores, threads, base_clock_ghz, boost_clock_ghz, tdp_watts, release_date)
    VALUES
      ('Intel Core i7-1260P', 'Intel', 1, 12, 16, 2.1, 4.7, 28, '2022-01-01'),
      ('Apple M2 Pro', 'Apple', 2, 12, 12, 3.5, 3.5, 30, '2023-01-01'),
      ('Intel Core i7-1280P', 'Intel', 1, 14, 20, 1.8, 4.8, 28, '2022-01-01'),
      ('AMD Ryzen 9 6900HS', 'AMD', 3, 8, 16, 3.3, 4.9, 35, '2022-01-01'),
      ('Intel Core i7-1255U', 'Intel', 1, 10, 12, 1.7, 4.7, 15, '2022-01-01'),
      ('Intel Core i7-1260P', 'Intel', 1, 12, 16, 2.1, 4.7, 28, '2022-01-01'),
      ('Intel Core i7-1260P', 'Intel', 1, 12, 16, 2.1, 4.7, 28, '2022-01-01'),
      ('Intel Core i7-1260P', 'Intel', 1, 12, 16, 2.1, 4.7, 28, '2022-01-01'),
      ('Intel Core i7-1280P', 'Intel', 1, 14, 20, 1.8, 4.8, 28, '2022-01-01'),
      ('AMD Ryzen 9 6900HX', 'AMD', 3, 8, 16, 3.3, 4.9, 45, '2022-01-01');
    
    -- Insertar GPUs para las laptops
    INSERT INTO gpus (model, manufacturer, vram_gb, vram_type, cuda_cores, tensor_cores, ray_tracing_cores, base_clock_mhz, boost_clock_mhz, tdp_watts, release_date)
    VALUES
      ('Intel Iris Xe', 'Intel', 0, NULL, 96, 0, 0, 400, 1300, 15, '2022-01-01'),
      ('Apple M2 Pro 19-core GPU', 'Apple', 0, NULL, 2432, 0, 0, 400, 1400, 30, '2023-01-01'),
      ('Intel Iris Xe', 'Intel', 0, NULL, 96, 0, 0, 400, 1300, 15, '2022-01-01'),
      ('AMD Radeon 680M', 'AMD', 0, NULL, 768, 0, 0, 400, 2200, 15, '2022-01-01'),
      ('Intel Iris Xe', 'Intel', 0, NULL, 96, 0, 0, 400, 1300, 15, '2022-01-01'),
      ('Intel Iris Xe', 'Intel', 0, NULL, 96, 0, 0, 400, 1300, 15, '2022-01-01'),
      ('Intel Iris Xe', 'Intel', 0, NULL, 96, 0, 0, 400, 1300, 15, '2022-01-01'),
      ('Intel Iris Xe', 'Intel', 0, NULL, 96, 0, 0, 400, 1300, 15, '2022-01-01'),
      ('Intel Iris Xe', 'Intel', 0, NULL, 96, 0, 0, 400, 1300, 15, '2022-01-01'),
      ('NVIDIA RTX 3080 Ti Laptop', 'NVIDIA', 16, 'GDDR6', 7424, 232, 58, 1395, 1695, 150, '2022-01-01');
    
    -- Insertar configuraciones de RAM para las laptops
    INSERT INTO ram_configurations (size_gb, speed_mhz, memory_type_id, channels, soldered, max_capacity_gb)
    VALUES
      (16, 4800, 3, 2, true, 16),
      (32, 6400, 4, 4, true, 32),
      (16, 4800, 3, 2, true, 16),
      (16, 4800, 3, 2, false, 32),
      (16, 4800, 3, 2, true, 16),
      (16, 4800, 3, 2, false, 32),
      (16, 4800, 3, 2, true, 16),
      (16, 4800, 3, 2, true, 16),
      (16, 4800, 3, 2, false, 64),
      (16, 4800, 3, 2, false, 32);
    
    -- Insertar dispositivos de almacenamiento para las laptops
    INSERT INTO storage_devices (capacity_gb, interface_id, read_speed_mbps, write_speed_mbps, form_factor, manufacturer)
    VALUES
      (512, 1, 3500, 2500, 'M.2 2280', 'Samsung'),
      (1024, 1, 7000, 5000, 'Custom', 'Apple'),
      (512, 1, 3500, 2500, 'M.2 2280', 'Western Digital'),
      (1024, 1, 3500, 2500, 'M.2 2280', 'Samsung'),
      (512, 1, 3500, 2500, 'M.2 2280', 'Samsung'),
      (512, 1, 3500, 2500, 'M.2 2280', 'Samsung'),
      (512, 1, 3500, 2500, 'M.2 2280', 'Samsung'),
      (512, 1, 3500, 2500, 'M.2 2280', 'Samsung'),
      (512, 1, 3500, 2500, 'M.2 2280', 'Western Digital'),
      (1024, 1, 7000, 5000, 'M.2 2280', 'Samsung');
    
    -- Asociar CPUs con laptops
    INSERT INTO laptop_cpus (laptop_id, cpu_id, is_primary)
    VALUES
      (1, 1, true),
      (2, 2, true),
      (3, 3, true),
      (4, 4, true),
      (5, 5, true),
      (6, 6, true),
      (7, 7, true),
      (8, 8, true),
      (9, 9, true),
      (10, 10, true);
    
    -- Asociar GPUs con laptops
    INSERT INTO laptop_gpus (laptop_id, gpu_id, is_primary)
    VALUES
      (1, 1, true),
      (2, 2, true),
      (3, 3, true),
      (4, 4, true),
      (5, 5, true),
      (6, 6, true),
      (7, 7, true),
      (8, 8, true),
      (9, 9, true),
      (10, 10, true);
    
    -- Asociar RAM con laptops
    INSERT INTO laptop_ram (laptop_id, ram_configuration_id)
    VALUES
      (1, 1),
      (2, 2),
      (3, 3),
      (4, 4),
      (5, 5),
      (6, 6),
      (7, 7),
      (8, 8),
      (9, 9),
      (10, 10);
    
    -- Asociar almacenamiento con laptops
    INSERT INTO laptop_storage (laptop_id, storage_id, is_primary)
    VALUES
      (1, 1, true),
      (2, 2, true),
      (3, 3, true),
      (4, 4, true),
      (5, 5, true),
      (6, 6, true),
      (7, 7, true),
      (8, 8, true),
      (9, 9, true),
      (10, 10, true);
    
    -- Insertar puntuaciones para las laptops
    INSERT INTO laptop_scores (laptop_id, overall_score, llm_performance_score, battery_life_score, build_quality_score, display_score, keyboard_score, performance_score, value_score, thermal_score, noise_score, portability_score)
    VALUES
      (1, 85, 70, 90, 95, 80, 95, 75, 80, 85, 90, 95),
      (2, 95, 90, 85, 95, 95, 90, 95, 70, 80, 85, 80),
      (3, 88, 75, 80, 90, 85, 80, 80, 75, 80, 85, 90),
      (4, 90, 85, 75, 85, 90, 85, 90, 85, 75, 70, 80),
      (5, 87, 70, 85, 90, 85, 85, 75, 80, 85, 90, 85),
      (6, 86, 70, 90, 85, 80, 80, 75, 85, 85, 90, 95),
      (7, 89, 75, 80, 95, 90, 85, 80, 75, 80, 85, 85),
      (8, 88, 75, 80, 90, 90, 85, 80, 80, 80, 85, 85),
      (9, 87, 75, 80, 95, 80, 85, 80, 90, 85, 85, 85),
      (10, 92, 90, 70, 90, 90, 90, 95, 75, 70, 65, 75);
    
    -- Generar compatibilidades con modelos LLM
    DO $$
    DECLARE
      llm_rec RECORD;
      laptop_rec RECORD;
      base_score INT;
      tokens_per_second INT;
      context_length INT;
      memory_usage NUMERIC(5, 2);
      batch_size INT;
      can_run_offline BOOLEAN;
      qualitative_assessment TEXT;
    BEGIN
      -- Iterar sobre todos los modelos LLM
      FOR llm_rec IN SELECT id, name, parameters_billions, requires_gpu FROM llm_models
      LOOP
        -- Iterar sobre todas las laptops
        FOR laptop_rec IN SELECT id, model_name FROM laptops
        LOOP
          -- Generar puntuación aleatoria basada en el tamaño del modelo
          IF llm_rec.parameters_billions <= 7 THEN
            base_score := floor(random() * 30) + 70; -- 70-99
          ELSIF llm_rec.parameters_billions <= 20 THEN
            base_score := floor(random() * 40) + 50; -- 50-89
          ELSE
            base_score := floor(random() * 60) + 30; -- 30-89
          END IF;
          
          -- Ajustar para modelos que requieren GPU
          IF llm_rec.requires_gpu AND random() > 0.7 THEN
            base_score := GREATEST(10, base_score - 30); -- Penalizar algunas laptops que no tienen GPU potente
          END IF;
          
          -- Generar tokens por segundo basado en la puntuación
          tokens_per_second := floor((base_score / 100.0) * (CASE WHEN llm_rec.parameters_billions <= 7 THEN 30 ELSE 15 END) + 1);
          
          -- Generar contexto máximo
          context_length := floor(random() * 3 + 1) * 2048;
          
          -- Generar uso de memoria
          memory_usage := llm_rec.parameters_billions * (random() * 0.3 + 0.2);
          
          -- Generar tamaño de lote recomendado
          IF llm_rec.parameters_billions <= 7 THEN
            batch_size := floor(random() * 8 + 1);
          ELSE
            batch_size := floor(random() * 4 + 1);
          END IF;
          
          -- Generar si puede ejecutarse offline
          can_run_offline := llm_rec.parameters_billions <= 13 AND random() > 0.3;
          
          -- Generar evaluación cualitativa
          IF base_score >= 80 THEN
            qualitative_assessment := 'Esta laptop es excelente para ejecutar ' || llm_rec.name || '. Recomendada para uso intensivo.';
          ELSIF base_score >= 60 THEN
            qualitative_assessment := 'Esta laptop es buena para ejecutar ' || llm_rec.name || '. Recomendada para uso intensivo.';
          ELSIF base_score >= 40 THEN
            qualitative_assessment := 'Esta laptop es adecuada para ejecutar ' || llm_rec.name || '. No recomendada para uso intensivo.';
          ELSE
            qualitative_assessment := 'Esta laptop puede tener dificultades para ejecutar ' || llm_rec.name || '. No recomendada para uso intensivo.';
          END IF;
          
          -- Insertar en la tabla
          INSERT INTO laptop_llm_compatibility (
            laptop_id, llm_id, estimated_tokens_per_second, max_context_length,
            score, qualitative_assessment, can_run_offline, recommended_batch_size,
            estimated_memory_usage_gb
          ) VALUES (
            laptop_rec.id, llm_rec.id, tokens_per_second, context_length,
            base_score, qualitative_assessment, can_run_offline, batch_size,
            round(memory_usage::numeric, 2)
          );
        END LOOP;
      END LOOP;
    END $$;
    
    -- Contar cuántas laptops se insertaron
    SELECT COUNT(*) FROM laptops;
  `;
  
  try {
    // Guardar la consulta SQL en un archivo temporal
    const tempFile = path.join(__dirname, 'temp_laptops.sql');
    fs.writeFileSync(tempFile, sqlQuery);
    
    // Ejecutar la consulta usando psql
    console.log('Ejecutando consulta SQL para insertar laptops...');
    
    try {
      execSync(`docker cp ${tempFile} supabase-db:/tmp/temp_laptops.sql && docker exec supabase-db psql -U postgres -d postgres -f /tmp/temp_laptops.sql`, {
        stdio: 'inherit'
      });
      console.log('Laptops insertadas correctamente');
    } catch (execError) {
      console.error('Error al insertar laptops:', execError.message);
    } finally {
      // Eliminar el archivo temporal
      if (fs.existsSync(tempFile)) {
        fs.unlinkSync(tempFile);
      }
    }
  } catch (err) {
    console.error('Error:', err.message);
  }
}

// Función principal
async function main() {
  try {
    // Insertar laptops de ejemplo
    await insertLaptops();
    
    console.log('Datos de ejemplo insertados correctamente');
  } catch (error) {
    console.error('Error al insertar datos de ejemplo:', error);
  }
}

// Ejecutar función principal
main();
