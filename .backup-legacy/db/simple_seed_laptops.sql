-- Script simple para insertar datos de ejemplo de laptops
-- Compatible con el esquema actual de la base de datos

-- Insertar laptops de ejemplo
INSERT INTO laptops (model_name, brand_id, release_date, description, image_url, is_available, msrp)
VALUES
  ('ThinkPad X1 Carbon Gen 10', 1, '2022-01-01', 'Laptop empresarial ultraligera con excelente teclado y batería de larga duración', 'https://example.com/thinkpad-x1-carbon.jpg', true, 1499.99),
  ('MacBook Pro 16" M2 Pro', 2, '2023-01-01', 'Potente laptop para profesionales creativos con chip M2 Pro', 'https://example.com/macbook-pro-16.jpg', true, 2499.99),
  ('XPS 13 Plus', 3, '2022-06-01', 'Laptop ultradelgada con diseño minimalista y pantalla InfinityEdge', 'https://example.com/xps-13-plus.jpg', true, 1299.99);

-- Insertar displays para las laptops
INSERT INTO displays (laptop_id, size_inches, resolution_id, panel_type_id, refresh_rate, brightness_nits, color_gamut, hdr_support, is_touchscreen)
VALUES
  (1, 14.0, 3, 2, 60, 400, 'sRGB 100%', false, false),
  (2, 16.0, 4, 3, 120, 1000, 'P3 100%', true, false),
  (3, 13.4, 3, 2, 60, 500, 'sRGB 100%', false, false);

-- Insertar especificaciones físicas para las laptops
INSERT INTO physical_specs (laptop_id, weight_kg, height_mm, width_mm, depth_mm, material, color, has_fingerprint_reader, has_webcam, webcam_resolution, has_backlit_keyboard)
VALUES
  (1, 1.12, 15.36, 315.6, 222.5, 'Carbon Fiber', 'Black', true, true, '1080p', true),
  (2, 2.15, 16.8, 355.7, 248.1, 'Aluminum', 'Silver', true, true, '1080p', true),
  (3, 1.23, 15.28, 295.3, 199.04, 'Aluminum', 'Platinum', true, true, '720p', true);

-- Insertar puntuaciones para las laptops
INSERT INTO laptop_scores (laptop_id, overall_score, llm_performance_score, battery_life_score, build_quality_score, display_score, keyboard_score, performance_score, value_score, thermal_score, noise_score, portability_score)
VALUES
  (1, 85, 70, 90, 95, 80, 95, 75, 80, 85, 90, 95),
  (2, 95, 90, 85, 95, 95, 90, 95, 70, 80, 85, 80),
  (3, 88, 75, 80, 90, 85, 80, 80, 75, 80, 85, 90);

-- Generar compatibilidades con modelos LLM
DO $$
DECLARE
  llm_rec RECORD;
  laptop_rec RECORD;
  base_score INT;
  tokens_per_second INT;
  context_length INT;
  memory_usage NUMERIC(5, 2);
  batch_size INT;
  can_run_offline BOOLEAN;
  qualitative_assessment TEXT;
BEGIN
  -- Iterar sobre todos los modelos LLM
  FOR llm_rec IN SELECT id, name, parameters_billions, requires_gpu FROM llm_models
  LOOP
    -- Iterar sobre todas las laptops
    FOR laptop_rec IN SELECT id, model_name FROM laptops
    LOOP
      -- Generar puntuación aleatoria basada en el tamaño del modelo
      IF llm_rec.parameters_billions <= 7 THEN
        base_score := floor(random() * 30) + 70; -- 70-99
      ELSIF llm_rec.parameters_billions <= 20 THEN
        base_score := floor(random() * 40) + 50; -- 50-89
      ELSE
        base_score := floor(random() * 60) + 30; -- 30-89
      END IF;
      
      -- Ajustar para modelos que requieren GPU
      IF llm_rec.requires_gpu AND random() > 0.7 THEN
        base_score := GREATEST(10, base_score - 30); -- Penalizar algunas laptops que no tienen GPU potente
      END IF;
      
      -- Generar tokens por segundo basado en la puntuación
      tokens_per_second := floor((base_score / 100.0) * (CASE WHEN llm_rec.parameters_billions <= 7 THEN 30 ELSE 15 END) + 1);
      
      -- Generar contexto máximo
      context_length := floor(random() * 3 + 1) * 2048;
      
      -- Generar uso de memoria
      memory_usage := llm_rec.parameters_billions * (random() * 0.3 + 0.2);
      
      -- Generar tamaño de lote recomendado
      IF llm_rec.parameters_billions <= 7 THEN
        batch_size := floor(random() * 8 + 1);
      ELSE
        batch_size := floor(random() * 4 + 1);
      END IF;
      
      -- Generar si puede ejecutarse offline
      can_run_offline := llm_rec.parameters_billions <= 13 AND random() > 0.3;
      
      -- Generar evaluación cualitativa
      IF base_score >= 80 THEN
        qualitative_assessment := 'Esta laptop es excelente para ejecutar ' || llm_rec.name || '. Recomendada para uso intensivo.';
      ELSIF base_score >= 60 THEN
        qualitative_assessment := 'Esta laptop es buena para ejecutar ' || llm_rec.name || '. Adecuada para uso moderado.';
      ELSIF base_score >= 40 THEN
        qualitative_assessment := 'Esta laptop es adecuada para ejecutar ' || llm_rec.name || '. No recomendada para uso intensivo.';
      ELSE
        qualitative_assessment := 'Esta laptop puede tener dificultades para ejecutar ' || llm_rec.name || '. No recomendada.';
      END IF;
      
      -- Insertar en la tabla
      INSERT INTO laptop_llm_compatibility (
        laptop_id, llm_id, estimated_tokens_per_second, max_context_length,
        score, qualitative_assessment, can_run_offline, recommended_batch_size,
        estimated_memory_usage_gb
      ) VALUES (
        laptop_rec.id, llm_rec.id, tokens_per_second, context_length,
        base_score, qualitative_assessment, can_run_offline, batch_size,
        round(memory_usage::numeric, 2)
      );
    END LOOP;
  END LOOP;
END $$;

-- Contar cuántas laptops se insertaron
SELECT COUNT(*) FROM laptops;
