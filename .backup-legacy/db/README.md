# Scripts de Base de Datos para LLM Laptop Lens

Este directorio contiene scripts para gestionar la base de datos de LLM Laptop Lens.

**Versión actual:** 1.2.0 (Última actualización: 6 de Mayo 2025)

## Requisitos

- Node.js 18 o superior
- npm 7 o superior
- Supabase CLI instalado (`npm install -g supabase`)
- Docker instalado y en ejecución

## Configuración de Supabase Local

Este proyecto está configurado para usar una instancia local de Supabase en lugar de la versión en la nube. Esto facilita el desarrollo y las pruebas sin afectar los datos de producción.

### Iniciar Supabase Local

Antes de ejecutar los scripts, asegúrate de que Supabase local esté en ejecución:

```bash
# Iniciar Supabase local
npm run db:start
```

Este comando verificará si Supabase local está en ejecución y lo iniciará si es necesario.

### Configuración del Archivo .env

El archivo `.env` en la raíz del proyecto debe contener las siguientes variables:

```env
# Configuración de Supabase Local
SUPABASE_URL=http://localhost:8000
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiYW5vbiIsImlzcyI6InN1cGFiYXNlIiwiaWF0IjoxNzQ1NDQ5MjAwLCJleHAiOjE5MDMyMTU2MDB9.n6gEnWpGRSp-ljxSVC71-OWHEpVk8RYn4j8sbtFnrqc
SUPABASE_SERVICE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoic2VydmljZV9yb2xlIiwiaXNzIjoic3VwYWJhc2UiLCJpYXQiOjE3NDU0NDkyMDAsImV4cCI6MTkwMzIxNTYwMH0.xB4QeOclazoAN_mGTLsY3CugOsJqotqaZuv0YgAPhuQ
```

> **NOTA**: Para desarrollo local, la clave de servicio es similar a la clave anónima pero con el rol "service_role" en lugar de "anon".

## Esquema de Base de Datos

El esquema de la base de datos está diseñado para almacenar información detallada sobre laptops, sus componentes, compatibilidad con modelos LLM, y precios en diferentes tiendas.

### Estructura General

El esquema se divide en varias categorías principales:

1. **Tablas de Catálogo**: Almacenan información de referencia (manufacturers, brands, etc.)
2. **Tablas de Componentes**: Almacenan especificaciones detalladas (cpus, gpus, etc.)
3. **Tablas de Relación**: Conectan laptops con sus componentes (laptop_cpus, laptop_gpus, etc.)
4. **Tablas de Compatibilidad LLM**: Almacenan información sobre compatibilidad con modelos LLM
5. **Tablas de Precios**: Almacenan información sobre precios y disponibilidad

### Tablas Principales

#### Laptops y Marcas
- `manufacturers`: Fabricantes de laptops y componentes
- `brands`: Marcas de laptops asociadas a fabricantes
- `laptops`: Información básica de laptops

#### Componentes
- `displays`: Especificaciones de pantallas
- `physical_specs`: Especificaciones físicas (peso, dimensiones, etc.)
- `cpus`: Procesadores disponibles
- `gpus`: Tarjetas gráficas disponibles
- `ram_configurations`: Configuraciones de memoria RAM
- `storage_devices`: Dispositivos de almacenamiento

#### Compatibilidad con LLMs
- `llm_models`: Modelos de lenguaje disponibles
- `model_config`: Configuración específica de modelos LLM
- `laptop_llm_compatibility`: Compatibilidad entre laptops y modelos LLM

#### Sistemas Operativos
- `operating_systems`: Sistemas operativos disponibles
- `laptop_os_compatibility`: Compatibilidad entre laptops y sistemas operativos

#### Precios y Listados
- `sources`: Fuentes de datos (tiendas, sitios web)
- `laptop_listings`: Listados de laptops en diferentes fuentes
- `price_history`: Historial de precios
- `scraping_history`: Historial de scraping de fuentes

#### Etiquetas y Puntuaciones
- `tag_categories`: Categorías de etiquetas
- `tags`: Etiquetas para filtrado
- `laptop_tags`: Relación entre laptops y etiquetas
- `laptop_scores`: Puntuaciones de laptops en diferentes categorías

### Vistas

- `laptop_basic_specs`: Vista con especificaciones básicas de laptops
- `laptop_best_prices`: Vista con los mejores precios por laptop
- `llm_compatible_laptops`: Vista de laptops compatibles con modelos LLM

## Uso

Ejecuta los scripts desde el directorio raíz del proyecto:

```bash
# Verificar la configuración
npm run db:check

# Ejecutar la migración
npm run db:migrate

# Insertar datos de ejemplo
npm run db:seed

# Probar la aplicación
npm run test:app
```

Todos los scripts utilizan el archivo `.env` en la raíz del proyecto para la configuración.

## Ejecución de la Migración

Para ejecutar la migración, sigue estos pasos:

1. Asegúrate de tener una copia de seguridad de la base de datos actual
2. Instala las dependencias necesarias:

   ```bash
   # En el directorio raíz del proyecto
   npm install dotenv @supabase/supabase-js
   ```

3. Inicia Supabase local:

   ```bash
   # Desde el directorio raíz
   npm run db:start
   ```

4. Verifica la configuración:

   ```bash
   # Desde el directorio raíz
   npm run db:check
   ```

5. Ejecuta la migración:

   ```bash
   # Desde el directorio raíz
   npm run db:migrate
   ```

6. Sigue las instrucciones en pantalla para confirmar la migración y seleccionar las fases a ejecutar

## Fases de la Migración

La migración se divide en cuatro fases:

1. **Esquema Inicial**: Crea las tablas básicas del nuevo esquema
2. **Compatibilidad LLM**: Crea las tablas relacionadas con la compatibilidad de LLMs
3. **Migración de Datos**: Migra los datos del esquema actual al nuevo esquema
4. **Actualizaciones de Esquema**: Aplica actualizaciones recientes al esquema (model_config, campos adicionales)

Puedes ejecutar las fases individualmente o todas a la vez.

## Consultas de Ejemplo

### Obtener laptops compatibles con un modelo LLM específico

```sql
SELECT l.model_name, b.name AS brand, llc.score AS compatibility_score,
       llc.estimated_tokens_per_second
FROM laptops l
JOIN brands b ON l.brand_id = b.id
JOIN laptop_llm_compatibility llc ON llc.laptop_id = l.id
JOIN llm_models llm ON llc.llm_id = llm.id
WHERE llm.name = 'Llama 2 7B'
ORDER BY llc.score DESC
LIMIT 10;
```

### Obtener laptops con mejor relación precio-rendimiento para LLMs

```sql
SELECT l.model_name, b.name AS brand,
       MIN(ll.price) AS lowest_price,
       lscores.llm_performance_score,
       (lscores.llm_performance_score / MIN(ll.price) * 1000) AS value_score
FROM laptops l
JOIN brands b ON l.brand_id = b.id
JOIN laptop_scores lscores ON lscores.laptop_id = l.id
JOIN laptop_listings ll ON ll.laptop_id = l.id
WHERE ll.in_stock = TRUE
GROUP BY l.id, l.model_name, b.name, lscores.llm_performance_score
ORDER BY value_score DESC
LIMIT 10;
```

### Obtener historial de precios de una laptop específica

```sql
SELECT ph.recorded_at, ph.price, s.name AS source
FROM laptops l
JOIN laptop_listings ll ON ll.laptop_id = l.id
JOIN sources s ON ll.source_id = s.id
JOIN price_history ph ON ph.listing_id = ll.id
WHERE l.model_name = 'ThinkPad X1 Carbon Gen 10'
ORDER BY ph.recorded_at DESC;
```

## Verificación

Después de ejecutar la migración, verifica que los datos se hayan migrado correctamente:

1. Ejecuta el script de prueba:

   ```bash
   npm run test:app
   ```

2. Verifica que las nuevas tablas se hayan creado correctamente
3. Verifica que los datos se hayan migrado correctamente
4. Ejecuta algunas consultas de prueba para verificar que las vistas funcionen correctamente

## Rollback

Si necesitas revertir la migración, puedes ejecutar el siguiente script:

```sql
-- Este script elimina todas las tablas creadas por la migración
DROP TABLE IF EXISTS price_history;
DROP TABLE IF EXISTS laptop_listings;
DROP TABLE IF EXISTS laptop_storage;
DROP TABLE IF EXISTS laptop_ram;
DROP TABLE IF EXISTS laptop_gpus;
DROP TABLE IF EXISTS laptop_cpus;
DROP TABLE IF EXISTS laptop_llm_compatibility;
DROP TABLE IF EXISTS laptop_os_compatibility;
DROP TABLE IF EXISTS laptop_tags;
DROP TABLE IF EXISTS laptop_scores;
DROP TABLE IF EXISTS model_config;
DROP TABLE IF EXISTS storage_devices;
DROP TABLE IF EXISTS ram_configurations;
DROP TABLE IF EXISTS gpus;
DROP TABLE IF EXISTS cpus;
DROP TABLE IF EXISTS physical_specs;
DROP TABLE IF EXISTS displays;
DROP TABLE IF EXISTS laptops;
DROP TABLE IF EXISTS operating_systems;
DROP TABLE IF EXISTS tags;
DROP TABLE IF EXISTS tag_categories;
DROP TABLE IF EXISTS port_types;
DROP TABLE IF EXISTS storage_interfaces;
DROP TABLE IF EXISTS memory_types;
DROP TABLE IF EXISTS cpu_architectures;
DROP TABLE IF EXISTS resolution_types;
DROP TABLE IF EXISTS panel_types;
DROP TABLE IF EXISTS scraping_history;
DROP TABLE IF EXISTS sources;
DROP TABLE IF EXISTS brands;
DROP TABLE IF EXISTS manufacturers;
DROP TABLE IF EXISTS llm_models;
```

> **ADVERTENCIA**: Este script eliminará todas las tablas creadas por la migración, incluyendo los datos migrados. Asegúrate de tener una copia de seguridad antes de ejecutarlo.

## Descripción de los Scripts

- **check-config.mjs**: Verifica que todas las dependencias y configuraciones necesarias estén correctamente instaladas y configuradas.
- **migrate.mjs**: Ejecuta la migración de la base de datos.
- **run_migration.mjs**: Script interno utilizado por `migrate.mjs` para ejecutar las consultas SQL.
- **seed_llm_data.mjs**: Inserta datos de ejemplo en la base de datos.
- **seed_laptops.mjs**: Inserta datos de ejemplo de laptops en la base de datos.
- **test_app.mjs**: Prueba la aplicación después de la migración.
- **start-supabase.mjs**: Inicia la instancia local de Supabase.

## Archivos SQL

- **01_initial_schema.sql**: Crea las tablas básicas del nuevo esquema.
- **02_llm_compatibility_schema.sql**: Crea las tablas relacionadas con la compatibilidad de LLMs.
- **03_migration_script.sql**: Migra los datos del esquema actual al nuevo esquema.
- **04_schema_updates.sql**: Aplica actualizaciones recientes al esquema.
- **05_api_compatibility_updates.sql**: Actualiza el esquema para asegurar compatibilidad con las APIs de Revolico, laptop-ventas.ola.click y Smart-things.ola.click.
- **update_revolico_source.sql**: Actualiza la configuración de Revolico en la tabla de fuentes.
- **update_laptop_ventas_source.sql**: Actualiza la configuración de laptop-ventas.ola.click en la tabla de fuentes.
- **update_smart_things_source.sql**: Actualiza la configuración de Smart-things.ola.click en la tabla de fuentes.
- **update_all_sources.sql**: Script unificado que ejecuta todas las actualizaciones de fuentes.

## Cambios Principales en la Versión Actual

- Adición de la tabla `model_config` para almacenar configuraciones específicas de modelos LLM
- Actualización de la tabla `laptop_listings` con campos adicionales (name, brand, source_url, specs)
- Adición de restricción de esquema JSON para el campo `specs` en `laptop_listings`
- Creación de índices adicionales para mejorar el rendimiento de consultas
- Actualización de la vista `laptop_basic_specs` para incluir información de specs
- Adición de funciones para extraer automáticamente especificaciones de las descripciones
- Actualización de la configuración de fuentes para Revolico, laptop-ventas.ola.click y Smart-things.ola.click
- Adición de campos adicionales en `laptop_listings` para almacenar información específica de cada fuente (currency, original_price, posted_date, seller_info)
- Implementación de un trigger para extraer automáticamente especificaciones de las descripciones

## Solución de Problemas

Si encuentras algún problema al ejecutar los scripts, asegúrate de:

1. Tener Node.js y npm instalados y actualizados.
2. Tener todas las dependencias instaladas (`npm install` en el directorio raíz).
3. Tener un archivo `.env` correctamente configurado en la raíz del proyecto.
4. Tener acceso a la base de datos de Supabase con la clave de servicio correcta.

Si sigues teniendo problemas, ejecuta el script de verificación (`npm run db:check`) para obtener más información sobre el problema.
