#!/usr/bin/env node

/**
 * Script para ejecutar la migración de la base de datos
 *
 * Este script ejecuta la migración de la base de datos utilizando el script run_migration.js.
 * Proporciona una interfaz de línea de comandos para ejecutar la migración.
 */

import { spawn } from 'child_process';
import path from 'path';
import fs from 'fs';
import readline from 'readline';
import { fileURLToPath } from 'url';

// Obtener __dirname en ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuración
const MIGRATION_SCRIPT = path.join(__dirname, 'run_migration.mjs');
const ENV_FILE = path.join(__dirname, '../../.env');

// Verificar que el script de migración existe
if (!fs.existsSync(MIGRATION_SCRIPT)) {
  console.error(`Error: El script de migración no existe en ${MIGRATION_SCRIPT}`);
  process.exit(1);
}

// Verificar que el archivo .env existe
if (!fs.existsSync(ENV_FILE)) {
  console.error('\x1b[31m%s\x1b[0m', `Error: El archivo .env no existe en ${ENV_FILE}`);
  console.error('\x1b[33m%s\x1b[0m', 'Por favor, crea un archivo .env en la raíz del proyecto con las siguientes variables:');
  console.error('SUPABASE_URL=https://uaytxwoswbnioawwxaln.supabase.co');
  console.error('SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...');
  console.error('SUPABASE_SERVICE_KEY=tu-clave-de-servicio-aquí');
  console.error('\nPuedes obtener la clave de servicio desde la consola de Supabase:');
  console.error('1. Ve a https://supabase.com/dashboard');
  console.error('2. Selecciona tu proyecto "laptop-deal"');
  console.error('3. Ve a Project Settings > API');
  console.error('4. Copia la "service_role key" (¡NO la anon key!)');
  process.exit(1);
}

// Crear interfaz de línea de comandos
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Mostrar advertencia
console.log('\x1b[33m%s\x1b[0m', '¡ADVERTENCIA!');
console.log('Este script ejecutará una migración en la base de datos de Supabase.');
console.log('Esta operación puede modificar o eliminar datos existentes.');
console.log('Asegúrate de tener una copia de seguridad de la base de datos antes de continuar.');
console.log();

// Preguntar al usuario si desea continuar
rl.question('¿Deseas continuar con la migración? (s/N): ', (answer) => {
  if (answer.toLowerCase() !== 's') {
    console.log('Migración cancelada.');
    rl.close();
    process.exit(0);
  }

  // Preguntar qué fase de la migración ejecutar
  rl.question('¿Qué fase de la migración deseas ejecutar? (1: Esquema inicial, 2: Compatibilidad LLM, 3: Migración de datos, 4: Actualizaciones de esquema, 0: Todas): ', (phase) => {
    rl.close();

    // Configurar las fases a ejecutar
    let phasesToRun = [];

    switch (phase) {
      case '1':
        phasesToRun = ['01_initial_schema.sql'];
        break;
      case '2':
        phasesToRun = ['02_llm_compatibility_schema.sql'];
        break;
      case '3':
        phasesToRun = ['03_migration_script.sql'];
        break;
      case '4':
        phasesToRun = ['04_schema_updates.sql'];
        break;
      case '0':
      default:
        phasesToRun = ['01_initial_schema.sql', '02_llm_compatibility_schema.sql', '03_migration_script.sql', '04_schema_updates.sql'];
        break;
    }

    // Ejecutar el script de migración
    console.log(`Ejecutando migración (fases: ${phasesToRun.join(', ')})...`);

    const env = { ...process.env, MIGRATION_PHASES: phasesToRun.join(',') };
    // Usar node con la opción --experimental-specifier-resolution=node para permitir importaciones sin extensiones
    const migration = spawn('node', ['--experimental-specifier-resolution=node', MIGRATION_SCRIPT], { env, stdio: 'inherit' });

    migration.on('close', (code) => {
      if (code === 0) {
        console.log('\x1b[32m%s\x1b[0m', '¡Migración completada con éxito!');
      } else {
        console.error('\x1b[31m%s\x1b[0m', `Error: La migración falló con código ${code}`);
      }
    });
  });
});
