-- Actualizar la configuración de laptop-ventas.ola.click en la tabla de fuentes
UPDATE sources
SET selectors = '{
  "apiUrl": "https://laptop-ventas.ola.click/api/products",
  "dataPath": "data",
  "productPath": "products",
  "fields": {
    "id": "id",
    "name": "name",
    "description": "description",
    "visible": "visible",
    "imageUrl": "images[0].image_url",
    "price": "product_variants[0].price",
    "originalPrice": "product_variants[0].original_price",
    "categoryId": "product_category_id"
  },
  "filters": {
    "visible": true,
    "minPrice": 500
  }
}'::jsonb
WHERE name = 'Laptop Ventas';

-- Si no existe la fuente, insertarla
INSERT INTO sources (name, url, api_endpoint, is_active, selectors)
SELECT 'Laptop Ventas', 'https://laptop-ventas.ola.click', 'products', true, '{
  "apiUrl": "https://laptop-ventas.ola.click/api/products",
  "dataPath": "data",
  "productPath": "products",
  "fields": {
    "id": "id",
    "name": "name",
    "description": "description",
    "visible": "visible",
    "imageUrl": "images[0].image_url",
    "price": "product_variants[0].price",
    "originalPrice": "product_variants[0].original_price",
    "categoryId": "product_category_id"
  },
  "filters": {
    "visible": true,
    "minPrice": 500
  }
}'::jsonb
WHERE NOT EXISTS (SELECT 1 FROM sources WHERE name = 'Laptop Ventas');
