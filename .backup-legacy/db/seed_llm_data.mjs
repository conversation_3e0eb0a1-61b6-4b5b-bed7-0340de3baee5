/**
 * Script para insertar datos de ejemplo de modelos LLM y compatibilidad
 *
 * Este script inserta datos de ejemplo de modelos LLM y compatibilidad con laptops
 * en la base de datos de Supabase.
 */

import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// Obtener __dirname en ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configurar dotenv para cargar el archivo .env de la raíz del proyecto
const rootEnvFile = path.join(__dirname, '../../.env');
dotenv.config({ path: rootEnvFile });

// Configuración de Supabase local
const supabaseUrl = process.env.SUPABASE_URL || 'http://localhost:8000';
const supabaseKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseKey) {
  console.error('\x1b[31m%s\x1b[0m', 'Error: SUPABASE_SERVICE_KEY no está definida en el archivo .env');
  console.error('\x1b[33m%s\x1b[0m', 'Para desarrollo local, la clave de servicio debe ser similar a la clave anónima pero con el rol "service_role"');
  console.error('Actualiza el archivo .env con la clave de servicio correcta');
  process.exit(1);
}

// Verificar que Supabase local esté en ejecución
console.log(`Conectando a Supabase local en ${supabaseUrl}...`);

// Ya no necesitamos crear un cliente de Supabase, ya que usaremos psql directamente
console.log(`Configuración de Supabase: ${supabaseUrl}`);
console.log(`Usando clave de servicio: ${supabaseKey.substring(0, 10)}...`);
console.log('Usando psql directamente para ejecutar las consultas SQL');

// Modelos LLM de ejemplo
const llmModels = [
  {
    name: 'Llama 2 7B',
    parameters_billions: 7,
    quantization_bits: 4,
    min_ram_gb: 8,
    min_vram_gb: 4,
    requires_gpu: false,
    description: 'Llama 2 es una colección de modelos de lenguaje preentrenados y ajustados con instrucciones, que van desde 7B a 70B parámetros. Esta es la versión de 7B, que es la más pequeña y rápida, pero menos capaz que las versiones más grandes.',
    model_card_url: 'https://ai.meta.com/llama/'
  },
  {
    name: 'Llama 2 13B',
    parameters_billions: 13,
    quantization_bits: 4,
    min_ram_gb: 16,
    min_vram_gb: 8,
    requires_gpu: true,
    description: 'Llama 2 es una colección de modelos de lenguaje preentrenados y ajustados con instrucciones, que van desde 7B a 70B parámetros. Esta es la versión de 13B, que ofrece un buen equilibrio entre rendimiento y capacidad.',
    model_card_url: 'https://ai.meta.com/llama/'
  },
  {
    name: 'Llama 2 70B',
    parameters_billions: 70,
    quantization_bits: 4,
    min_ram_gb: 32,
    min_vram_gb: 16,
    requires_gpu: true,
    description: 'Llama 2 es una colección de modelos de lenguaje preentrenados y ajustados con instrucciones, que van desde 7B a 70B parámetros. Esta es la versión de 70B, que es la más grande y capaz, pero también la más lenta y con mayores requisitos de hardware.',
    model_card_url: 'https://ai.meta.com/llama/'
  },
  {
    name: 'Mistral 7B',
    parameters_billions: 7,
    quantization_bits: 4,
    min_ram_gb: 8,
    min_vram_gb: 4,
    requires_gpu: false,
    description: 'Mistral 7B es un modelo de lenguaje de 7B parámetros que supera a Llama 2 13B en la mayoría de los benchmarks. Es un modelo muy eficiente que puede ejecutarse en hardware modesto.',
    model_card_url: 'https://mistral.ai/news/announcing-mistral-7b/'
  },
  {
    name: 'Mixtral 8x7B',
    parameters_billions: 47,
    quantization_bits: 4,
    min_ram_gb: 24,
    min_vram_gb: 12,
    requires_gpu: true,
    description: 'Mixtral 8x7B es un modelo Mixture of Experts (MoE) con 8 expertos de 7B parámetros cada uno. Aunque tiene 47B parámetros en total, solo activa 12B en cada forward pass, lo que lo hace más eficiente que modelos densos de tamaño similar.',
    model_card_url: 'https://mistral.ai/news/mixtral-of-experts/'
  },
  {
    name: 'Phi-2',
    parameters_billions: 2.7,
    quantization_bits: 4,
    min_ram_gb: 4,
    min_vram_gb: 2,
    requires_gpu: false,
    description: 'Phi-2 es un modelo de lenguaje de 2.7B parámetros desarrollado por Microsoft. A pesar de su pequeño tamaño, muestra un rendimiento sorprendente en tareas de razonamiento y comprensión de código.',
    model_card_url: 'https://huggingface.co/microsoft/phi-2'
  },
  {
    name: 'Claude 3 Opus',
    parameters_billions: 175,
    quantization_bits: 16,
    min_ram_gb: 64,
    min_vram_gb: 32,
    requires_gpu: true,
    description: 'Claude 3 Opus es el modelo más potente de la familia Claude 3 de Anthropic. Es un modelo de gran escala diseñado para tareas complejas de razonamiento, comprensión y generación de texto.',
    model_card_url: 'https://www.anthropic.com/claude'
  },
  {
    name: 'GPT-4',
    parameters_billions: 220,
    quantization_bits: 16,
    min_ram_gb: 64,
    min_vram_gb: 32,
    requires_gpu: true,
    description: 'GPT-4 es un modelo de lenguaje multimodal desarrollado por OpenAI. Es uno de los modelos más avanzados disponibles, capaz de comprender y generar texto, así como analizar imágenes.',
    model_card_url: 'https://openai.com/gpt-4'
  }
];

// Función para insertar modelos LLM usando psql directamente
async function insertLlmModels() {
  console.log('Insertando modelos LLM usando psql directamente...');

  // Usar child_process.execSync para ejecutar el comando
  const { execSync } = await import('child_process');
  const fs = await import('fs');

  // Crear una consulta SQL para insertar todos los modelos
  let sqlQuery = 'BEGIN;\n';

  for (const model of llmModels) {
    // Escapar las comillas simples en los valores de texto
    const name = model.name.replace(/'/g, "''");
    const description = model.description.replace(/'/g, "''");
    const model_card_url = model.model_card_url.replace(/'/g, "''");

    sqlQuery += `
      INSERT INTO llm_models (
        name, parameters_billions, quantization_bits, min_ram_gb, min_vram_gb,
        requires_gpu, description, model_card_url
      )
      VALUES (
        '${name}', ${model.parameters_billions}, ${model.quantization_bits || 'NULL'},
        ${model.min_ram_gb || 'NULL'}, ${model.min_vram_gb || 'NULL'},
        ${model.requires_gpu === null ? 'NULL' : model.requires_gpu},
        '${description}', '${model_card_url}'
      )
      ON CONFLICT (name) DO UPDATE SET
        parameters_billions = EXCLUDED.parameters_billions,
        quantization_bits = EXCLUDED.quantization_bits,
        min_ram_gb = EXCLUDED.min_ram_gb,
        min_vram_gb = EXCLUDED.min_vram_gb,
        requires_gpu = EXCLUDED.requires_gpu,
        description = EXCLUDED.description,
        model_card_url = EXCLUDED.model_card_url,
        updated_at = CURRENT_TIMESTAMP;
    `;
  }

  sqlQuery += 'COMMIT;';

  try {
    // Guardar la consulta SQL en un archivo temporal
    const tempFile = path.join(__dirname, 'temp_llm_models.sql');
    fs.writeFileSync(tempFile, sqlQuery);

    // Ejecutar la consulta usando psql
    console.log('Ejecutando consulta SQL para insertar modelos LLM...');

    try {
      execSync(`docker cp ${tempFile} supabase-db:/tmp/temp_llm_models.sql && docker exec supabase-db psql -U postgres -d postgres -f /tmp/temp_llm_models.sql`, {
        stdio: 'inherit'
      });
      console.log('Modelos LLM insertados correctamente');
    } catch (execError) {
      console.error('Error al insertar modelos LLM:', execError.message);
    } finally {
      // Eliminar el archivo temporal
      if (fs.existsSync(tempFile)) {
        fs.unlinkSync(tempFile);
      }
    }
  } catch (err) {
    console.error('Error:', err.message);
  }
}

// Función para generar compatibilidades aleatorias usando psql directamente
async function generateRandomCompatibilities() {
  console.log('Generando compatibilidades aleatorias usando psql directamente...');

  // Usar child_process.execSync para ejecutar el comando
  const { execSync } = await import('child_process');
  const fs = await import('fs');

  try {
    // Obtener modelos LLM y laptops directamente con SQL
    const sqlQuery = `
      -- Crear tabla temporal para almacenar compatibilidades
      CREATE TEMP TABLE temp_compatibilities (
        laptop_id INT,
        llm_id INT,
        estimated_tokens_per_second INT,
        max_context_length INT,
        score INT,
        qualitative_assessment TEXT,
        can_run_offline BOOLEAN,
        recommended_batch_size INT,
        estimated_memory_usage_gb NUMERIC(5, 2)
      );

      -- Generar compatibilidades aleatorias
      DO $$
      DECLARE
        llm_rec RECORD;
        laptop_rec RECORD;
        base_score INT;
        tokens_per_second INT;
        context_length INT;
        memory_usage NUMERIC(5, 2);
        batch_size INT;
        can_run_offline BOOLEAN;
        qualitative_assessment TEXT;
      BEGIN
        -- Iterar sobre todos los modelos LLM
        FOR llm_rec IN SELECT id, name, parameters_billions, requires_gpu FROM llm_models
        LOOP
          -- Iterar sobre todas las laptops
          FOR laptop_rec IN SELECT id, model_name FROM laptops
          LOOP
            -- Generar puntuación aleatoria basada en el tamaño del modelo
            IF llm_rec.parameters_billions <= 7 THEN
              base_score := floor(random() * 30) + 70; -- 70-99
            ELSIF llm_rec.parameters_billions <= 20 THEN
              base_score := floor(random() * 40) + 50; -- 50-89
            ELSE
              base_score := floor(random() * 60) + 30; -- 30-89
            END IF;

            -- Ajustar para modelos que requieren GPU
            IF llm_rec.requires_gpu AND random() > 0.7 THEN
              base_score := GREATEST(10, base_score - 30); -- Penalizar algunas laptops que no tienen GPU potente
            END IF;

            -- Generar tokens por segundo basado en la puntuación
            tokens_per_second := floor((base_score / 100.0) * (CASE WHEN llm_rec.parameters_billions <= 7 THEN 30 ELSE 15 END) + 1);

            -- Generar contexto máximo
            context_length := floor(random() * 3 + 1) * 2048;

            -- Generar uso de memoria
            memory_usage := llm_rec.parameters_billions * (random() * 0.3 + 0.2);

            -- Generar tamaño de lote recomendado
            IF llm_rec.parameters_billions <= 7 THEN
              batch_size := floor(random() * 8 + 1);
            ELSE
              batch_size := floor(random() * 4 + 1);
            END IF;

            -- Generar si puede ejecutarse offline
            can_run_offline := llm_rec.parameters_billions <= 13 AND random() > 0.3;

            -- Generar evaluación cualitativa
            IF base_score >= 80 THEN
              qualitative_assessment := 'Esta laptop es excelente para ejecutar ' || llm_rec.name || '. Recomendada para uso intensivo.';
            ELSIF base_score >= 60 THEN
              qualitative_assessment := 'Esta laptop es buena para ejecutar ' || llm_rec.name || '. Recomendada para uso intensivo.';
            ELSIF base_score >= 40 THEN
              qualitative_assessment := 'Esta laptop es adecuada para ejecutar ' || llm_rec.name || '. No recomendada para uso intensivo.';
            ELSE
              qualitative_assessment := 'Esta laptop puede tener dificultades para ejecutar ' || llm_rec.name || '. No recomendada para uso intensivo.';
            END IF;

            -- Insertar en la tabla temporal
            INSERT INTO temp_compatibilities (
              laptop_id, llm_id, estimated_tokens_per_second, max_context_length,
              score, qualitative_assessment, can_run_offline, recommended_batch_size,
              estimated_memory_usage_gb
            ) VALUES (
              laptop_rec.id, llm_rec.id, tokens_per_second, context_length,
              base_score, qualitative_assessment, can_run_offline, batch_size,
              round(memory_usage::numeric, 2)
            );
          END LOOP;
        END LOOP;
      END $$;

      -- Insertar desde la tabla temporal a la tabla real
      INSERT INTO laptop_llm_compatibility (
        laptop_id, llm_id, estimated_tokens_per_second, max_context_length,
        score, qualitative_assessment, can_run_offline, recommended_batch_size,
        estimated_memory_usage_gb
      )
      SELECT
        laptop_id, llm_id, estimated_tokens_per_second, max_context_length,
        score, qualitative_assessment, can_run_offline, recommended_batch_size,
        estimated_memory_usage_gb
      FROM temp_compatibilities
      ON CONFLICT (laptop_id, llm_id) DO UPDATE SET
        estimated_tokens_per_second = EXCLUDED.estimated_tokens_per_second,
        max_context_length = EXCLUDED.max_context_length,
        score = EXCLUDED.score,
        qualitative_assessment = EXCLUDED.qualitative_assessment,
        can_run_offline = EXCLUDED.can_run_offline,
        recommended_batch_size = EXCLUDED.recommended_batch_size,
        estimated_memory_usage_gb = EXCLUDED.estimated_memory_usage_gb;

      -- Contar cuántas compatibilidades se insertaron
      SELECT COUNT(*) FROM temp_compatibilities;

      -- Eliminar la tabla temporal
      DROP TABLE temp_compatibilities;
    `;

    // Guardar la consulta SQL en un archivo temporal
    const tempFile = path.join(__dirname, 'temp_compatibilities.sql');
    fs.writeFileSync(tempFile, sqlQuery);

    // Ejecutar la consulta usando psql
    console.log('Ejecutando consulta SQL para generar compatibilidades...');

    try {
      execSync(`docker cp ${tempFile} supabase-db:/tmp/temp_compatibilities.sql && docker exec supabase-db psql -U postgres -d postgres -f /tmp/temp_compatibilities.sql`, {
        stdio: 'inherit'
      });
      console.log('Compatibilidades generadas correctamente');
    } catch (execError) {
      console.error('Error al generar compatibilidades:', execError.message);
    } finally {
      // Eliminar el archivo temporal
      if (fs.existsSync(tempFile)) {
        fs.unlinkSync(tempFile);
      }
    }
  } catch (err) {
    console.error('Error:', err.message);
  }
}

// Función principal
async function main() {
  try {
    // Insertar modelos LLM
    await insertLlmModels();

    // Generar compatibilidades aleatorias
    await generateRandomCompatibilities();

    console.log('Datos de ejemplo insertados correctamente');
  } catch (error) {
    console.error('Error al insertar datos de ejemplo:', error);
  }
}

// Ejecutar función principal
main();
