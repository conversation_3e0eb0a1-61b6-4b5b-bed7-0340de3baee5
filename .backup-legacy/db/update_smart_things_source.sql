-- Actualizar la configuración de Smart-things.ola.click en la tabla de fuentes
UPDATE sources
SET selectors = '{
  "apiUrl": "https://smart-things.ola.click/api/products",
  "dataPath": "data",
  "productPath": "products",
  "fields": {
    "id": "id",
    "name": "name",
    "description": "description",
    "visible": "visible",
    "imageUrl": "images[0].image_url",
    "price": "product_variants[0].price",
    "originalPrice": "product_variants[0].original_price",
    "categoryId": "product_category_id"
  },
  "filters": {
    "visible": true,
    "minPrice": 500,
    "excludeKeywords": ["garantía", "instalación", "forro", "funda", "soporte", "RAM DDR", "mouse", "bolso", "mesita"]
  }
}'::jsonb
WHERE name = 'Smart Things';

-- Si no existe la fuente, insertarla
INSERT INTO sources (name, url, api_endpoint, is_active, selectors)
SELECT 'Smart Things', 'https://smart-things.ola.click', 'products', true, '{
  "apiUrl": "https://smart-things.ola.click/api/products",
  "dataPath": "data",
  "productPath": "products",
  "fields": {
    "id": "id",
    "name": "name",
    "description": "description",
    "visible": "visible",
    "imageUrl": "images[0].image_url",
    "price": "product_variants[0].price",
    "originalPrice": "product_variants[0].original_price",
    "categoryId": "product_category_id"
  },
  "filters": {
    "visible": true,
    "minPrice": 500,
    "excludeKeywords": ["garantía", "instalación", "forro", "funda", "soporte", "RAM DDR", "mouse", "bolso", "mesita"]
  }
}'::jsonb
WHERE NOT EXISTS (SELECT 1 FROM sources WHERE name = 'Smart Things');
