-- Actualizar la configuración de Revolico en la tabla de fuentes
UPDATE sources
SET selectors = '{
  "apiUrl": "https://graphql-api.revolico.app/",
  "subcategorySlug": "computadoras_laptop",
  "price": {
    "currency": "USD",
    "gte": 800,
    "lte": 2000,
    "configurable": true,
    "defaultRange": {
      "min": 800,
      "max": 2000
    }
  },
  "dateFilter": {
    "enabled": true,
    "field": "updatedOnToOrder",
    "daysBack": 7,
    "configurable": true
  },
  "sort": [
    {
      "order": "desc",
      "field": "updated_on_to_order"
    }
  ],
  "hasImage": true,
  "provinceSlug": "la-habana",
  "pageLength": 20,
  "requiredFields": [
    "id",
    "title",
    "price",
    "currency",
    "permalink",
    "shortDescription",
    "updatedOnToOrder"
  ]
}'::jsonb
WHERE name = 'Revolico';

-- Si no existe la fuente, insertarla
INSERT INTO sources (name, url, api_endpoint, is_active, selectors)
SELECT 'Revolico', 'https://www.revolico.com', 'search', true, '{
  "apiUrl": "https://graphql-api.revolico.app/",
  "subcategorySlug": "computadoras_laptop",
  "price": {
    "currency": "USD",
    "gte": 800,
    "lte": 2000,
    "configurable": true,
    "defaultRange": {
      "min": 800,
      "max": 2000
    }
  },
  "dateFilter": {
    "enabled": true,
    "field": "updatedOnToOrder",
    "daysBack": 7,
    "configurable": true
  },
  "sort": [
    {
      "order": "desc",
      "field": "updated_on_to_order"
    }
  ],
  "hasImage": true,
  "provinceSlug": "la-habana",
  "pageLength": 20,
  "requiredFields": [
    "id",
    "title",
    "price",
    "currency",
    "permalink",
    "shortDescription",
    "updatedOnToOrder"
  ]
}'::jsonb
WHERE NOT EXISTS (SELECT 1 FROM sources WHERE name = 'Revolico');
