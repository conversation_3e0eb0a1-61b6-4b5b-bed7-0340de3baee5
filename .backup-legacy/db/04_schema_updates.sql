-- Actualización del esquema para corregir errores de TypeScript
-- Creación de la tabla model_config para configuración de modelos LLM

-- Crear tabla model_config si no existe
CREATE TABLE IF NOT EXISTS model_config (
    id SERIAL PRIMARY KEY,
    model_id INT REFERENCES llm_models(id),
    name VARCHAR(255) NOT NULL,
    provider VARCHAR(100),
    version VARCHAR(50),
    context_length INTEGER,
    max_tokens INTEGER,
    cost_per_1k_tokens DECIMAL(10,4),
    capabilities TEXT[],
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    endpoint TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Actualizar la tabla laptop_listings
ALTER TABLE laptop_listings
ADD COLUMN IF NOT EXISTS name TEXT,
ADD COLUMN IF NOT EXISTS brand TEXT,
ADD COLUMN IF NOT EXISTS source_url TEXT,
ADD COLUMN IF NOT EXISTS specs JSONB;

-- Agregar restricción para el esquema JSON
ALTER TABLE laptop_listings
ADD CONSTRAINT IF NOT EXISTS specs_schema CHECK (
  specs IS NULL OR specs @> '{
    "gpu": {
      "name": "text",
      "memory": "text" 
    },
    "ram": {
      "size": "text",
      "type": "text",
      "speed": "text"
    },
    "cpu": {
      "name": "text",
      "cores": "integer",
      "threads": "integer", 
      "speed": "text"
    },
    "storage": {
      "size": "text",
      "type": "text"
    },
    "display": {
      "size": "text",
      "resolution": "text",
      "type": "text"
    }
  }'
);

-- Actualizar la tabla sources (equivalente a scraping_source)
ALTER TABLE sources
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT TRUE;

-- Crear índices para mejorar el rendimiento
CREATE INDEX IF NOT EXISTS idx_laptop_listings_specs ON laptop_listings USING GIN (specs);
CREATE INDEX IF NOT EXISTS idx_model_config_model_id ON model_config (model_id);

-- Actualizar la vista laptop_basic_specs para incluir la nueva información
DROP VIEW IF EXISTS laptop_basic_specs;
CREATE VIEW laptop_basic_specs AS
SELECT 
    l.id, l.model_name, b.name AS brand_name, b.manufacturer_id,
    m.name AS manufacturer_name,
    d.size_inches AS screen_size, rt.name AS resolution, 
    pt.name AS panel_type, d.refresh_rate,
    cpu.model AS cpu_model, cpu.cores, cpu.threads, 
    gpu.model AS gpu_model, gpu.vram_gb,
    rc.size_gb AS ram_gb, rc.speed_mhz AS ram_speed,
    mt.name AS memory_type,
    sd.capacity_gb AS storage_capacity, 
    si.name AS storage_interface,
    ps.weight_kg,
    lscores.overall_score,
    lscores.llm_performance_score,
    ll.specs
FROM laptops l
JOIN brands b ON l.brand_id = b.id
JOIN manufacturers m ON b.manufacturer_id = m.id
LEFT JOIN displays d ON d.laptop_id = l.id
LEFT JOIN resolution_types rt ON d.resolution_id = rt.id
LEFT JOIN panel_types pt ON d.panel_type_id = pt.id
LEFT JOIN laptop_cpus lc ON lc.laptop_id = l.id
LEFT JOIN cpus cpu ON lc.cpu_id = cpu.id
LEFT JOIN laptop_gpus lg ON lg.laptop_id = l.id
LEFT JOIN gpus gpu ON lg.gpu_id = gpu.id
LEFT JOIN laptop_ram lr ON lr.laptop_id = l.id
LEFT JOIN ram_configurations rc ON lr.ram_configuration_id = rc.id
LEFT JOIN memory_types mt ON rc.memory_type_id = mt.id
LEFT JOIN laptop_storage lst ON lst.laptop_id = l.id AND lst.is_primary = TRUE
LEFT JOIN storage_devices sd ON lst.storage_id = sd.id
LEFT JOIN storage_interfaces si ON sd.interface_id = si.id
LEFT JOIN physical_specs ps ON ps.laptop_id = l.id
LEFT JOIN laptop_scores lscores ON lscores.laptop_id = l.id
LEFT JOIN (
    SELECT DISTINCT ON (laptop_id) laptop_id, specs
    FROM laptop_listings
    WHERE specs IS NOT NULL
    ORDER BY laptop_id, id DESC
) ll ON ll.laptop_id = l.id;

-- Aplicar el trigger de actualización a la tabla model_config
CREATE TRIGGER update_model_config_modtime
BEFORE UPDATE ON model_config
FOR EACH ROW
EXECUTE PROCEDURE update_modified_column();