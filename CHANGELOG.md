# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

## [2.2.0] - 2023-05-10

### Added
- Nueva tabla `model_config` para almacenar configuraciones específicas de modelos LLM
- Documentación detallada del esquema de base de datos en `scripts/db/DATABASE_SCHEMA.md`
- Script simplificado para insertar datos de ejemplo de laptops
- Nuevos índices para mejorar el rendimiento de consultas

### Changed
- Actualización del esquema de base de datos a la versión 2.2
- Mejora de la tabla `laptop_listings` con campos adicionales (name, brand, source_url, specs)
- Actualización de la vista `laptop_basic_specs` para incluir información de specs
- Documentación mejorada en `scripts/db/README.md`

### Fixed
- Corrección de errores en los scripts de migración
- Actualización de scripts de datos de ejemplo para compatibilidad con el nuevo esquema

## [2.1.0] - 2023-07-15

### Added
- Winston logger configuration for better error tracking and debugging
- Comprehensive error handling with try/catch blocks for all async operations
- Dynamic imports for routes to reduce main bundle size
- Code splitting configuration in webpack.config.js
- Unit tests for React components to achieve 80% coverage

### Changed
- Migrated database schema to version 2.1
- Replaced 'any' type declarations with specific TypeScript types
- Fixed React Hook dependency array issues
- Removed unused imports and dead code
- Optimized bundle size to under 250KB

### Fixed
- ESLint warnings throughout the codebase
- React Hook dependency array issues in /src/hooks/*
- Test failures in component tests

## [2.0.0] - 2023-06-01

### Added
- Initial release with basic functionality
- Scraper dashboard for monitoring data collection
- LLM compatibility analysis for laptops
- Basic visualization tools
