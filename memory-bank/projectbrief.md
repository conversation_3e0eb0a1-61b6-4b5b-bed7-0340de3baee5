# Project Brief: LLM Laptop Lens

## Overview
Aplicación para comparar laptops y su compatibilidad con modelos LLM.

## Objetivos Principales
- Comparar especificaciones técnicas de laptops
- Evaluar compatibilidad con diferentes modelos LLM
- Proporcionar recomendaciones basadas en requisitos

## Alcance
- Frontend: React + TypeScript
- Backend: Supabase
- Scraping: Playwright

## Stakeholders
- <PERSON> (dueño del proyecto)
- Usuarios técnicos que necesitan laptops para AI/ML
