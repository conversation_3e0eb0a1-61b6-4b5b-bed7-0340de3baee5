# System Patterns: LLM Laptop Lens

## Arquitectura
- **Frontend**: Aplicación React con TypeScript
- **Backend**: Supabase para base de datos y autenticación
- **Scraping**: Playwright para recolección de datos

## Patrones Clave
1. **Componentes UI**: Usa componentes reutilizables en src/components/
2. **Gestión de estado**: Context API + hooks personalizados
3. **Datos**: 
   - Repositorios en src/repositories/
   - Servicios en src/services/
4. **Testing**: Jest + React Testing Library

## Flujos Críticos
1. Scraping -> Almacenamiento -> Visualización
2. Filtrado -> Comparación -> Recomendación
3. Autenticación -> Personalización -> Historial

## Diagrama de Componentes
```mermaid
flowchart TD
    A[Frontend React] --> B[Supabase]
    A --> C[Playwright Scraper]
    B --> D[(PostgreSQL)]
    C --> B
