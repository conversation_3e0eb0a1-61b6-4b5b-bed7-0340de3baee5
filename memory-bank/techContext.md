# Tech Context: LLM Laptop Lens

## Tecnologías principales
- **Frontend**: React 18, TypeScript 5, Vite 4
- **UI**: Tailwind CSS 3, Shadcn UI
- **Backend**: Supabase (PostgreSQL)
- **Testing**: Jest, React Testing Library, Playwright
- **Bundler**: Vite
- **Linting**: ESLint, Prettier

## Configuración de desarrollo
- **Node.js**: Versión 20+
- **Gestión de paquetes**: npm
- **Variables de entorno**: Configuradas en .env
- **Scripts principales**:
  - `dev`: Inicia servidor de desarrollo
  - `build`: Construye para producción
  - `test`: Ejecuta pruebas unitarias
  - `lint`: Ejecuta linter

## Dependencias clave
- **Frontend**: react-router-dom, react-icons, react-hook-form
- **Estado**: Zustand
- **Utilidades**: date-fns, zod
- **Visualización**: Chart.js

## Configuración de herramientas
- **ESLint**: Configuración personalizada en .eslintrc
- **Prettier**: Configuración compartida
- **TypeScript**: Configuraciones específicas en tsconfig.json
- **Vite**: Configuración optimizada para React
