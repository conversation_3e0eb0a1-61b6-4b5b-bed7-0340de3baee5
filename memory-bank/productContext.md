# Product Context: LLM Laptop Lens

## Propósito
Proporcionar una herramienta técnica para comparar laptops basada en su compatibilidad con modelos de lenguaje avanzados (LLM).

## Problemas que resuelve
1. Falta de herramientas especializadas para evaluar hardware para ML
2. Dificultad para comparar especificaciones técnicas entre modelos
3. Falta de claridad en requisitos para ejecutar LLMs

## Funcionamiento
1. Usuario ingresa especificaciones técnicas deseadas
2. Sistema compara con base de datos de laptops
3. Sistema evalúa compatibilidad con modelos LLM
4. Sistema genera recomendaciones personalizadas

## Experiencia de usuario
- Interfaz clara y técnica
- Comparación visual de especificaciones
- Sistema de puntuación de compatibilidad
- Filtros avanzados por requisitos técnicos
