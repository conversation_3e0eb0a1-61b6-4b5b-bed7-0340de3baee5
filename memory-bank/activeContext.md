# Active Context: LLM Laptop Lens

## Estado Actual
- **Frontend**: Implementación básica completa
- **Backend**: Integración con Supabase funcionando
- **Scraping**: Sistema de recolección de datos operativo
- **Comparación**: Funcionalidad básica implementada

## Cambios Recientes
1. Implementación de componentes UI reutilizables
2. Integración completa con Supabase
3. Desarrollo de sistema de scraping con Playwright
4. Creación de sistema de puntuación de compatibilidad

## Próximos Pasos
1. Mejorar sistema de recomendaciones
2. Añadir más fuentes de datos para scraping
3. Optimizar cálculo de compatibilidad
4. Implementar autenticación avanzada

## Decisiones Pendientes
1. Selección de modelo de precios para LLMs
2. Estrategia de actualización de datos
3. Enfoque para soporte móvil

## Patrones Importantes
- Uso intensivo de hooks personalizados
- Componentes UI altamente reutilizables
- Separación clara entre lógica y presentación
